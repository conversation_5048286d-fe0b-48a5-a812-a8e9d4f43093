# WebSocket Authentication Guide

## Overview

The Query Intelligence Service WebSocket endpoint now supports JWT-based authentication to ensure secure real-time communication.

## Authentication Method

WebSocket connections authenticate using a JWT token passed as a query parameter.

### Connection URL Format
```
ws://localhost:8002/api/v1/ws/query?token=<JWT_TOKEN>
```

### Example Connection

```javascript
// JavaScript/TypeScript Example
const token = localStorage.getItem('authToken'); // Your JWT token
const wsUrl = `ws://localhost:8002/api/v1/ws/query?token=${token}`;
const ws = new WebSocket(wsUrl);

ws.onopen = () => {
    console.log('WebSocket connected');
    
    // Send a query
    ws.send(JSON.stringify({
        query: "How does authentication work?",
        repository_id: "episteme",
        stream: true
    }));
};

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    
    if (data.type === 'error' && data.code === 'AUTH_FAILED') {
        console.error('Authentication failed');
        // Handle authentication failure
    } else {
        // Handle normal messages
        console.log('Received:', data);
    }
};

ws.onerror = (error) => {
    console.error('WebSocket error:', error);
};

ws.onclose = (event) => {
    if (event.code === 1008) {
        console.error('Connection closed: Policy Violation (Authentication failed)');
    }
};
```

## Authentication Flow

1. **Client obtains JWT token** via the standard authentication endpoint (`POST /api/v1/auth/login`)
2. **Client connects to WebSocket** with token as query parameter
3. **Server validates token** immediately after accepting connection
4. **If valid**: Connection proceeds normally, user_id is automatically added to requests
5. **If invalid**: Server sends error message and closes connection with code 1008

## Error Handling

### Authentication Failure Response
```json
{
    "type": "error",
    "message": "Authentication failed",
    "code": "AUTH_FAILED"
}
```

The connection will be closed with WebSocket close code 1008 (Policy Violation).

## Security Benefits

1. **User Identification**: All queries are associated with the authenticated user
2. **Rate Limiting**: Can apply per-user rate limits for WebSocket connections
3. **Audit Trail**: All WebSocket queries are logged with user identification
4. **Access Control**: Can implement role-based access control for WebSocket endpoints

## Optional Authentication

Authentication is currently optional to maintain backward compatibility. Unauthenticated connections are still accepted but will not have user-specific features.

To make authentication mandatory in production:
1. Remove the `Optional` type from the token parameter
2. Return 403 Forbidden for unauthenticated connections

## Python Client Example

```python
import asyncio
import websockets
import json

async def query_with_auth(token: str):
    uri = f"ws://localhost:8002/api/v1/ws/query?token={token}"
    
    async with websockets.connect(uri) as websocket:
        # Send query
        query_data = {
            "query": "Explain the authentication system",
            "repository_id": "episteme",
            "stream": True
        }
        await websocket.send(json.dumps(query_data))
        
        # Receive responses
        async for message in websocket:
            data = json.loads(message)
            print(f"Received: {data}")
            
            if data.get("type") == "done":
                break

# Run the client
asyncio.run(query_with_auth("your-jwt-token"))
```

## Testing Authentication

### With Valid Token
```bash
# First, get a token
curl -X POST http://localhost:8002/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "testpass"}'

# Use the token for WebSocket connection
wscat -c "ws://localhost:8002/api/v1/ws/query?token=<TOKEN>"
```

### Without Token
```bash
# Connection will work but without user context
wscat -c "ws://localhost:8002/api/v1/ws/query"
```

## Future Enhancements

1. **Token Refresh**: Implement token refresh mechanism for long-lived connections
2. **Connection Limits**: Limit concurrent WebSocket connections per user
3. **Message Encryption**: Add end-to-end encryption for sensitive queries
4. **Heartbeat**: Implement ping/pong for connection health monitoring