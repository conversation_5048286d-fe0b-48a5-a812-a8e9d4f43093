# Multi-Language Query Support

## Overview

The Query Intelligence Service now supports queries in 15+ languages, automatically detecting and translating them to ensure accurate code search results across language barriers.

## Supported Languages

- **English** (en) - Native support, no translation needed
- **Spanish** (es) - español
- **French** (fr) - français  
- **German** (de) - Deutsch
- **Italian** (it) - italiano
- **Portuguese** (pt) - português
- **Dutch** (nl) - Nederlands
- **Polish** (pl) - polski
- **Russian** (ru) - русский
- **Japanese** (ja) - 日本語
- **Korean** (ko) - 한국어
- **Chinese Simplified** (zh-cn) - 简体中文
- **Chinese Traditional** (zh-tw) - 繁體中文
- **Arabic** (ar) - العربية
- **Hindi** (hi) - हिन्दी

## How It Works

### 1. Automatic Language Detection
When you submit a query, the service automatically detects the language using advanced language detection algorithms.

### 2. Intelligent Translation
- Queries in non-English languages are translated to English
- Programming terms and code identifiers are preserved during translation
- Function names, variable names, and quoted strings remain unchanged

### 3. Enhanced Search
The translated query is used to search the codebase while maintaining the context and intent of the original query.

## API Usage

### Standard Query (Any Language)
```bash
curl -X POST http://localhost:8002/api/v1/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "¿Cómo funciona la autenticación?",
    "repository_id": "episteme"
  }'
```

### Response Includes Language Metadata
```json
{
  "answer": "The authentication system works by...",
  "confidence": 0.85,
  "metadata": {
    "query_language": "es",
    "was_translated": true,
    "original_query": "¿Cómo funciona la autenticación?"
  }
}
```

## Examples

### Spanish Query
```json
{
  "query": "Buscar la función que valida tokens JWT",
  "repository_id": "episteme"
}
```

### French Query
```json
{
  "query": "Expliquer comment fonctionne le système de cache",
  "repository_id": "episteme"
}
```

### Japanese Query
```json
{
  "query": "認証システムのエラー処理を表示",
  "repository_id": "episteme"
}
```

### Chinese Query
```json
{
  "query": "查找处理用户登录的代码",
  "repository_id": "episteme"
}
```

## Code Term Preservation

The translation system preserves:
- **Function names**: `validateUser()` remains unchanged
- **Variable names**: `auth_token` is not translated
- **Class names**: `UserAuthentication` stays as-is
- **Quoted strings**: `"error message"` is preserved
- **Code patterns**: camelCase and snake_case identifiers

## Language-Specific Tips

### For Spanish Speakers
- Use "función" to search for functions
- Use "clase" to search for classes  
- Use "método" for methods
- Example: "Buscar función de validación de email"

### For French Speakers
- Use "fonction" for functions
- Use "classe" for classes
- Use "méthode" for methods
- Example: "Trouver la fonction qui gère les erreurs"

### For German Speakers
- Use "Funktion" for functions
- Use "Klasse" for classes
- Use "Methode" for methods
- Example: "Zeige die Implementierung der Authentifizierung"

### For Japanese Speakers
- Use "関数" for functions
- Use "クラス" for classes
- Use "メソッド" for methods
- Example: "ユーザー認証の関数を検索"

### For Chinese Speakers
- Use "函数" for functions
- Use "类" for classes
- Use "方法" for methods
- Example: "搜索处理错误的函数"

## Best Practices

### 1. Natural Language Queries
Write queries in your native language as naturally as possible. The system will handle the translation.

### 2. Include Code Terms
When referring to specific function or variable names, include them as-is in your query.

**Good**: "¿Dónde está definida la función validateJWT?"  
**Good**: "validateJWT 関数の実装を表示"

### 3. Use Programming Keywords
Use programming terms in your language for better detection:
- Spanish: error, función, clase, variable
- French: erreur, fonction, classe, variable
- German: Fehler, Funktion, Klasse, Variable
- Japanese: エラー, 関数, クラス, 変数
- Chinese: 错误, 函数, 类, 变量

## Technical Implementation

### Language Detection
- Uses `langdetect` library for accurate language identification
- Confidence threshold of 0.7 for reliable detection
- Fallback to English if detection fails

### Translation
- Google Translate API for high-quality translations
- Code term preservation algorithm
- Asynchronous translation for performance

### Performance
- Language detection: ~5ms
- Translation: ~50-200ms (cached)
- No impact on English queries
- Translated queries are cached

## Limitations

1. **Technical Jargon**: Some domain-specific technical terms might not translate perfectly
2. **Context**: Very short queries might have ambiguous language detection
3. **Mixed Languages**: Queries mixing multiple languages default to the dominant language

## Future Enhancements

1. **Response Translation**: Return answers in the query's original language
2. **Custom Terminology**: Domain-specific translation dictionaries
3. **Voice Queries**: Multi-language voice input support
4. **Regional Dialects**: Support for regional language variations