[tool.poetry]
name = "query-intelligence"
version = "0.1.0"
description = "Query Intelligence Service"
authors = ["Your Name <<EMAIL>>"]
packages = [{include = "query_intelligence", from = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.115.14"
uvicorn = {extras = ["standard"], version = "^0.35.0"}
google-genai = "^0.5.0"
google-cloud-secret-manager = "^2.20.0"
google-cloud-kms = "^2.22.0"
google-auth = "^2.30.0"
redis = "^6.2.0"
langchain = "^0.3.26"
pydantic = "^2.11.7"
pydantic-settings = "^2.1.0"
numpy = "^2.3.1"
sentence-transformers = "^5.0.0"
websockets = "^14.1"
httpx = "^0.28.1"
aiofiles = "^24.1.0"
prometheus-client = "^0.22.1"
google-cloud-pubsub = "^2.30.0"
structlog = "^24.1.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
pinecone-client = "^3.2.2"
prometheus-fastapi-instrumentator = "^6.1.0"
langdetect = "^1.0.9"
googletrans = "^4.0.0-rc1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.21.0"
black = "^24.0.0"
ruff = "^0.1.0"
mypy = "^1.0.0"
httpx = "^0.28.1"
pytest-cov = "^6.2.1"
types-python-jose = "^3.5.0.20250531"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
pythonpath = "src"