import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch
from query_intelligence.main import app  # Assuming the main app is in main.py
from query_intelligence.config.settings import get_settings

# Override settings for testing
settings = get_settings()
settings.JWT_SECRET_KEY = "test_secret"


@pytest.fixture
def client():
    """Test client for the FastAPI app"""
    return TestClient(app)


@pytest.fixture
def mock_redis_client():
    """Mock Redis client"""
    mock = AsyncMock()
    mock.get.return_value = "0"
    mock.scard.return_value = 0
    mock.zrevrange.return_value = []
    mock.hgetall.return_value = {}
    mock.info.return_value = {}
    return mock


@pytest.fixture(autouse=True)
def override_dependencies(mock_redis_client):
    """Override dependencies for all tests"""
    with patch(
        "query_intelligence.api.admin.get_redis_client",
        return_value=mock_redis_client,
    ), patch(
        "query_intelligence.api.admin.get_cache_manager",
        return_value=AsyncMock(),
    ), patch(
        "query_intelligence.middleware.auth.jwt.decode",
        return_value={"sub": "admin", "exp": 9999999999},
    ):
        yield


def get_admin_headers():
    """Helper to get admin headers"""
    # This is a simplified way to get admin headers.
    # In a real application, you would generate a valid JWT token.
    return {"Authorization": "Bearer admin_token"}


class TestAdminAPI:
    """Test suite for the Admin API"""

    def test_get_metrics_unauthorized(self, client):
        """Test that metrics endpoint requires authentication"""
        response = client.get("/admin/metrics")
        assert response.status_code == 401

    def test_get_metrics_success(self, client, mock_redis_client):
        """Test successful retrieval of system metrics"""
        mock_redis_client.get.side_effect = [
            "100",  # total_queries
            "80",  # cache_hits
            "20",  # cache_misses
            "12345.0",  # total_response_time
            "5",  # error_count
            "10",  # qpm_count
        ]
        mock_redis_client.scard.return_value = 10
        response = client.get("/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["total_queries"] == 100
        assert data["cache_hit_rate"] == 0.8

    def test_get_health_success(self, client, mock_redis_client):
        """Test successful retrieval of service health"""
        mock_redis_client.ping.return_value = True
        mock_redis_client.get.side_effect = ["healthy", "healthy", "healthy"]
        with patch(
            "query_intelligence.api.admin.get_circuit_breaker_status",
            return_value={"test_breaker": {"state": "closed"}},
        ):
            response = client.get("/admin/health", headers=get_admin_headers())
            assert response.status_code == 200
            data = response.json()
            assert data["redis"] == "healthy"
            assert data["circuit_breakers"]["test_breaker"] == "closed"

    def test_get_query_stats_success(self, client, mock_redis_client):
        """Test successful retrieval of query statistics"""
        mock_redis_client.zrevrange.return_value = [("test query", 10)]
        mock_redis_client.hgetall.return_value = {"en": "5", "es": "5"}
        response = client.get("/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert len(data["top_queries"]) == 1
        assert data["language_distribution"]["en"] == 5

    def test_clear_cache_success(self, client):
        """Test successful cache clearing"""
        response = client.post(
            "/admin/cache/clear?cache_type=all", headers=get_admin_headers()
        )
        assert response.status_code == 200
        assert response.json() == {"status": "success", "cleared": "all"}
