"""
Unit tests for SecretManagerService
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import os

from query_intelligence.services.secret_manager import (
    SecretManagerService,
    get_secret_manager_service,
    get_jwt_secret,
    get_pinecone_api_key,
    get_google_api_key,
)


class TestSecretManagerService:
    """Test cases for SecretManagerService"""

    def setup_method(self):
        """Set up test fixtures"""
        # Clear any cached instances
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None

    def test_init_with_default_settings(self):
        """Test initialization with default test settings"""
        service = SecretManagerService()
        
        # In test environment, USE_SECRET_MANAGER should be False
        assert service.use_secret_manager is False
        assert service._client is None
        # project_id might be None in test environment
        assert hasattr(service, 'project_id')

    def test_get_secret_with_secret_manager_disabled(self):
        """Test get_secret when SECRET_MANAGER is disabled"""
        service = SecretManagerService()
        
        result = service.get_secret("test-secret")
        
        assert result is None

    def test_get_secret_or_env_with_env_var(self):
        """Test get_secret_or_env with environment variable"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {'TEST_ENV_VAR': 'env-value'}):
            result = service.get_secret_or_env("nonexistent-secret", "TEST_ENV_VAR", "default-value")
            assert result == "env-value"

    def test_get_secret_or_env_use_default(self):
        """Test get_secret_or_env using default value"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("nonexistent-secret", "NONEXISTENT_ENV", "default-value")
            assert result == "default-value"

    def test_get_secret_or_env_no_default(self):
        """Test get_secret_or_env with no default value"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("nonexistent-secret", "NONEXISTENT_ENV")
            assert result is None

    def test_validate_production_secrets_non_production(self):
        """Test validate_production_secrets in non-production environment"""
        service = SecretManagerService()
        
        # In test environment, this should indicate non-production
        result = service.validate_production_secrets()
        
        # Should be valid in non-production
        assert result["valid"] is True
        assert "Not in production environment" in result["warnings"]

    def test_refresh_cache(self):
        """Test refresh_cache method exists and can be called"""
        service = SecretManagerService()
        
        # Should not raise any exceptions
        service.refresh_cache()
        
        # Method should exist
        assert hasattr(service, 'refresh_cache')

    def test_get_secret_manager_service_singleton(self):
        """Test that get_secret_manager_service returns singleton instance"""
        service1 = get_secret_manager_service()
        service2 = get_secret_manager_service()
        
        assert service1 is service2

    def test_get_jwt_secret_from_env(self):
        """Test get_jwt_secret gets value from environment (test setup)"""
        result = get_jwt_secret()
        # Should get the test value from conftest.py
        assert result == "test-secret-key"

    def test_get_pinecone_api_key_from_env(self):
        """Test get_pinecone_api_key gets value from environment (test setup)"""
        result = get_pinecone_api_key()
        # Should get the test value from conftest.py
        assert result == "test-pinecone-key"

    def test_get_google_api_key_from_env(self):
        """Test get_google_api_key gets value from environment (test setup)"""
        result = get_google_api_key()
        # Should get the test value from conftest.py
        assert result == "test-api-key"

    def test_get_pinecone_api_key_not_found(self):
        """Test get_pinecone_api_key when key is not found"""
        # Clear the cached instance to avoid conflicts
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None
        
        with patch.dict(os.environ, {}, clear=True):
            result = get_pinecone_api_key()
            assert result is None

    def test_get_google_api_key_not_found(self):
        """Test get_google_api_key when key is not found"""
        # Clear the cached instance to avoid conflicts
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None
        
        with patch.dict(os.environ, {}, clear=True):
            result = get_google_api_key()
            assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_enabled_with_successful_client(self, mock_client):
        """Test secret manager when enabled with successful client creation"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock the response
        mock_response = Mock()
        mock_response.payload.data.decode.return_value = "secret-value"
        mock_client_instance.access_secret_version.return_value = mock_response
        
        # Create service with mocked settings
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result == "secret-value"
                mock_client_instance.access_secret_version.assert_called_once()

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_exception_handling(self, mock_client):
        """Test secret manager exception handling"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock exception
        mock_client_instance.access_secret_version.side_effect = Exception("Secret not found")
        
        # Create service with mocked settings
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("nonexistent-secret")
                assert result is None

    def test_secret_manager_client_not_initialized(self):
        """Test behavior when secret manager client is not initialized"""
        service = SecretManagerService()
        
        # Simulate client not being initialized
        original_client = service._client
        service._client = None
        
        result = service.get_secret("test-secret")
        assert result is None
        
        # Restore original client
        service._client = original_client

    def test_validate_production_secrets_structure(self):
        """Test that validate_production_secrets returns proper structure"""
        service = SecretManagerService()
        
        result = service.validate_production_secrets()
        
        # Should have proper structure
        assert "valid" in result
        assert "errors" in result
        assert "warnings" in result
        assert isinstance(result["valid"], bool)
        assert isinstance(result["errors"], list)
        assert isinstance(result["warnings"], list)

    def test_secret_manager_production_validation_with_env_vars(self):
        """Test production validation with environment variables"""
        service = SecretManagerService()
        
        # Test with production environment
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'proper-secret-value',
            'PINECONE_API_KEY': 'proper-pinecone-value'
        }):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should validate based on environment setup
            assert isinstance(result["valid"], bool)

    def test_secret_manager_production_validation_missing_secrets(self):
        """Test production validation with missing secrets"""
        service = SecretManagerService()
        
        # Test with production environment and no secrets
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should have errors for missing secrets
            assert isinstance(result["errors"], list)

    def test_secret_manager_production_validation_default_values(self):
        """Test production validation with default values"""
        service = SecretManagerService()
        
        # Test with production environment and default values
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER',
            'PINECONE_API_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER'
        }):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should have errors for default values
            assert isinstance(result["errors"], list)

    def test_get_jwt_secret_production_validation(self):
        """Test JWT secret production validation"""
        # Test with production environment and default value
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            # Need to reload settings to pick up the environment change
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                # Should raise ValueError in production with default value
                with pytest.raises(ValueError, match="JWT secret key must be set in production"):
                    get_jwt_secret()

    def test_get_jwt_secret_non_production_default(self):
        """Test JWT secret in non-production with default"""
        # Test with development environment and no JWT secret
        with patch.dict(os.environ, {'ENVIRONMENT': 'development'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            result = get_jwt_secret()
            
            # Should return default value
            assert result == "CHANGE-THIS-USE-SECRET-MANAGER"

    def test_secret_manager_service_attributes(self):
        """Test that SecretManagerService has expected attributes"""
        service = SecretManagerService()
        
        assert hasattr(service, 'use_secret_manager')
        assert hasattr(service, 'project_id')
        assert hasattr(service, '_client')
        assert hasattr(service, 'get_secret')
        assert hasattr(service, 'get_secret_or_env')
        assert hasattr(service, 'validate_production_secrets')
        assert hasattr(service, 'refresh_cache')

    def test_secret_manager_service_methods_callable(self):
        """Test that SecretManagerService methods are callable"""
        service = SecretManagerService()
        
        # These should not raise exceptions
        service.get_secret("test")
        service.get_secret_or_env("test", "TEST_ENV", "default")
        service.validate_production_secrets()
        service.refresh_cache()

    def test_global_helper_functions_callable(self):
        """Test that global helper functions are callable"""
        # These should not raise exceptions
        get_secret_manager_service()
        get_jwt_secret()
        get_pinecone_api_key()
        get_google_api_key()

    def test_secret_manager_with_version_parameter(self):
        """Test secret retrieval with version parameter"""
        service = SecretManagerService()
        
        # Should handle version parameter gracefully
        result = service.get_secret("test-secret", version="2")
        
        # With SECRET_MANAGER disabled, should return None
        assert result is None

    def test_secret_caching_behavior(self):
        """Test that secret caching works properly"""
        service = SecretManagerService()
        
        # Test that get_secret has cache_clear method (from lru_cache)
        assert hasattr(service.get_secret, 'cache_clear')
        
        # Test that refresh_cache calls cache_clear
        service.refresh_cache()  # Should not raise exceptions