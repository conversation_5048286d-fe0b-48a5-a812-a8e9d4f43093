# Remaining Production Issues - Analysis Engine

**Last Updated**: July 10, 2025  
**Priority**: Low (Non-blocking)

## Issue Summary

All issues have workarounds or fallback mechanisms. The service is production-ready despite these minor issues.

## 1. Validation Error Response Format

**Priority**: Low  
**Impact**: API consistency  
**Status**: Non-blocking

### Issue:
Request validation errors return plain text instead of the structured JSON error format used elsewhere.

### Example:
```
Failed to deserialize the JSON body into the target type: missing field `repository_url` at line 1 column 2
```

### Expected:
```json
{
  "error_id": "error_xxx",
  "message": "Failed to deserialize the JSON body",
  "details": "missing field `repository_url` at line 1 column 2"
}
```

### Workaround:
Clients can detect 422 status code and parse the text error.

### Fix:
Update the request validation middleware to return structured errors.

---

## 2. AI Service IAM Permissions

**Priority**: Medium  
**Impact**: Feature completeness  
**Status**: Gracefully degraded

### Issue:
Vertex AI services return 403 Permission Denied due to missing IAM roles.

### Required Permissions:
- `roles/aiplatform.user`
- `roles/serviceusage.serviceUsageConsumer`

### Current Behavior:
- All AI features fall back to traditional algorithms
- No service disruption
- Performance remains excellent

### Fix Commands:
```bash
SERVICE_ACCOUNT_EMAIL="<EMAIL>"

gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/serviceusage.serviceUsageConsumer"
```

---

## 3. GCP Service Dependencies

**Priority**: Low  
**Impact**: Observability  
**Status**: Using local fallbacks

### Issue:
The following services show as "unhealthy" in /ready endpoint:
- Cloud Storage (bucket doesn't exist)
- Pub/Sub (topics don't exist)
- Spanner (not configured)

### Current Behavior:
- Results stored in memory
- No event publishing
- No persistent storage

### Workaround:
Service fully functional without these dependencies.

### Fix:
1. Create GCS bucket: `ccl-analysis-vibe-match-463114`
2. Create Pub/Sub topics: `analysis-events`, `analysis-progress`, `pattern-detected`
3. Configure Spanner instance (optional)

---

## 4. Missing Tree-Sitter Parsers

**Priority**: Low  
**Impact**: Language support  
**Status**: Core languages working

### Issue:
Some extended language parsers show as "not available":
- Markdown
- Swift
- Objective-C
- R
- Julia
- Haskell
- Scala
- Elixir
- Zig
- Nix

### Current Behavior:
- 9 core languages fully supported
- Extended languages use basic text analysis

### Workaround:
Fallback to regex-based parsing for unsupported languages.

---

## Production Deployment Decision

**None of these issues block production deployment.**

All issues have:
- ✅ Fallback mechanisms
- ✅ Graceful degradation
- ✅ No impact on core functionality
- ✅ Can be fixed post-deployment

**Recommendation**: Deploy to production and address issues based on actual usage patterns.