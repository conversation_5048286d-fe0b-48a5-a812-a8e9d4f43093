# ============================================
# Multi-stage Dockerfile for Analysis Engine
# Optimized for Cloud Run deployment
# ============================================

# Stage 1: Dependencies caching
FROM rust:1.70-slim AS dependencies

WORKDIR /usr/src/app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy only Cargo files for dependency caching
COPY Cargo.toml Cargo.lock ./

# Create dummy main.rs to build dependencies
RUN mkdir src && \
    echo "fn main() {}" > src/main.rs && \
    cargo build --release && \
    rm -rf src

# Stage 2: Builder
FROM rust:1.70-slim AS builder

WORKDIR /usr/src/app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy cached dependencies from previous stage
COPY --from=dependencies /usr/src/app/target target
COPY --from=dependencies /usr/local/cargo /usr/local/cargo

# Copy source code
COPY Cargo.toml Cargo.lock ./
COPY src ./src
COPY benches ./benches

# Build with aggressive optimizations
ENV CARGO_BUILD_JOBS=8
RUN cargo build --release \
    && strip /usr/src/app/target/release/analysis-engine

# Stage 3: Distroless runtime
FROM gcr.io/distroless/cc-debian12:nonroot

# Copy SSL certificates
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary
COPY --from=builder /usr/src/app/target/release/analysis-engine /app/analysis-engine

# Set non-root user (distroless already runs as nonroot by default)
USER nonroot

# Cloud Run will set PORT env var
ENV RUST_LOG=info \
    RUST_BACKTRACE=1 \
    ANALYSIS_ENGINE_ADDR=0.0.0.0:8080

# The distroless image doesn't have a shell, so we can't use HEALTHCHECK
# Cloud Run will use its own health checks

# Expose port (Cloud Run will override this with PORT env var)
EXPOSE 8080

# Run the binary
ENTRYPOINT ["/app/analysis-engine"]