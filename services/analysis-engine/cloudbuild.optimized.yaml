steps:
  # Step 1: Build the container image with caching
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '--cache-from'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:latest'
      - '-t'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}'
      - '-t'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:latest'
      - '-f'
      - 'services/analysis-engine/Dockerfile.optimized'
      - '--build-arg'
      - 'BUILDKIT_INLINE_CACHE=1'
      - 'services/analysis-engine'
    env:
      - 'DOCKER_BUILDKIT=1'

  # Step 2: Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    args:
      - 'push'
      - '--all-tags'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}'
    waitFor: ['build-image']

  # Step 3: Run security scanning on the image
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'scan-image'
    args:
      - 'container'
      - 'images'
      - 'scan'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}'
    waitFor: ['push-image']

  # Step 4: Deploy to Cloud Run with traffic management
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-service'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - 'managed'
      - '--port'
      - '8080'
      - '--memory'
      - '${_MEMORY}'
      - '--cpu'
      - '${_CPU}'
      - '--timeout'
      - '300s'
      - '--concurrency'
      - '${_CONCURRENCY}'
      - '--min-instances'
      - '${_MIN_INSTANCES}'
      - '--max-instances'
      - '${_MAX_INSTANCES}'
      - '--cpu-boost'
      - '--no-traffic'
      - '--tag'
      - 'sha-${SHORT_SHA}'
      - '--set-env-vars'
      - 'ENVIRONMENT=${_ENVIRONMENT},RUST_LOG=${_RUST_LOG},GCP_PROJECT_ID=${PROJECT_ID},GCP_REGION=${_REGION},SPANNER_INSTANCE=${_SPANNER_INSTANCE},SPANNER_DATABASE=${_SPANNER_DATABASE},STORAGE_BUCKET=${_STORAGE_BUCKET},STORAGE_BUCKET_NAME=${_STORAGE_BUCKET_NAME},PUBSUB_TOPIC=${_PUBSUB_TOPIC},VERTEX_AI_LOCATION=${_REGION},ENABLE_AUTH=${_ENABLE_AUTH},CORS_ORIGINS=${_CORS_ORIGINS},MAX_CONCURRENT_ANALYSES=${_MAX_CONCURRENT_ANALYSES},MAX_REPOSITORY_SIZE_GB=${_MAX_REPOSITORY_SIZE_GB},ANALYSIS_TIMEOUT_SECONDS=${_ANALYSIS_TIMEOUT_SECONDS},MAX_FILE_SIZE_MB=${_MAX_FILE_SIZE_MB},TEMP_DIR=/tmp/ccl-analysis,ENABLE_TRACING=${_ENABLE_TRACING},ENABLE_METRICS=${_ENABLE_METRICS}'
      - '--set-secrets'
      - 'JWT_SECRET=analysis-engine-jwt-secret:latest,GEMINI_API_KEY=gemini-api-key:latest'
      - '--service-account'
      - '${_SERVICE_ACCOUNT}'
      - '--vpc-connector'
      - '${_VPC_CONNECTOR}'
      - '--vpc-egress'
      - 'all-traffic'
      - '--allow-unauthenticated'
    waitFor: ['scan-image']

  # Step 5: Gradual traffic migration (canary deployment)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'migrate-traffic'
    entrypoint: bash
    args:
      - '-c'
      - |
        # Migrate 10% traffic to new version
        gcloud run services update-traffic ${_SERVICE_NAME} \
          --region ${_REGION} \
          --to-tags sha-${SHORT_SHA}=10

        # Wait for stability
        sleep 60

        # Check service health
        SERVICE_URL=$(gcloud run services describe ${_SERVICE_NAME} --region ${_REGION} --format 'value(status.url)')
        HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" $${SERVICE_URL}/health)
        
        if [ "$$HEALTH_CHECK" -eq "200" ]; then
          echo "Health check passed, continuing rollout"
          
          # Increase to 50%
          gcloud run services update-traffic ${_SERVICE_NAME} \
            --region ${_REGION} \
            --to-tags sha-${SHORT_SHA}=50
          
          sleep 60
          
          # Full rollout
          gcloud run services update-traffic ${_SERVICE_NAME} \
            --region ${_REGION} \
            --to-latest
        else
          echo "Health check failed, rolling back"
          gcloud run services update-traffic ${_SERVICE_NAME} \
            --region ${_REGION} \
            --to-tags sha-${SHORT_SHA}=0
          exit 1
        fi
    waitFor: ['deploy-service']

# Substitutions for different environments
substitutions:
  _REGION: us-central1
  _REPOSITORY: ccl-services
  _SERVICE_NAME: analysis-engine
  _ENVIRONMENT: production
  _RUST_LOG: info
  _MEMORY: 4Gi
  _CPU: '4'
  _CONCURRENCY: '1000'
  _MIN_INSTANCES: '1'
  _MAX_INSTANCES: '1000'
  _SPANNER_INSTANCE: ccl-production
  _SPANNER_DATABASE: ccl-main
  _STORAGE_BUCKET: ccl-analysis-artifacts
  _STORAGE_BUCKET_NAME: ccl-analysis-${PROJECT_ID}
  _PUBSUB_TOPIC: analysis-events
  _SERVICE_ACCOUNT: analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com
  _VPC_CONNECTOR: projects/${PROJECT_ID}/locations/${_REGION}/connectors/ccl-vpc-connector
  _ENABLE_AUTH: 'true'
  _CORS_ORIGINS: '*'
  _MAX_CONCURRENT_ANALYSES: '50'
  _MAX_REPOSITORY_SIZE_GB: '10'
  _ANALYSIS_TIMEOUT_SECONDS: '300'
  _MAX_FILE_SIZE_MB: '50'
  _ENABLE_TRACING: 'true'
  _ENABLE_METRICS: 'true'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  substitutionOption: 'ALLOW_LOOSE'
  dynamicSubstitutions: true

timeout: 1800s