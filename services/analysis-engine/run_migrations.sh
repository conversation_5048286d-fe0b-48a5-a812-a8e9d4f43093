#!/bin/bash

# Production-ready migration execution script for Analysis Engine
# This script manages database migrations for the CCL Analysis Engine Spanner database

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-ccl-development}"
INSTANCE_ID="${INSTANCE_ID:-ccl-instance}"
DATABASE_ID="${DATABASE_ID:-ccl_main}"
MIGRATIONS_DIR="${MIGRATIONS_DIR:-migrations}"
LOG_FILE="${LOG_FILE:-migration_$(date +%Y%m%d_%H%M%S).log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

# Validation functions
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found. Please run 'gcloud auth login'."
        exit 1
    fi
    
    # Check if project exists
    if ! gcloud projects describe "$PROJECT_ID" &> /dev/null; then
        log_error "Project $PROJECT_ID does not exist or is not accessible."
        exit 1
    fi
    
    # Check if Spanner instance exists
    if ! gcloud spanner instances describe "$INSTANCE_ID" --project="$PROJECT_ID" &> /dev/null; then
        log_error "Spanner instance $INSTANCE_ID does not exist in project $PROJECT_ID."
        exit 1
    fi
    
    # Check if database exists
    if ! gcloud spanner databases describe "$DATABASE_ID" --instance="$INSTANCE_ID" --project="$PROJECT_ID" &> /dev/null; then
        log_error "Database $DATABASE_ID does not exist in instance $INSTANCE_ID."
        exit 1
    fi
    
    # Check if migrations directory exists
    if [[ ! -d "$MIGRATIONS_DIR" ]]; then
        log_error "Migrations directory $MIGRATIONS_DIR does not exist."
        exit 1
    fi
    
    log_success "Prerequisites validated successfully."
}

# Get list of applied migrations
get_applied_migrations() {
    log_info "Getting list of applied migrations..."
    
    # Try to query the schema_migrations table
    if ! gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="SELECT version FROM schema_migrations ORDER BY version" \
        --format="value(version)" 2>/dev/null; then
        log_warn "Schema migrations table does not exist. This might be a fresh database."
        return 1
    fi
}

# Create schema_migrations table if it doesn't exist
create_migrations_table() {
    log_info "Creating schema_migrations table if it doesn't exist..."
    
    local ddl="CREATE TABLE IF NOT EXISTS schema_migrations (
        version STRING(50) NOT NULL,
        applied_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
        description STRING(255),
    ) PRIMARY KEY (version);"
    
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl="$ddl" \
        --format="text" 2>&1 | tee -a "$LOG_FILE"; then
        log_success "Schema migrations table created/verified successfully."
    else
        log_error "Failed to create schema migrations table."
        exit 1
    fi
}

# Apply a single migration
apply_migration() {
    local migration_file="$1"
    local version="$2"
    local description="$3"
    
    log_info "Applying migration $version: $description"
    
    # Backup current schema before applying migration
    backup_schema "$version"
    
    # Apply the migration
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl-file="$migration_file" \
        --format="text" 2>&1 | tee -a "$LOG_FILE"; then
        
        # Record successful migration
        record_migration "$version" "$description"
        log_success "Migration $version applied successfully."
        return 0
    else
        log_error "Failed to apply migration $version."
        return 1
    fi
}

# Record successful migration in schema_migrations table
record_migration() {
    local version="$1"
    local description="$2"
    
    local sql="INSERT INTO schema_migrations (version, applied_at, description) 
               VALUES ('$version', PENDING_COMMIT_TIMESTAMP(), '$description');"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$sql" &> /dev/null; then
        log_info "Migration $version recorded in schema_migrations table."
    else
        log_warn "Failed to record migration $version in schema_migrations table."
    fi
}

# Backup current schema
backup_schema() {
    local version="$1"
    local backup_file="schema_backup_${version}_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "Backing up schema before migration $version..."
    
    if gcloud spanner databases ddl describe "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --format="value(statements)" > "$backup_file" 2>/dev/null; then
        log_success "Schema backed up to $backup_file"
    else
        log_warn "Failed to backup schema."
    fi
}

# Get pending migrations
get_pending_migrations() {
    local applied_migrations_file="$1"
    
    log_info "Identifying pending migrations..."
    
    # Get all migration files sorted by version
    local migration_files=()
    while IFS= read -r -d '' file; do
        migration_files+=("$file")
    done < <(find "$MIGRATIONS_DIR" -name "*.sql" -type f -print0 | sort -z)
    
    # Check which migrations are pending
    local pending_migrations=()
    for file in "${migration_files[@]}"; do
        local basename=$(basename "$file" .sql)
        local version=$(echo "$basename" | cut -d'_' -f1)
        
        if [[ -f "$applied_migrations_file" ]] && grep -q "^$version$" "$applied_migrations_file"; then
            log_info "Migration $version already applied, skipping."
        else
            pending_migrations+=("$file")
        fi
    done
    
    printf '%s\n' "${pending_migrations[@]}"
}

# Validate migration file
validate_migration_file() {
    local file="$1"
    
    log_info "Validating migration file: $file"
    
    # Check if file exists and is readable
    if [[ ! -f "$file" ]] || [[ ! -r "$file" ]]; then
        log_error "Migration file $file does not exist or is not readable."
        return 1
    fi
    
    # Check if file is empty
    if [[ ! -s "$file" ]]; then
        log_error "Migration file $file is empty."
        return 1
    fi
    
    # Basic SQL syntax validation (check for common issues)
    if grep -q "ON DUPLICATE KEY UPDATE" "$file"; then
        log_error "Migration file $file contains 'ON DUPLICATE KEY UPDATE' which is not supported in Spanner."
        return 1
    fi
    
    if grep -q "FOREIGN KEY" "$file"; then
        log_warn "Migration file $file contains 'FOREIGN KEY' which is not supported in Spanner."
    fi
    
    log_success "Migration file $file validated successfully."
    return 0
}

# Main migration execution function
run_migrations() {
    log_info "Starting migration execution..."
    
    # Validate prerequisites
    validate_prerequisites
    
    # Create migrations table if needed
    create_migrations_table
    
    # Get applied migrations
    local applied_migrations_file="applied_migrations.tmp"
    if get_applied_migrations > "$applied_migrations_file"; then
        log_info "Found $(wc -l < "$applied_migrations_file") applied migrations."
    else
        log_info "No applied migrations found (fresh database)."
        touch "$applied_migrations_file"
    fi
    
    # Get pending migrations
    local pending_migrations_file="pending_migrations.tmp"
    get_pending_migrations "$applied_migrations_file" > "$pending_migrations_file"
    
    local pending_count=$(wc -l < "$pending_migrations_file")
    if [[ "$pending_count" -eq 0 ]]; then
        log_success "No pending migrations found. Database is up to date."
        cleanup_temp_files
        return 0
    fi
    
    log_info "Found $pending_count pending migrations."
    
    # Apply each pending migration
    local failed_migrations=0
    while IFS= read -r migration_file; do
        if [[ -z "$migration_file" ]]; then
            continue
        fi
        
        # Extract version and description from filename
        local basename=$(basename "$migration_file" .sql)
        local version=$(echo "$basename" | cut -d'_' -f1)
        local description=$(echo "$basename" | cut -d'_' -f2- | tr '_' ' ')
        
        # Validate migration file
        if ! validate_migration_file "$migration_file"; then
            log_error "Migration file validation failed: $migration_file"
            ((failed_migrations++))
            continue
        fi
        
        # Apply migration
        if ! apply_migration "$migration_file" "$version" "$description"; then
            log_error "Failed to apply migration: $migration_file"
            ((failed_migrations++))
            
            # Ask user if they want to continue
            if [[ "${FORCE_CONTINUE:-}" != "true" ]]; then
                read -p "Do you want to continue with remaining migrations? (y/n): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    log_error "Migration process aborted by user."
                    cleanup_temp_files
                    exit 1
                fi
            fi
        fi
    done < "$pending_migrations_file"
    
    # Summary
    if [[ "$failed_migrations" -eq 0 ]]; then
        log_success "All migrations applied successfully!"
    else
        log_warn "$failed_migrations migrations failed. Please check the logs."
    fi
    
    cleanup_temp_files
}

# Cleanup temporary files
cleanup_temp_files() {
    rm -f applied_migrations.tmp pending_migrations.tmp
}

# Show migration status
show_status() {
    log_info "Migration Status for $PROJECT_ID/$INSTANCE_ID/$DATABASE_ID"
    echo "=============================================="
    
    # Get applied migrations
    local applied_migrations_file="applied_migrations.tmp"
    if get_applied_migrations > "$applied_migrations_file"; then
        echo "Applied Migrations:"
        while IFS= read -r version; do
            echo "  ✓ $version"
        done < "$applied_migrations_file"
    else
        echo "Applied Migrations: None (fresh database)"
        touch "$applied_migrations_file"
    fi
    
    echo ""
    
    # Get pending migrations
    local pending_migrations_file="pending_migrations.tmp"
    get_pending_migrations "$applied_migrations_file" > "$pending_migrations_file"
    
    if [[ -s "$pending_migrations_file" ]]; then
        echo "Pending Migrations:"
        while IFS= read -r migration_file; do
            if [[ -n "$migration_file" ]]; then
                local basename=$(basename "$migration_file" .sql)
                local version=$(echo "$basename" | cut -d'_' -f1)
                local description=$(echo "$basename" | cut -d'_' -f2- | tr '_' ' ')
                echo "  ○ $version: $description"
            fi
        done < "$pending_migrations_file"
    else
        echo "Pending Migrations: None"
    fi
    
    cleanup_temp_files
}

# Help function
show_help() {
    cat << EOF
Usage: $0 [OPTIONS] COMMAND

Production-ready migration execution script for Analysis Engine.

COMMANDS:
    run         Execute all pending migrations
    status      Show migration status
    help        Show this help message

OPTIONS:
    -p, --project PROJECT_ID     GCP project ID (default: ccl-development)
    -i, --instance INSTANCE_ID   Spanner instance ID (default: ccl-instance)
    -d, --database DATABASE_ID   Database ID (default: ccl_main)
    -m, --migrations-dir DIR     Migrations directory (default: migrations)
    -l, --log-file FILE          Log file path (default: migration_TIMESTAMP.log)
    -f, --force-continue         Continue on migration failures without prompting
    -h, --help                   Show this help message

ENVIRONMENT VARIABLES:
    PROJECT_ID          GCP project ID
    INSTANCE_ID         Spanner instance ID
    DATABASE_ID         Database ID
    MIGRATIONS_DIR      Migrations directory path
    LOG_FILE            Log file path
    FORCE_CONTINUE      Set to 'true' to continue on failures

EXAMPLES:
    $0 run                                   # Run all pending migrations
    $0 status                               # Show migration status
    $0 -p my-project -i my-instance run     # Run with custom project/instance
    $0 --force-continue run                 # Run without prompting on failures

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--project)
            PROJECT_ID="$2"
            shift 2
            ;;
        -i|--instance)
            INSTANCE_ID="$2"
            shift 2
            ;;
        -d|--database)
            DATABASE_ID="$2"
            shift 2
            ;;
        -m|--migrations-dir)
            MIGRATIONS_DIR="$2"
            shift 2
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        -f|--force-continue)
            FORCE_CONTINUE="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        run)
            COMMAND="run"
            shift
            ;;
        status)
            COMMAND="status"
            shift
            ;;
        help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Default command
COMMAND="${COMMAND:-run}"

# Main execution
case "$COMMAND" in
    run)
        run_migrations
        ;;
    status)
        show_status
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac