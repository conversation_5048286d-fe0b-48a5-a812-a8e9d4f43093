#!/bin/bash

# Index validation script for Analysis Engine
# This script validates that all indexes are created correctly and are performant

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-ccl-development}"
INSTANCE_ID="${INSTANCE_ID:-ccl-instance}"
DATABASE_ID="${DATABASE_ID:-ccl_main}"
LOG_FILE="${LOG_FILE:-index_validation_$(date +%Y%m%d_%H%M%S).log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

# Expected indexes for each table
declare -A EXPECTED_INDEXES=(
    ["analyses"]="idx_analyses_repository_url idx_analyses_status idx_analyses_user_id idx_analyses_started_at idx_analyses_completed_at idx_analyses_commit_hash idx_analyses_repo_commit idx_analyses_performance"
    ["file_analyses"]="idx_file_analyses_path idx_file_analyses_language idx_file_analyses_complexity idx_file_analyses_size idx_file_analyses_content_hash idx_file_analyses_embeddings idx_file_analyses_created"
    ["schema_migrations"]=""
)

# Get current schema
get_current_schema() {
    log_info "Retrieving current database schema..."
    
    local schema_file="current_schema.sql"
    if gcloud spanner databases ddl describe "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --format="value(statements)" > "$schema_file" 2>/dev/null; then
        log_success "Schema retrieved successfully."
        echo "$schema_file"
    else
        log_error "Failed to retrieve schema."
        return 1
    fi
}

# Check if index exists
check_index_exists() {
    local index_name="$1"
    local schema_file="$2"
    
    if grep -q "CREATE INDEX $index_name" "$schema_file"; then
        return 0
    else
        return 1
    fi
}

# Validate index performance with test query
validate_index_performance() {
    local index_name="$1"
    local test_query="$2"
    
    log_info "Testing performance of index $index_name..."
    
    # Create test data if needed
    create_test_data_if_needed
    
    # Run query and check if it uses the index
    local start_time=$(date +%s%N)
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$test_query" \
        --format="table" &> /dev/null; then
        
        local end_time=$(date +%s%N)
        local duration=$((($end_time - $start_time) / 1000000))  # Convert to milliseconds
        
        log_success "Index $index_name performance test passed (${duration}ms)."
        return 0
    else
        log_error "Index $index_name performance test failed."
        return 1
    fi
}

# Create test data if needed
create_test_data_if_needed() {
    # Check if we have any test data
    local count=$(gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="SELECT COUNT(*) as count FROM analyses" \
        --format="value(count)" 2>/dev/null || echo "0")
    
    if [[ "$count" -eq 0 ]]; then
        log_info "Creating test data for index validation..."
        
        # Insert test analysis record
        local test_sql="INSERT INTO analyses (
            analysis_id, repository_url, branch, commit_hash, repository_size_bytes, clone_time_ms,
            status, started_at, metrics, patterns, languages, embeddings, user_id, file_count, 
            success_rate, warnings, successful_analyses, created_at, updated_at
        ) VALUES (
            'test-analysis-index', 'https://github.com/test/repo', 'main', 'abc123def456', 1000000, 5000,
            'completed', PENDING_COMMIT_TIMESTAMP(), '{}', '[]', '{}', '[]', 'test-user', 10, 
            1.0, '[]', '[]', PENDING_COMMIT_TIMESTAMP(), PENDING_COMMIT_TIMESTAMP()
        );"
        
        if gcloud spanner databases execute-sql "$DATABASE_ID" \
            --instance="$INSTANCE_ID" \
            --project="$PROJECT_ID" \
            --sql="$test_sql" &> /dev/null; then
            
            # Insert test file analysis record
            local file_sql="INSERT INTO file_analyses (
                analysis_id, file_id, file_path, language, content_hash, size_bytes,
                lines_of_code, total_lines, complexity_score, ast_data, symbols, code_chunks, 
                metrics, created_at
            ) VALUES (
                'test-analysis-index', 'test-file-index', 'src/main.rs', 'rust', 'def456ghi789', 1000,
                50, 60, 2.5, '{}', '[]', '[]', '{}', PENDING_COMMIT_TIMESTAMP()
            );"
            
            if gcloud spanner databases execute-sql "$DATABASE_ID" \
                --instance="$INSTANCE_ID" \
                --project="$PROJECT_ID" \
                --sql="$file_sql" &> /dev/null; then
                log_success "Test data created successfully."
            else
                log_warn "Failed to create test file analysis data."
            fi
        else
            log_warn "Failed to create test analysis data."
        fi
    fi
}

# Clean up test data
cleanup_test_data() {
    log_info "Cleaning up test data..."
    
    local cleanup_sql="DELETE FROM file_analyses WHERE analysis_id = 'test-analysis-index';
DELETE FROM analyses WHERE analysis_id = 'test-analysis-index';"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$cleanup_sql" &> /dev/null; then
        log_success "Test data cleaned up successfully."
    else
        log_warn "Failed to clean up test data."
    fi
}

# Validate all indexes for a table
validate_table_indexes() {
    local table_name="$1"
    local schema_file="$2"
    
    log_info "Validating indexes for table: $table_name"
    
    local expected_indexes="${EXPECTED_INDEXES[$table_name]}"
    local missing_indexes=()
    local index_count=0
    
    # Check each expected index
    for index_name in $expected_indexes; do
        if check_index_exists "$index_name" "$schema_file"; then
            log_success "✓ Index $index_name exists."
            ((index_count++))
        else
            log_error "✗ Index $index_name is missing."
            missing_indexes+=("$index_name")
        fi
    done
    
    # Report results
    if [[ ${#missing_indexes[@]} -eq 0 ]]; then
        log_success "All indexes for table $table_name are present ($index_count indexes)."
        return 0
    else
        log_error "Missing ${#missing_indexes[@]} indexes for table $table_name: ${missing_indexes[*]}"
        return 1
    fi
}

# Test index performance with specific queries
test_index_performance() {
    log_info "Testing index performance with sample queries..."
    
    local performance_tests=(
        "idx_analyses_repository_url:SELECT analysis_id FROM analyses WHERE repository_url = 'https://github.com/test/repo' LIMIT 10"
        "idx_analyses_status:SELECT analysis_id FROM analyses WHERE status = 'completed' LIMIT 10"
        "idx_analyses_commit_hash:SELECT analysis_id FROM analyses WHERE commit_hash = 'abc123def456' LIMIT 10"
        "idx_analyses_repo_commit:SELECT analysis_id FROM analyses WHERE repository_url = 'https://github.com/test/repo' AND commit_hash = 'abc123def456' LIMIT 10"
        "idx_file_analyses_path:SELECT file_id FROM file_analyses WHERE analysis_id = 'test-analysis-index' AND file_path = 'src/main.rs' LIMIT 10"
        "idx_file_analyses_language:SELECT file_id FROM file_analyses WHERE language = 'rust' LIMIT 10"
        "idx_file_analyses_complexity:SELECT file_id FROM file_analyses WHERE complexity_score > 2.0 LIMIT 10"
        "idx_file_analyses_content_hash:SELECT file_id FROM file_analyses WHERE content_hash = 'def456ghi789' LIMIT 10"
    )
    
    local failed_tests=0
    
    for test in "${performance_tests[@]}"; do
        local index_name=$(echo "$test" | cut -d':' -f1)
        local query=$(echo "$test" | cut -d':' -f2)
        
        if ! validate_index_performance "$index_name" "$query"; then
            ((failed_tests++))
        fi
    done
    
    if [[ "$failed_tests" -eq 0 ]]; then
        log_success "All index performance tests passed."
        return 0
    else
        log_error "$failed_tests index performance tests failed."
        return 1
    fi
}

# Check index statistics
check_index_statistics() {
    log_info "Checking index statistics and usage..."
    
    # This is a simplified check - in a real production environment,
    # you would use Spanner's query statistics and execution plans
    
    local stats_query="SELECT 
        TABLE_NAME,
        INDEX_NAME,
        INDEX_TYPE,
        IS_UNIQUE,
        IS_NULL_FILTERED
    FROM INFORMATION_SCHEMA.INDEXES 
    WHERE TABLE_SCHEMA = '' 
    AND TABLE_NAME IN ('analyses', 'file_analyses', 'schema_migrations')
    ORDER BY TABLE_NAME, INDEX_NAME;"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$stats_query" \
        --format="table" 2>/dev/null; then
        log_success "Index statistics retrieved successfully."
        return 0
    else
        log_warn "Could not retrieve index statistics."
        return 1
    fi
}

# Check for unused or duplicate indexes
check_for_index_issues() {
    log_info "Checking for potential index issues..."
    
    # Get all indexes
    local indexes_query="SELECT 
        TABLE_NAME,
        INDEX_NAME,
        COLUMN_NAME,
        ORDINAL_POSITION
    FROM INFORMATION_SCHEMA.INDEX_COLUMNS 
    WHERE TABLE_SCHEMA = '' 
    AND TABLE_NAME IN ('analyses', 'file_analyses', 'schema_migrations')
    ORDER BY TABLE_NAME, INDEX_NAME, ORDINAL_POSITION;"
    
    local indexes_file="indexes_info.tmp"
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$indexes_query" \
        --format="csv" > "$indexes_file" 2>/dev/null; then
        
        # Basic analysis of indexes
        local total_indexes=$(tail -n +2 "$indexes_file" | cut -d',' -f2 | sort -u | wc -l)
        log_info "Total indexes found: $total_indexes"
        
        # Check for indexes on JSON columns (which may not be optimal)
        if grep -q "JSON" "$indexes_file"; then
            log_warn "Found indexes on JSON columns - these may not be optimal in Spanner."
        fi
        
        rm -f "$indexes_file"
        log_success "Index analysis completed."
        return 0
    else
        log_warn "Could not retrieve index information."
        return 1
    fi
}

# Main validation function
run_index_validation() {
    log_info "Starting comprehensive index validation..."
    
    # Get current schema
    local schema_file
    if ! schema_file=$(get_current_schema); then
        log_error "Could not retrieve database schema."
        return 1
    fi
    
    local validation_errors=0
    
    # Validate indexes for each table
    for table_name in "${!EXPECTED_INDEXES[@]}"; do
        if ! validate_table_indexes "$table_name" "$schema_file"; then
            ((validation_errors++))
        fi
    done
    
    # Test index performance
    if ! test_index_performance; then
        ((validation_errors++))
    fi
    
    # Check index statistics
    if ! check_index_statistics; then
        ((validation_errors++))
    fi
    
    # Check for index issues
    if ! check_for_index_issues; then
        ((validation_errors++))
    fi
    
    # Clean up
    cleanup_test_data
    rm -f "$schema_file"
    
    # Summary
    if [[ "$validation_errors" -eq 0 ]]; then
        log_success "All index validations passed! Database indexes are correctly configured."
        return 0
    else
        log_error "$validation_errors validation errors found. Please review the logs."
        return 1
    fi
}

# Show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Index validation script for Analysis Engine.

OPTIONS:
    -p, --project PROJECT_ID     GCP project ID (default: ccl-development)
    -i, --instance INSTANCE_ID   Spanner instance ID (default: ccl-instance)
    -d, --database DATABASE_ID   Database ID (default: ccl_main)
    -l, --log-file FILE          Log file path (default: index_validation_TIMESTAMP.log)
    -h, --help                   Show this help message

ENVIRONMENT VARIABLES:
    PROJECT_ID          GCP project ID
    INSTANCE_ID         Spanner instance ID
    DATABASE_ID         Database ID
    LOG_FILE            Log file path

EXAMPLES:
    $0                                      # Validate with defaults
    $0 -p my-project -i my-instance        # Validate with custom project/instance
    $0 -l my-validation.log                # Validate with custom log file

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--project)
            PROJECT_ID="$2"
            shift 2
            ;;
        -i|--instance)
            INSTANCE_ID="$2"
            shift 2
            ;;
        -d|--database)
            DATABASE_ID="$2"
            shift 2
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
run_index_validation