# Analysis Engine - Optimized Cloud Run Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Analysis Engine to Google Cloud Run with optimized performance, security, and reliability.

## Key Improvements

### 1. **Optimized Docker Image**
- Multi-stage build with dependency caching
- Distroless base image for minimal attack surface
- Final image size: ~200MB (down from ~1GB)
- Cold start time: <5 seconds

### 2. **Enhanced Deployment Strategies**
- Canary deployments with gradual rollout
- Blue-green deployment option
- Automated rollback on failure
- Health check validation at each stage

### 3. **Security Enhancements**
- Non-root user execution
- Distroless runtime (no shell access)
- Automated vulnerability scanning
- Secret management via Google Secret Manager

### 4. **Performance Optimizations**
- CPU boost enabled for faster cold starts
- Aggressive Rust compiler optimizations
- Binary stripping for size reduction
- Connection pooling and caching

## Prerequisites

1. **Google Cloud Project Setup**
   ```bash
   gcloud config set project vibe-match-463114
   ```

2. **Required APIs**
   ```bash
   gcloud services enable \
     run.googleapis.com \
     cloudbuild.googleapis.com \
     artifactregistry.googleapis.com \
     secretmanager.googleapis.com \
     spanner.googleapis.com \
     storage.googleapis.com \
     pubsub.googleapis.com
   ```

3. **Service Account**
   ```bash
   gcloud iam service-accounts create analysis-engine \
     --display-name="Analysis Engine Service Account"
   ```

4. **Artifact Registry Repository**
   ```bash
   gcloud artifacts repositories create ccl-services \
     --repository-format=docker \
     --location=us-central1 \
     --description="CCL Services Docker Repository"
   ```

5. **Secrets Setup**
   ```bash
   # Create JWT secret
   echo -n "your-jwt-secret" | gcloud secrets create analysis-engine-jwt-secret \
     --data-file=-
   
   # Create Gemini API key secret
   echo -n "your-gemini-api-key" | gcloud secrets create gemini-api-key \
     --data-file=-
   
   # Grant access to service account
   gcloud secrets add-iam-policy-binding analysis-engine-jwt-secret \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/secretmanager.secretAccessor"
   ```

## Deployment Options

### Option 1: Automated CI/CD (Recommended)

Push to main branch triggers automatic deployment:

```bash
git add .
git commit -m "feat: deploy analysis engine"
git push origin main
```

The GitHub Actions workflow will:
1. Run tests and security scans
2. Build optimized container
3. Deploy with canary rollout
4. Validate health checks
5. Complete rollout or rollback

### Option 2: Manual Deployment Script

For manual deployments with control over strategy:

```bash
# Make script executable
chmod +x deploy-optimized.sh

# Deploy with canary strategy (default)
./deploy-optimized.sh

# Deploy with blue-green strategy
DEPLOYMENT_MODE=blue-green ./deploy-optimized.sh

# Direct deployment (no gradual rollout)
DEPLOYMENT_MODE=direct ./deploy-optimized.sh
```

### Option 3: Cloud Build

Trigger Cloud Build directly:

```bash
gcloud builds submit \
  --config=cloudbuild.optimized.yaml \
  --substitutions=_ENVIRONMENT=production
```

## Configuration

### Environment Variables

The optimized deployment uses the following configuration:

| Variable | Production Value | Description |
|----------|-----------------|-------------|
| MEMORY | 4Gi | Optimized for Tree-sitter operations |
| CPU | 4 | Parallel processing capability |
| MIN_INSTANCES | 1 | Avoid cold starts |
| MAX_INSTANCES | 1000 | High scalability |
| CONCURRENCY | 1000 | Requests per instance |
| TIMEOUT | 300s | Analysis timeout |

### Performance Tuning

1. **Connection Pooling**
   ```
   SPANNER_MAX_SESSIONS=100
   SPANNER_MIN_SESSIONS=10
   CONNECTION_POOL_SIZE=50
   ```

2. **Thread Pool**
   ```
   THREAD_POOL_SIZE=8
   RAYON_NUM_THREADS=8
   ```

3. **Caching**
   ```
   CACHE_SIZE_MB=1024
   REDIS_CONNECTION_POOL_SIZE=20
   ```

## Monitoring & Observability

### 1. Health Checks

The service exposes comprehensive health endpoints:

```bash
# Basic health
curl https://your-service-url/health

# Detailed readiness
curl https://your-service-url/ready

# Metrics
curl https://your-service-url/metrics
```

### 2. Cloud Monitoring

Set up alerts for:
- Response time > 500ms
- Error rate > 1%
- CPU utilization > 80%
- Memory usage > 3.5GB

```bash
# Create alert policy
gcloud alpha monitoring policies create \
  --notification-channels=YOUR_CHANNEL_ID \
  --display-name="Analysis Engine High Latency" \
  --condition-display-name="Response time > 500ms" \
  --condition-threshold-value=0.5 \
  --condition-threshold-duration=60s
```

### 3. Logging

View logs:
```bash
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=analysis-engine" \
  --limit=50 \
  --format=json
```

## Rollback Procedures

### Automatic Rollback

The deployment automatically rolls back if:
- Health checks fail
- Error rate exceeds threshold
- Performance degrades

### Manual Rollback

```bash
# List revisions
gcloud run revisions list --service=analysis-engine --region=us-central1

# Route traffic to previous revision
gcloud run services update-traffic analysis-engine \
  --region=us-central1 \
  --to-revisions=analysis-engine-00001-abc=100
```

## Performance Validation

After deployment, validate performance:

```bash
# Run load test
./run_load_tests.sh

# Check metrics
curl https://your-service-url/metrics | grep -E "(latency|throughput|errors)"

# Validate language support
curl -X POST https://your-service-url/api/v1/analysis \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'
```

## Troubleshooting

### Cold Start Issues

If experiencing slow cold starts:
1. Ensure MIN_INSTANCES >= 1
2. Enable CPU boost
3. Check image size (<500MB)

### Memory Issues

For OOM errors:
1. Increase memory limit
2. Check for memory leaks in Tree-sitter parsers
3. Enable memory profiling

### Connection Issues

For database connection problems:
1. Verify VPC connector configuration
2. Check Spanner instance accessibility
3. Review connection pool settings

## Cost Optimization

Estimated monthly costs:
- Cloud Run (1 min instance): ~$50
- Spanner: ~$65
- Artifact Registry: ~$5
- Total: ~$120/month + usage

To reduce costs:
1. Use development environment for testing
2. Implement request batching
3. Enable auto-scaling down during low traffic

## Security Checklist

- [ ] Service account has minimal permissions
- [ ] Secrets stored in Secret Manager
- [ ] VPC connector configured
- [ ] No hardcoded credentials
- [ ] Container runs as non-root
- [ ] Security scanning enabled
- [ ] HTTPS only access
- [ ] CORS properly configured

## Next Steps

1. **Enable Production Features**
   - Set up Cloud CDN for static assets
   - Configure Cloud Armor for DDoS protection
   - Enable Cloud Trace for distributed tracing

2. **Optimize Further**
   - Implement request coalescing
   - Add Redis caching layer
   - Enable gRPC for internal communication

3. **Scale Globally**
   - Deploy to multiple regions
   - Set up Cloud Load Balancing
   - Implement geo-routing

---

For support, contact the platform team or check the [troubleshooting guide](./TROUBLESHOOTING.md).