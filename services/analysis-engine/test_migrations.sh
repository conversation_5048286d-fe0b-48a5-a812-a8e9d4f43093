#!/bin/bash

# Migration testing script for Analysis Engine
# This script tests migrations in a safe development environment

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-ccl-development}"
INSTANCE_ID="${INSTANCE_ID:-ccl-instance}"
DATABASE_ID="${DATABASE_ID:-ccl_main_test}"
MIGRATIONS_DIR="${MIGRATIONS_DIR:-migrations}"
LOG_FILE="${LOG_FILE:-migration_test_$(date +%Y%m%d_%H%M%S).log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

# Create test database
create_test_database() {
    log_info "Creating test database: $DATABASE_ID"
    
    # Delete existing test database if it exists
    if gcloud spanner databases describe "$DATABASE_ID" --instance="$INSTANCE_ID" --project="$PROJECT_ID" &> /dev/null; then
        log_warn "Test database $DATABASE_ID already exists. Deleting..."
        gcloud spanner databases delete "$DATABASE_ID" --instance="$INSTANCE_ID" --project="$PROJECT_ID" --quiet
    fi
    
    # Create new test database
    if gcloud spanner databases create "$DATABASE_ID" --instance="$INSTANCE_ID" --project="$PROJECT_ID" --quiet; then
        log_success "Test database $DATABASE_ID created successfully."
    else
        log_error "Failed to create test database $DATABASE_ID."
        exit 1
    fi
}

# Clean up test database
cleanup_test_database() {
    log_info "Cleaning up test database: $DATABASE_ID"
    
    if gcloud spanner databases delete "$DATABASE_ID" --instance="$INSTANCE_ID" --project="$PROJECT_ID" --quiet; then
        log_success "Test database $DATABASE_ID deleted successfully."
    else
        log_warn "Failed to delete test database $DATABASE_ID."
    fi
}

# Test individual migration
test_migration() {
    local migration_file="$1"
    local version="$2"
    local description="$3"
    
    log_info "Testing migration $version: $description"
    
    # Apply migration
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl-file="$migration_file" \
        --format="text" 2>&1 | tee -a "$LOG_FILE"; then
        
        log_success "Migration $version applied successfully in test."
        return 0
    else
        log_error "Failed to apply migration $version in test."
        return 1
    fi
}

# Validate schema after migration
validate_schema() {
    log_info "Validating schema after migrations..."
    
    # Get current schema
    local schema_file="current_schema.sql"
    gcloud spanner databases ddl describe "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --format="value(statements)" > "$schema_file"
    
    # Check for expected tables
    local expected_tables=("schema_migrations" "analyses" "file_analyses")
    for table in "${expected_tables[@]}"; do
        if grep -q "CREATE TABLE $table" "$schema_file"; then
            log_success "Table $table exists in schema."
        else
            log_error "Table $table missing from schema."
            return 1
        fi
    done
    
    # Check for expected columns in analyses table
    local expected_columns=("commit_hash" "repository_size_bytes" "clone_time_ms" "warnings")
    for column in "${expected_columns[@]}"; do
        if grep -A 50 "CREATE TABLE analyses" "$schema_file" | grep -q "$column"; then
            log_success "Column $column exists in analyses table."
        else
            log_error "Column $column missing from analyses table."
            return 1
        fi
    done
    
    # Check for expected indexes
    local expected_indexes=("idx_analyses_commit_hash" "idx_analyses_repo_commit" "idx_file_analyses_path")
    for index in "${expected_indexes[@]}"; do
        if grep -q "CREATE INDEX $index" "$schema_file"; then
            log_success "Index $index exists in schema."
        else
            log_error "Index $index missing from schema."
            return 1
        fi
    done
    
    rm -f "$schema_file"
    log_success "Schema validation completed successfully."
    return 0
}

# Test data operations
test_data_operations() {
    log_info "Testing basic data operations..."
    
    # Test inserting into schema_migrations table
    local sql="INSERT INTO schema_migrations (version, applied_at, description) 
               VALUES ('test_001', PENDING_COMMIT_TIMESTAMP(), 'Test migration');"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$sql" &> /dev/null; then
        log_success "Successfully inserted into schema_migrations table."
    else
        log_error "Failed to insert into schema_migrations table."
        return 1
    fi
    
    # Test querying schema_migrations table
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="SELECT version FROM schema_migrations WHERE version = 'test_001'" \
        --format="value(version)" | grep -q "test_001"; then
        log_success "Successfully queried schema_migrations table."
    else
        log_error "Failed to query schema_migrations table."
        return 1
    fi
    
    # Test inserting into analyses table
    local analyses_sql="INSERT INTO analyses (
        analysis_id, repository_url, branch, commit_hash, repository_size_bytes, clone_time_ms,
        status, started_at, metrics, patterns, languages, embeddings, user_id, file_count, 
        success_rate, warnings, successful_analyses
    ) VALUES (
        'test-analysis-001', 'https://github.com/test/repo', 'main', 'abc123', 1000000, 5000,
        'completed', PENDING_COMMIT_TIMESTAMP(), '{}', '[]', '{}', '[]', 'test-user', 10, 
        1.0, '[]', '[]'
    );"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$analyses_sql" &> /dev/null; then
        log_success "Successfully inserted into analyses table."
    else
        log_error "Failed to insert into analyses table."
        return 1
    fi
    
    # Test inserting into file_analyses table
    local file_analyses_sql="INSERT INTO file_analyses (
        analysis_id, file_id, file_path, language, content_hash, size_bytes,
        lines_of_code, total_lines, complexity_score, ast_data, symbols, code_chunks, 
        metrics, created_at
    ) VALUES (
        'test-analysis-001', 'test-file-001', 'src/main.rs', 'rust', 'def456', 1000,
        50, 60, 2.5, '{}', '[]', '[]', '{}', PENDING_COMMIT_TIMESTAMP()
    );"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$file_analyses_sql" &> /dev/null; then
        log_success "Successfully inserted into file_analyses table."
    else
        log_error "Failed to insert into file_analyses table."
        return 1
    fi
    
    # Test querying with joins
    local join_sql="SELECT a.analysis_id, a.repository_url, f.file_path, f.language
                   FROM analyses a
                   JOIN file_analyses f ON a.analysis_id = f.analysis_id
                   WHERE a.analysis_id = 'test-analysis-001';"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$join_sql" \
        --format="table" | grep -q "test-analysis-001"; then
        log_success "Successfully performed join query between analyses and file_analyses tables."
    else
        log_error "Failed to perform join query."
        return 1
    fi
    
    log_success "All data operations tests passed."
    return 0
}

# Test index performance
test_index_performance() {
    log_info "Testing index performance..."
    
    # Test commit_hash index
    local commit_hash_query="SELECT analysis_id FROM analyses WHERE commit_hash = 'abc123';"
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$commit_hash_query" \
        --format="value(analysis_id)" | grep -q "test-analysis-001"; then
        log_success "Commit hash index is working correctly."
    else
        log_error "Commit hash index is not working."
        return 1
    fi
    
    # Test repository_url + commit_hash index
    local repo_commit_query="SELECT analysis_id FROM analyses WHERE repository_url = 'https://github.com/test/repo' AND commit_hash = 'abc123';"
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$repo_commit_query" \
        --format="value(analysis_id)" | grep -q "test-analysis-001"; then
        log_success "Repository URL + commit hash index is working correctly."
    else
        log_error "Repository URL + commit hash index is not working."
        return 1
    fi
    
    # Test file_analyses indexes
    local file_path_query="SELECT file_id FROM file_analyses WHERE analysis_id = 'test-analysis-001' AND file_path = 'src/main.rs';"
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$file_path_query" \
        --format="value(file_id)" | grep -q "test-file-001"; then
        log_success "File path index is working correctly."
    else
        log_error "File path index is not working."
        return 1
    fi
    
    log_success "All index performance tests passed."
    return 0
}

# Test rollback procedure
test_rollback() {
    log_info "Testing rollback procedure..."
    
    # Create a test table to roll back
    local test_ddl="CREATE TABLE test_rollback_table (
        id STRING(36) NOT NULL,
        name STRING(255),
        created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    ) PRIMARY KEY (id);"
    
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl="$test_ddl" &> /dev/null; then
        log_success "Test table created for rollback test."
    else
        log_error "Failed to create test table for rollback test."
        return 1
    fi
    
    # Verify table exists
    local schema_file="rollback_schema.sql"
    gcloud spanner databases ddl describe "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --format="value(statements)" > "$schema_file"
    
    if grep -q "CREATE TABLE test_rollback_table" "$schema_file"; then
        log_success "Test table exists before rollback."
    else
        log_error "Test table does not exist before rollback."
        return 1
    fi
    
    # Perform rollback
    local rollback_ddl="DROP TABLE test_rollback_table;"
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl="$rollback_ddl" &> /dev/null; then
        log_success "Test table dropped successfully."
    else
        log_error "Failed to drop test table."
        return 1
    fi
    
    # Verify table is gone
    gcloud spanner databases ddl describe "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --format="value(statements)" > "$schema_file"
    
    if ! grep -q "CREATE TABLE test_rollback_table" "$schema_file"; then
        log_success "Test table successfully removed after rollback."
    else
        log_error "Test table still exists after rollback."
        return 1
    fi
    
    rm -f "$schema_file"
    log_success "Rollback procedure test passed."
    return 0
}

# Run comprehensive migration tests
run_comprehensive_tests() {
    log_info "Starting comprehensive migration tests..."
    
    # Create test database
    create_test_database
    
    # Test each migration individually
    local migration_files=()
    while IFS= read -r -d '' file; do
        migration_files+=("$file")
    done < <(find "$MIGRATIONS_DIR" -name "*.sql" -type f -print0 | sort -z)
    
    local failed_tests=0
    for migration_file in "${migration_files[@]}"; do
        local basename=$(basename "$migration_file" .sql)
        local version=$(echo "$basename" | cut -d'_' -f1)
        local description=$(echo "$basename" | cut -d'_' -f2- | tr '_' ' ')
        
        if ! test_migration "$migration_file" "$version" "$description"; then
            ((failed_tests++))
        fi
    done
    
    # Validate final schema
    if ! validate_schema; then
        ((failed_tests++))
    fi
    
    # Test data operations
    if ! test_data_operations; then
        ((failed_tests++))
    fi
    
    # Test index performance
    if ! test_index_performance; then
        ((failed_tests++))
    fi
    
    # Test rollback procedure
    if ! test_rollback; then
        ((failed_tests++))
    fi
    
    # Summary
    if [[ "$failed_tests" -eq 0 ]]; then
        log_success "All migration tests passed!"
    else
        log_error "$failed_tests tests failed. Check the logs for details."
    fi
    
    # Clean up test database
    cleanup_test_database
    
    return $failed_tests
}

# Show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Migration testing script for Analysis Engine.

OPTIONS:
    -p, --project PROJECT_ID     GCP project ID (default: ccl-development)
    -i, --instance INSTANCE_ID   Spanner instance ID (default: ccl-instance)
    -d, --database DATABASE_ID   Test database ID (default: ccl_main_test)
    -m, --migrations-dir DIR     Migrations directory (default: migrations)
    -l, --log-file FILE          Log file path (default: migration_test_TIMESTAMP.log)
    -h, --help                   Show this help message

ENVIRONMENT VARIABLES:
    PROJECT_ID          GCP project ID
    INSTANCE_ID         Spanner instance ID
    DATABASE_ID         Test database ID
    MIGRATIONS_DIR      Migrations directory path
    LOG_FILE            Log file path

EXAMPLES:
    $0                                      # Run all tests with defaults
    $0 -p my-project -i my-instance        # Run with custom project/instance
    $0 -d my-test-db                       # Run with custom test database

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--project)
            PROJECT_ID="$2"
            shift 2
            ;;
        -i|--instance)
            INSTANCE_ID="$2"
            shift 2
            ;;
        -d|--database)
            DATABASE_ID="$2"
            shift 2
            ;;
        -m|--migrations-dir)
            MIGRATIONS_DIR="$2"
            shift 2
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
run_comprehensive_tests