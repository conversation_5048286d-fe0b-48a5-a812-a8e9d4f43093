# GCP Permissions Setup for Analysis Engine

## Current Status
- **Project**: vibe-match-463114
- **Service Account**: <EMAIL>
- **Issue**: Missing `aiplatform.endpoints.predict` permission for Vertex AI access
- **APIs**: Vertex AI API (aiplatform.googleapis.com) is ENABLED ✅

## Required Actions (Must be run by Project Owner: <EMAIL>)

### 1. Grant Vertex AI User Role
```bash
gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/aiplatform.user"
```

### 2. Verify the Binding
```bash
gcloud projects get-iam-policy vibe-match-463114 \
    --flatten="bindings[].members" \
    --filter="bindings.members:<EMAIL>" \
    --format="table(bindings.role)"
```

### 3. Test API Access (After Permission Grant)
```bash
# Switch to service account
export GOOGLE_APPLICATION_CREDENTIALS="path/to/vibe-match-463114-dbda8d8a6cb9.json"
gcloud auth activate-service-account --key-file="path/to/vibe-match-463114-dbda8d8a6cb9.json"

# Test Gemini 2.5 Flash
curl -X POST \
  -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"role":"user","parts":[{"text":"Hello, test"}]}]}' \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/vibe-match-463114/locations/us-central1/publishers/google/models/gemini-2.5-flash:generateContent"

# Test Gemini 2.5 Pro
curl -X POST \
  -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"role":"user","parts":[{"text":"Hello, test"}]}]}' \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/vibe-match-463114/locations/us-central1/publishers/google/models/gemini-2.5-pro:generateContent"
```

## Analysis of Current Permissions

### ✅ Working Components
- Vertex AI API is enabled
- User account (<EMAIL>) has aiplatform.admin access
- API endpoints are accessible with proper authentication
- Service account exists and is properly configured

### ❌ Missing Components
- Service account lacks `roles/aiplatform.user` role
- Cannot grant permissions due to limited IAM access
- Project owner permissions required for IAM modifications

### 🔧 Technical Details
- **Required Permission**: `aiplatform.endpoints.predict`
- **Included in Role**: `roles/aiplatform.user`
- **Region**: us-central1
- **Models**: Gemini 2.5 Flash, Gemini 2.5 Pro

## Alternative Solutions (If Owner Unavailable)

### Option 1: Use User Account Temporarily
The Analysis Engine can temporarily use user account credentials (<EMAIL>) which already has the required permissions.

### Option 2: Request Specific Permission
Create a custom role with minimal permissions:
```bash
gcloud iam roles create analysis_engine_ai_access \
    --project=vibe-match-463114 \
    --permissions="aiplatform.endpoints.predict" \
    --title="Analysis Engine AI Access" \
    --description="Minimal role for AI prediction access"

gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:<EMAIL>" \
    --role="projects/vibe-match-463114/roles/analysis_engine_ai_access"
```

## Validation Steps

1. **Permission Verification**:
   ```bash
   gcloud projects get-iam-policy vibe-match-463114 --format="json" | \
   jq '.bindings[] | select(.members[] | contains("analysis-engine"))'
   ```

2. **API Test**:
   ```bash
   curl -X POST \
     -H "Authorization: Bearer $(gcloud auth print-access-token)" \
     -H "Content-Type: application/json" \
     -d '{"contents":[{"role":"user","parts":[{"text":"Test"}]}]}' \
     "https://us-central1-aiplatform.googleapis.com/v1/projects/vibe-match-463114/locations/us-central1/publishers/google/models/gemini-2.5-flash:generateContent"
   ```

3. **Expected Response**: JSON with model response content, not permission denied error

## Security Best Practices ✅

- ✅ Using least privilege principle (aiplatform.user only)
- ✅ Service account isolated to specific functionality
- ✅ Proper credential file management
- ✅ Regional endpoint configuration (us-central1)
- ✅ No hardcoded credentials in code

## Next Steps

1. Contact project owner (<EMAIL>) to run the IAM binding command
2. Test API access after permission grant
3. Update Analysis Engine configuration to use service account credentials
4. Implement proper error handling for permission scenarios in the application