# 🚀 Analysis Engine Production Deployment Guide

## Executive Summary

The Analysis Engine is a high-performance, Rust-based code analysis service designed for enterprise-scale deployments. This guide provides comprehensive instructions for deploying the Analysis Engine in production environments with optimal performance, security, and scalability.

## 🎯 System Requirements

### **Minimum Requirements**
- **Memory**: 4GB RAM (8GB recommended for high-load environments)
- **CPU**: 4 cores (8 cores recommended for concurrent analysis)
- **Storage**: 50GB SSD minimum (100GB recommended for large repositories)
- **Network**: High-bandwidth connection for repository cloning and AI service calls
- **Operating System**: Linux (Ubuntu 20.04+, RHEL 8+) or macOS (for development)

### **Recommended Production Environment**
- **Memory**: 16GB RAM for optimal performance
- **CPU**: 16 cores for maximum concurrency
- **Storage**: 500GB SSD with high IOPS
- **Network**: 1Gbps+ connection
- **Load Balancer**: For multi-instance deployments

## 🔧 Environment Configuration

### **Required Environment Variables**

```bash
# ====================================================================
# GOOGLE CLOUD PLATFORM CONFIGURATION
# ====================================================================
export GCP_PROJECT_ID="your-project-id"
export GCP_REGION="us-central1"
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"

# ====================================================================
# SPANNER DATABASE CONFIGURATION
# ====================================================================
export SPANNER_INSTANCE_ID="ccl-spanner-instance"
export SPANNER_DATABASE_ID="ccl_main"
export SPANNER_ENDPOINT="https://spanner.googleapis.com"

# ====================================================================
# AI SERVICES CONFIGURATION
# ====================================================================
export GEMINI_MODEL_NAME="gemini-2.5-flash"
export GEMINI_API_ENDPOINT="https://generativelanguage.googleapis.com"
export EMBEDDING_MODEL_NAME="text-embedding-005"
export EMBEDDING_TASK_TYPE="CODE_RETRIEVAL_QUERY"
export VERTEX_AI_ENDPOINT="https://us-central1-aiplatform.googleapis.com"

# ====================================================================
# PERFORMANCE TUNING CONFIGURATION
# ====================================================================
export MAX_CONCURRENT_ANALYSES=50
export MAX_MEMORY_USAGE_MB=4096
export PARSER_POOL_SIZE=16
export CACHE_SIZE_MB=1024
export ENABLE_STREAMING=true
export ENABLE_PARALLEL_PARSING=true
export BACKPRESSURE_THRESHOLD=80

# ====================================================================
# SECURITY CONFIGURATION
# ====================================================================
export ENABLE_VULNERABILITY_DETECTION=true
export ENABLE_SECRETS_DETECTION=true
export ENABLE_COMPLIANCE_CHECKING=true
export ENABLE_DEPENDENCY_SCANNING=true
export SECURITY_SCAN_TIMEOUT=300
export SECRETS_ENTROPY_THRESHOLD=3.5

# ====================================================================
# CACHING AND REDIS CONFIGURATION
# ====================================================================
export REDIS_URL="redis://localhost:6379"
export REDIS_CLUSTER_MODE=false
export CACHE_TTL_SECONDS=3600
export ENABLE_DISTRIBUTED_CACHE=true

# ====================================================================
# MONITORING AND OBSERVABILITY
# ====================================================================
export ENABLE_METRICS=true
export ENABLE_TRACING=true
export METRICS_PORT=9090
export HEALTH_CHECK_PORT=8080
export LOG_LEVEL="info"
export ENABLE_STRUCTURED_LOGGING=true

# ====================================================================
# RATE LIMITING AND THROTTLING
# ====================================================================
export RATE_LIMIT_REQUESTS_PER_MINUTE=1000
export RATE_LIMIT_BURST_SIZE=100
export ENABLE_CIRCUIT_BREAKER=true
export CIRCUIT_BREAKER_FAILURE_THRESHOLD=50
export CIRCUIT_BREAKER_TIMEOUT_SECONDS=60
```

### **Optional Performance Tuning Variables**

```bash
# ====================================================================
# ADVANCED PERFORMANCE CONFIGURATION
# ====================================================================
export TOKIO_WORKER_THREADS=16
export RAYON_NUM_THREADS=8
export STREAMING_BUFFER_SIZE=8192
export PARSER_CACHE_SIZE=1000
export ENABLE_MEMORY_POOLING=true
export ENABLE_ASYNC_PROCESSING=true
export BATCH_SIZE_LIMIT=100
export QUEUE_SIZE_LIMIT=1000

# ====================================================================
# LANGUAGE-SPECIFIC OPTIMIZATIONS
# ====================================================================
export ENABLE_TREE_SITTER_CACHING=true
export TREE_SITTER_CACHE_SIZE=500
export ENABLE_SYNTAX_HIGHLIGHTING=false
export ENABLE_DETAILED_AST=true
export LANGUAGE_DETECTION_THRESHOLD=0.8
```

## 🐳 Docker Deployment

### **Dockerfile**

```dockerfile
# Multi-stage build for optimized production image
FROM rust:1.70-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy dependency files
COPY Cargo.toml Cargo.lock ./

# Create src directory with dummy main.rs for dependency caching
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build dependencies (cached layer)
RUN cargo build --release && rm -rf src

# Copy source code
COPY src ./src
COPY tests ./tests
COPY migrations ./migrations

# Build application
RUN cargo build --release --bin analysis-engine

# Production image
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -s /bin/bash analysis-engine

# Copy binary from builder
COPY --from=builder /app/target/release/analysis-engine /usr/local/bin/analysis-engine

# Copy configuration files
COPY config/ /app/config/
COPY migrations/ /app/migrations/

# Set ownership
RUN chown -R analysis-engine:analysis-engine /app

# Switch to non-root user
USER analysis-engine

# Expose ports
EXPOSE 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Start application
CMD ["/usr/local/bin/analysis-engine"]
```

### **Docker Compose Configuration**

```yaml
# docker-compose.yml
version: '3.8'

services:
  analysis-engine:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - SPANNER_INSTANCE_ID=${SPANNER_INSTANCE_ID}
      - SPANNER_DATABASE_ID=${SPANNER_DATABASE_ID}
      - REDIS_URL=redis://redis:6379
      - MAX_CONCURRENT_ANALYSES=50
      - MAX_MEMORY_USAGE_MB=4096
      - ENABLE_STREAMING=true
      - LOG_LEVEL=info
    volumes:
      - ./config:/app/config:ro
      - gcp-credentials:/app/credentials:ro
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4'
        reservations:
          memory: 4G
          cpus: '2'

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1'

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ssl-certs:/etc/nginx/ssl:ro
    depends_on:
      - analysis-engine
    restart: unless-stopped

volumes:
  redis-data:
  gcp-credentials:
  ssl-certs:
```

## ☸️ Kubernetes Deployment

### **Deployment Configuration**

```yaml
# analysis-engine-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-engine
  namespace: ccl-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: analysis-engine
  template:
    metadata:
      labels:
        app: analysis-engine
    spec:
      containers:
      - name: analysis-engine
        image: gcr.io/your-project/analysis-engine:latest
        ports:
        - containerPort: 8080
        - containerPort: 9090
        env:
        - name: GCP_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: gcp-config
              key: project-id
        - name: SPANNER_INSTANCE_ID
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: spanner-instance-id
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        - name: MAX_CONCURRENT_ANALYSES
          value: "50"
        - name: MAX_MEMORY_USAGE_MB
          value: "4096"
        resources:
          limits:
            memory: "8Gi"
            cpu: "4"
          requests:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: gcp-credentials
          mountPath: /app/credentials
          readOnly: true
      volumes:
      - name: gcp-credentials
        secret:
          secretName: gcp-service-account
```

### **Service Configuration**

```yaml
# analysis-engine-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: analysis-engine-service
  namespace: ccl-platform
spec:
  selector:
    app: analysis-engine
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: LoadBalancer
```

## 🔐 Security Configuration

### **Service Account Setup**

```bash
# Create service account
gcloud iam service-accounts create analysis-engine-sa \
  --display-name="Analysis Engine Service Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding ${GCP_PROJECT_ID} \
  --member="serviceAccount:analysis-engine-sa@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/spanner.databaseUser"

gcloud projects add-iam-policy-binding ${GCP_PROJECT_ID} \
  --member="serviceAccount:analysis-engine-sa@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/aiplatform.user"

# Create and download key
gcloud iam service-accounts keys create analysis-engine-key.json \
  --iam-account=analysis-engine-sa@${GCP_PROJECT_ID}.iam.gserviceaccount.com
```

### **Network Security**

```bash
# Firewall rules for GKE
gcloud compute firewall-rules create analysis-engine-allow-http \
  --allow tcp:8080 \
  --source-ranges 0.0.0.0/0 \
  --description "Allow HTTP traffic to Analysis Engine"

gcloud compute firewall-rules create analysis-engine-allow-metrics \
  --allow tcp:9090 \
  --source-ranges 10.0.0.0/8 \
  --description "Allow metrics collection from internal networks"
```

## 📊 Monitoring and Observability

### **Health Check Endpoints**

```bash
# Basic health check
curl -f http://localhost:8080/health

# Detailed readiness check
curl -f http://localhost:8080/ready

# Prometheus metrics
curl http://localhost:8080/metrics

# System status
curl http://localhost:8080/api/v1/status
```

### **Monitoring Configuration**

```yaml
# monitoring-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: monitoring-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'analysis-engine'
      static_configs:
      - targets: ['analysis-engine-service:9090']
      metrics_path: /metrics
      scrape_interval: 10s
```

## 🚀 Deployment Commands

### **Local Development**

```bash
# Set up environment
export $(cat .env | xargs)

# Run migrations
./scripts/run_migrations.sh

# Start service
cargo run --release

# Run tests
cargo test --release
```

### **Production Deployment**

```bash
# Build and tag image
docker build -t gcr.io/${GCP_PROJECT_ID}/analysis-engine:latest .

# Push to registry
docker push gcr.io/${GCP_PROJECT_ID}/analysis-engine:latest

# Deploy to Kubernetes
kubectl apply -f k8s/

# Verify deployment
kubectl get pods -n ccl-platform
kubectl logs -f deployment/analysis-engine -n ccl-platform
```

## 📈 Performance Optimization

### **Resource Allocation**

```bash
# Optimize for high concurrency
export MAX_CONCURRENT_ANALYSES=100
export PARSER_POOL_SIZE=32
export TOKIO_WORKER_THREADS=32

# Optimize for memory efficiency
export MAX_MEMORY_USAGE_MB=8192
export CACHE_SIZE_MB=2048
export ENABLE_MEMORY_POOLING=true

# Optimize for I/O performance
export ENABLE_STREAMING=true
export STREAMING_BUFFER_SIZE=16384
export ENABLE_PARALLEL_PARSING=true
```

### **Database Optimization**

```bash
# Spanner optimization
export SPANNER_MAX_SESSIONS=400
export SPANNER_MIN_SESSIONS=25
export SPANNER_MAX_IDLE_SESSIONS=100
export SPANNER_WRITE_SESSIONS=50
```

## 🔄 Scaling Configuration

### **Horizontal Pod Autoscaler**

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analysis-engine-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analysis-engine
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 🛠️ Troubleshooting

### **Common Issues**

1. **High Memory Usage**
   - Reduce `MAX_CONCURRENT_ANALYSES`
   - Increase `MAX_MEMORY_USAGE_MB`
   - Enable `ENABLE_MEMORY_POOLING`

2. **Slow Performance**
   - Increase `PARSER_POOL_SIZE`
   - Enable `ENABLE_PARALLEL_PARSING`
   - Check database connection pool

3. **Database Connection Issues**
   - Verify Spanner instance is running
   - Check service account permissions
   - Validate network connectivity

### **Debug Commands**

```bash
# Check service logs
kubectl logs -f deployment/analysis-engine -n ccl-platform

# Check resource usage
kubectl top pods -n ccl-platform

# Check service endpoints
kubectl get endpoints -n ccl-platform

# Port forward for debugging
kubectl port-forward service/analysis-engine-service 8080:80
```

## 🎯 Production Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Service account created with proper permissions
- [ ] Container image built and pushed
- [ ] Kubernetes resources deployed
- [ ] Health checks passing
- [ ] Monitoring configured
- [ ] Logging configured
- [ ] Security policies applied
- [ ] Performance testing completed
- [ ] Backup and recovery procedures tested
- [ ] Documentation updated

## 📞 Support

For deployment support and troubleshooting:
- Check health endpoints: `/health`, `/ready`, `/metrics`
- Monitor logs for detailed error information
- Verify environment variables are correctly set
- Ensure all required GCP services are enabled and accessible

This deployment guide provides a comprehensive foundation for running the Analysis Engine in production environments with enterprise-grade reliability, performance, and security.