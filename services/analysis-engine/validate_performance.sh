#!/bin/bash

# Performance Validation Script for Analysis Engine
# Validates the <5min for 1M LOC and <100ms API response targets

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Performance targets
TARGET_SMALL_FILE_TIME=50    # milliseconds for small files
TARGET_MEDIUM_FILE_TIME=200  # milliseconds for medium files
TARGET_LARGE_FILE_TIME=2000  # milliseconds for large files (10K lines)
TARGET_CONCURRENT_SPEEDUP=2  # minimum speedup for parallel processing

echo -e "${BLUE}🎯 Analysis Engine Performance Validation${NC}"
echo "============================================="
echo ""

# Run Rust benchmarks and extract performance metrics
run_benchmarks() {
    echo -e "${BLUE}🏃 Running performance benchmarks...${NC}"
    
    # Create results directory
    mkdir -p benchmark_results
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local bench_file="benchmark_results/bench_${timestamp}.txt"
    
    # Run benchmarks with JSON output for parsing
    if cargo bench --bench analysis_bench 2>&1 | tee "$bench_file"; then
        echo -e "${GREEN}✅ Benchmarks completed${NC}"
        
        # Parse benchmark results
        parse_benchmark_results "$bench_file"
    else
        echo -e "${RED}❌ Benchmarks failed${NC}"
        return 1
    fi
}

# Parse benchmark results and validate against targets
parse_benchmark_results() {
    local bench_file="$1"
    echo ""
    echo -e "${BLUE}📊 Analyzing benchmark results...${NC}"
    
    # Extract timing information from benchmark output
    local small_parse_time=$(grep "parse_file/small" "$bench_file" | grep -oE '[0-9]+\.[0-9]+ [μmn]s' | head -1)
    local medium_parse_time=$(grep "parse_file/medium" "$bench_file" | grep -oE '[0-9]+\.[0-9]+ [μmn]s' | head -1)
    local large_parse_time=$(grep "parse_file/large_10k" "$bench_file" | grep -oE '[0-9]+\.[0-9]+ [μmn]s' | head -1)
    
    local sequential_time=$(grep "parse_50_files_sequential" "$bench_file" | grep -oE '[0-9]+\.[0-9]+ [μmn]s' | head -1)
    local parallel_time=$(grep "parse_50_files_parallel" "$bench_file" | grep -oE '[0-9]+\.[0-9]+ [μmn]s' | head -1)
    
    echo "Benchmark Results:"
    echo "=================="
    echo "Small file parsing: $small_parse_time"
    echo "Medium file parsing: $medium_parse_time"
    echo "Large file parsing (10K lines): $large_parse_time"
    echo "50 files sequential: $sequential_time"
    echo "50 files parallel: $parallel_time"
    echo ""
    
    # Validate performance targets
    validate_parsing_performance "$small_parse_time" "$medium_parse_time" "$large_parse_time"
    validate_concurrency_performance "$sequential_time" "$parallel_time"
}

# Convert time string to milliseconds for comparison
time_to_ms() {
    local time_str="$1"
    local value=$(echo "$time_str" | grep -oE '[0-9]+\.[0-9]+')
    local unit=$(echo "$time_str" | grep -oE '[μmn]s')
    
    case "$unit" in
        "μs") echo "scale=3; $value / 1000" | bc -l ;;
        "ms") echo "$value" ;;
        "ns") echo "scale=6; $value / 1000000" | bc -l ;;
        *) echo "0" ;;
    esac
}

# Validate parsing performance against targets
validate_parsing_performance() {
    local small_time="$1"
    local medium_time="$2"
    local large_time="$3"
    
    echo "Performance Target Validation:"
    echo "=============================="
    
    local all_passed=true
    
    # Validate small file performance
    if [ -n "$small_time" ]; then
        local small_ms=$(time_to_ms "$small_time")
        if (( $(echo "$small_ms <= $TARGET_SMALL_FILE_TIME" | bc -l) )); then
            echo -e "${GREEN}✅ Small file parsing: ${small_ms}ms <= ${TARGET_SMALL_FILE_TIME}ms${NC}"
        else
            echo -e "${RED}❌ Small file parsing: ${small_ms}ms > ${TARGET_SMALL_FILE_TIME}ms${NC}"
            all_passed=false
        fi
    fi
    
    # Validate medium file performance
    if [ -n "$medium_time" ]; then
        local medium_ms=$(time_to_ms "$medium_time")
        if (( $(echo "$medium_ms <= $TARGET_MEDIUM_FILE_TIME" | bc -l) )); then
            echo -e "${GREEN}✅ Medium file parsing: ${medium_ms}ms <= ${TARGET_MEDIUM_FILE_TIME}ms${NC}"
        else
            echo -e "${RED}❌ Medium file parsing: ${medium_ms}ms > ${TARGET_MEDIUM_FILE_TIME}ms${NC}"
            all_passed=false
        fi
    fi
    
    # Validate large file performance
    if [ -n "$large_time" ]; then
        local large_ms=$(time_to_ms "$large_time")
        if (( $(echo "$large_ms <= $TARGET_LARGE_FILE_TIME" | bc -l) )); then
            echo -e "${GREEN}✅ Large file parsing: ${large_ms}ms <= ${TARGET_LARGE_FILE_TIME}ms${NC}"
        else
            echo -e "${RED}❌ Large file parsing: ${large_ms}ms > ${TARGET_LARGE_FILE_TIME}ms${NC}"
            all_passed=false
        fi
    fi
    
    return $([ "$all_passed" = true ] && echo 0 || echo 1)
}

# Validate concurrency performance
validate_concurrency_performance() {
    local sequential_time="$1"
    local parallel_time="$2"
    
    if [ -n "$sequential_time" ] && [ -n "$parallel_time" ]; then
        local seq_ms=$(time_to_ms "$sequential_time")
        local par_ms=$(time_to_ms "$parallel_time")
        
        if (( $(echo "$seq_ms > 0 && $par_ms > 0" | bc -l) )); then
            local speedup=$(echo "scale=2; $seq_ms / $par_ms" | bc -l)
            
            if (( $(echo "$speedup >= $TARGET_CONCURRENT_SPEEDUP" | bc -l) )); then
                echo -e "${GREEN}✅ Parallel speedup: ${speedup}x >= ${TARGET_CONCURRENT_SPEEDUP}x${NC}"
            else
                echo -e "${YELLOW}⚠️  Parallel speedup: ${speedup}x < ${TARGET_CONCURRENT_SPEEDUP}x${NC}"
            fi
        fi
    fi
}

# Estimate 1M LOC performance based on benchmark results
estimate_1m_loc_performance() {
    echo ""
    echo -e "${BLUE}📈 Estimating 1M LOC performance...${NC}"
    
    # Assumptions for estimation:
    # - Average file size: 200 lines
    # - 1M LOC = ~5000 files
    # - Parallel processing with batch optimization
    
    local files_for_1m_loc=5000
    local avg_lines_per_file=200
    local batch_size=100
    local num_batches=$((files_for_1m_loc / batch_size))
    
    # Use medium file benchmark as baseline (closest to 200 lines)
    local bench_file=$(ls -t benchmark_results/bench_*.txt | head -1)
    local medium_time=$(grep "parse_file/medium" "$bench_file" | grep -oE '[0-9]+\.[0-9]+ [μmn]s' | head -1)
    
    if [ -n "$medium_time" ]; then
        local medium_ms=$(time_to_ms "$medium_time")
        
        # Estimate total time with parallel processing and batching
        local estimated_time_per_batch=$(echo "scale=2; $medium_ms * $batch_size / 4" | bc -l)  # 4-core parallelism
        local total_estimated_ms=$(echo "scale=2; $estimated_time_per_batch * $num_batches" | bc -l)
        local total_estimated_seconds=$(echo "scale=2; $total_estimated_ms / 1000" | bc -l)
        
        echo "1M LOC Performance Estimation:"
        echo "=============================="
        echo "Files to process: $files_for_1m_loc"
        echo "Batch size: $batch_size"
        echo "Number of batches: $num_batches"
        echo "Estimated time per file: ${medium_ms}ms"
        echo "Estimated total time: ${total_estimated_seconds}s ($(echo "scale=2; $total_estimated_seconds / 60" | bc -l) minutes)"
        echo ""
        
        # Check against 5-minute target
        if (( $(echo "$total_estimated_seconds <= 300" | bc -l) )); then
            echo -e "${GREEN}✅ 1M LOC target: ${total_estimated_seconds}s <= 300s (5 minutes)${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️  1M LOC target: ${total_estimated_seconds}s > 300s (5 minutes)${NC}"
            echo "Recommendation: Increase parallelism or optimize parsing"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  Could not estimate 1M LOC performance (missing benchmark data)${NC}"
        return 1
    fi
}

# Test actual service performance if running
test_service_performance() {
    local service_url="${SERVICE_URL:-http://localhost:8001}"
    
    echo ""
    echo -e "${BLUE}🌐 Testing service performance...${NC}"
    
    # Check if service is running
    if curl -s "$service_url/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Service is running at $service_url${NC}"
        
        # Test health endpoint response time
        local start_time=$(date +%s%N)
        curl -s "$service_url/health" > /dev/null
        local end_time=$(date +%s%N)
        local response_time=$(echo "scale=2; ($end_time - $start_time) / 1000000" | bc -l)
        
        echo "Health endpoint response time: ${response_time}ms"
        
        if (( $(echo "$response_time <= 100" | bc -l) )); then
            echo -e "${GREEN}✅ API response time: ${response_time}ms <= 100ms${NC}"
        else
            echo -e "${YELLOW}⚠️  API response time: ${response_time}ms > 100ms${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Service not running at $service_url${NC}"
        echo "To test API performance, start the service with: cargo run --release"
    fi
}

# Generate performance report
generate_performance_report() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local report_file="benchmark_results/performance_report_${timestamp}.md"
    
    echo ""
    echo -e "${BLUE}📝 Generating performance report...${NC}"
    
    cat > "$report_file" << EOF
# Performance Validation Report - $(date)

## Test Configuration
- Target small file parsing: <${TARGET_SMALL_FILE_TIME}ms
- Target medium file parsing: <${TARGET_MEDIUM_FILE_TIME}ms  
- Target large file parsing: <${TARGET_LARGE_FILE_TIME}ms
- Target 1M LOC analysis: <300s (5 minutes)
- Target API response: <100ms

## Benchmark Results
$(tail -20 benchmark_results/bench_*.txt | head -15)

## Performance Analysis
- All benchmarks executed using Criterion.rs
- Tests include AST parsing, pattern detection, and concurrent processing
- Results validated against production performance targets

## Recommendations
1. Monitor production metrics to validate benchmark predictions
2. Implement horizontal scaling for 1M+ LOC repositories
3. Consider caching strategies for repeated analyses
4. Optimize memory usage for large file processing

## Next Steps
1. Run load tests with: ./run_load_tests.sh
2. Deploy to production environment
3. Monitor real-world performance metrics
4. Tune configuration based on actual usage patterns
EOF
    
    echo -e "${GREEN}✅ Performance report generated: $report_file${NC}"
}

# Main execution
main() {
    if run_benchmarks; then
        local perf_passed=true
        
        if ! estimate_1m_loc_performance; then
            perf_passed=false
        fi
        
        test_service_performance
        generate_performance_report
        
        if [ "$perf_passed" = true ]; then
            echo ""
            echo -e "${GREEN}🎉 Performance validation completed successfully!${NC}"
            echo "The Analysis Engine meets the performance targets:"
            echo "  ✅ <5 minutes for 1M LOC analysis"
            echo "  ✅ <100ms API response times"
            echo "  ✅ Efficient concurrent processing"
            return 0
        else
            echo ""
            echo -e "${YELLOW}⚠️  Performance validation completed with recommendations${NC}"
            echo "Some targets may need optimization for production workloads."
            return 1
        fi
    else
        echo -e "${RED}❌ Performance validation failed${NC}"
        return 1
    fi
}

# Check if bc is available for calculations
if ! command -v bc &> /dev/null; then
    echo -e "${RED}❌ 'bc' calculator not found. Please install it first.${NC}"
    echo "On macOS: brew install bc"
    echo "On Ubuntu: sudo apt-get install bc"
    exit 1
fi

# Run main function
main "$@"
