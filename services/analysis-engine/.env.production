# ============================================
# Analysis Engine Production Environment Configuration
# ============================================
# IMPORTANT: Do not commit actual values to version control
# Use Google Secret Manager for sensitive values in production

# Google Cloud Configuration
GCP_PROJECT_ID=your-project-id
GCP_REGION=us-central1

# Service Configuration
ENVIRONMENT=production
# PORT is set automatically by Cloud Run
HOST=0.0.0.0

# GCP Service Configuration
SPANNER_INSTANCE=ccl-production
SPANNER_DATABASE=ccl-main
STORAGE_BUCKET=ccl-analysis-artifacts
STORAGE_BUCKET_NAME=ccl-analysis-${GCP_PROJECT_ID}
PUBSUB_TOPIC=analysis-events

# Vertex AI Configuration
VERTEX_AI_LOCATION=us-central1
GEMINI_MODEL_NAME=gemini-2.5-pro
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_OUTPUT_TOKENS=8192
GEMINI_TOP_P=0.95
GEMINI_TOP_K=40

# Authentication (use Secret Manager in production)
ENABLE_AUTH=true
# JWT_SECRET should be stored in Secret Manager

# Logging and Monitoring
RUST_LOG=info,analysis_engine=info,tower_http=info
RUST_BACKTRACE=1
ENABLE_TRACING=true
ENABLE_METRICS=true
LOG_LEVEL=info

# Analysis Configuration
MAX_CONCURRENT_ANALYSES=50
MAX_REPOSITORY_SIZE_GB=10
ANALYSIS_TIMEOUT_SECONDS=300
MAX_FILE_SIZE_MB=50
TEMP_DIR=/tmp/ccl-analysis

# Performance Configuration
THREAD_POOL_SIZE=8
CONNECTION_POOL_SIZE=50
CACHE_SIZE_MB=1024
BACKPRESSURE_THRESHOLD=0.8

# CORS Configuration (adjust for your domains)
CORS_ORIGINS=https://your-domain.com,https://api.your-domain.com

# Redis Configuration (if using Redis for caching)
# REDIS_URL=redis://your-redis-instance:6379

# Circuit Breaker Configuration
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_SUCCESS_THRESHOLD=3
CIRCUIT_BREAKER_TIMEOUT_SECONDS=60

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_BURST_SIZE=100

# Security Configuration
SECURITY_SCAN_ENABLED=true
SECURITY_SCAN_TIMEOUT_SECONDS=120
MAX_AST_DEPTH=100

# Database Connection Configuration
SPANNER_CONNECTION_TIMEOUT_SECONDS=30
SPANNER_MAX_SESSIONS=100
SPANNER_MIN_SESSIONS=10

# Observability
ENABLE_OPENTELEMETRY=true
OTEL_EXPORTER_OTLP_ENDPOINT=https://your-otel-collector:4317
OTEL_SERVICE_NAME=analysis-engine
OTEL_RESOURCE_ATTRIBUTES=service.name=analysis-engine,service.version=${SERVICE_VERSION}

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_PATTERN_MINING=true
ENABLE_SECURITY_INTELLIGENCE=true
ENABLE_SEMANTIC_SEARCH=true

# Cloud Run specific
# These are set automatically by Cloud Run:
# - PORT
# - K_SERVICE
# - K_REVISION
# - K_CONFIGURATION