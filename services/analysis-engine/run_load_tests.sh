#!/bin/bash

# Analysis Engine Load Testing Script
# Validates performance targets: <5min for 1M LOC, <100ms API responses

set -e

# Configuration
SERVICE_URL="${SERVICE_URL:-http://localhost:8001}"
LOAD_TEST_DIR="tests/load"
RESULTS_DIR="load_test_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="${RESULTS_DIR}/load_test_report_${TIMESTAMP}.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Performance targets
TARGET_P95_RESPONSE_TIME=100  # milliseconds
TARGET_SUCCESS_RATE=99.5     # percentage
TARGET_CONCURRENT_ANALYSES=100
TARGET_1M_LOC_TIME=300       # seconds (5 minutes)

echo -e "${BLUE}🚀 Analysis Engine Load Testing${NC}"
echo "=================================="
echo "Service URL: $SERVICE_URL"
echo "Timestamp: $TIMESTAMP"
echo ""

# Check prerequisites
check_prerequisites() {
    echo -e "${BLUE}📋 Checking prerequisites...${NC}"
    
    # Check if Artillery is installed
    if ! command -v artillery &> /dev/null; then
        echo -e "${RED}❌ Artillery not found. Installing...${NC}"
        npm install -g artillery
    else
        echo -e "${GREEN}✅ Artillery found: $(artillery version)${NC}"
    fi
    
    # Check if service is running
    if ! curl -s "$SERVICE_URL/health" > /dev/null; then
        echo -e "${RED}❌ Service not responding at $SERVICE_URL${NC}"
        echo "Please start the Analysis Engine service first:"
        echo "  cargo run --release"
        exit 1
    else
        echo -e "${GREEN}✅ Service is running at $SERVICE_URL${NC}"
    fi
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    echo -e "${GREEN}✅ Results directory created: $RESULTS_DIR${NC}"
    echo ""
}

# Run benchmark tests first
run_benchmarks() {
    echo -e "${BLUE}🏃 Running Rust benchmarks...${NC}"
    
    if cargo bench --bench analysis_bench > "${RESULTS_DIR}/benchmark_${TIMESTAMP}.txt" 2>&1; then
        echo -e "${GREEN}✅ Benchmarks completed${NC}"
        
        # Extract key metrics from benchmark results
        local bench_file="${RESULTS_DIR}/benchmark_${TIMESTAMP}.txt"
        if [ -f "$bench_file" ]; then
            echo "Key benchmark results:"
            grep -E "(parse_file|detect_patterns|parse_50_files)" "$bench_file" | head -10
        fi
    else
        echo -e "${YELLOW}⚠️  Benchmarks failed or not available${NC}"
    fi
    echo ""
}

# Run Artillery load tests
run_load_tests() {
    echo -e "${BLUE}🎯 Running Artillery load tests...${NC}"
    
    local config_file="${LOAD_TEST_DIR}/analysis-api.yaml"
    
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}❌ Load test configuration not found: $config_file${NC}"
        exit 1
    fi
    
    # Update target URL in config
    sed -i.bak "s|target: \".*\"|target: \"$SERVICE_URL\"|" "$config_file"
    
    echo "Running load test with configuration:"
    echo "  - Warm-up: 30s @ 1 req/s"
    echo "  - Ramp-up: 60s @ 10 req/s"
    echo "  - Sustained: 120s @ 100 req/s"
    echo ""
    
    # Run Artillery with JSON output
    if artillery run "$config_file" --output "$REPORT_FILE" > "${RESULTS_DIR}/artillery_output_${TIMESTAMP}.txt" 2>&1; then
        echo -e "${GREEN}✅ Load test completed${NC}"
    else
        echo -e "${RED}❌ Load test failed${NC}"
        cat "${RESULTS_DIR}/artillery_output_${TIMESTAMP}.txt"
        exit 1
    fi
    
    # Restore original config
    mv "${config_file}.bak" "$config_file"
    echo ""
}

# Analyze results and validate performance targets
analyze_results() {
    echo -e "${BLUE}📊 Analyzing results...${NC}"
    
    if [ ! -f "$REPORT_FILE" ]; then
        echo -e "${RED}❌ Results file not found: $REPORT_FILE${NC}"
        return 1
    fi
    
    # Extract key metrics using jq
    local total_requests=$(jq -r '.aggregate.counters."http.requests" // 0' "$REPORT_FILE")
    local successful_requests=$(jq -r '.aggregate.counters."http.codes.200" // 0' "$REPORT_FILE")
    local p95_response_time=$(jq -r '.aggregate.latency.p95 // 0' "$REPORT_FILE")
    local mean_response_time=$(jq -r '.aggregate.latency.mean // 0' "$REPORT_FILE")
    local error_rate=$(jq -r '.aggregate.rates.error // 0' "$REPORT_FILE")
    
    # Calculate success rate
    local success_rate=0
    if [ "$total_requests" -gt 0 ]; then
        success_rate=$(echo "scale=2; ($successful_requests * 100) / $total_requests" | bc -l)
    fi
    
    echo "Performance Results:"
    echo "==================="
    echo "Total Requests: $total_requests"
    echo "Successful Requests: $successful_requests"
    echo "Success Rate: ${success_rate}%"
    echo "P95 Response Time: ${p95_response_time}ms"
    echo "Mean Response Time: ${mean_response_time}ms"
    echo "Error Rate: ${error_rate}%"
    echo ""
    
    # Validate against targets
    local all_passed=true
    
    echo "Target Validation:"
    echo "=================="
    
    # Check P95 response time
    if (( $(echo "$p95_response_time <= $TARGET_P95_RESPONSE_TIME" | bc -l) )); then
        echo -e "${GREEN}✅ P95 Response Time: ${p95_response_time}ms <= ${TARGET_P95_RESPONSE_TIME}ms${NC}"
    else
        echo -e "${RED}❌ P95 Response Time: ${p95_response_time}ms > ${TARGET_P95_RESPONSE_TIME}ms${NC}"
        all_passed=false
    fi
    
    # Check success rate
    if (( $(echo "$success_rate >= $TARGET_SUCCESS_RATE" | bc -l) )); then
        echo -e "${GREEN}✅ Success Rate: ${success_rate}% >= ${TARGET_SUCCESS_RATE}%${NC}"
    else
        echo -e "${RED}❌ Success Rate: ${success_rate}% < ${TARGET_SUCCESS_RATE}%${NC}"
        all_passed=false
    fi
    
    # Check error rate (should be low)
    if (( $(echo "$error_rate <= 0.5" | bc -l) )); then
        echo -e "${GREEN}✅ Error Rate: ${error_rate}% <= 0.5%${NC}"
    else
        echo -e "${RED}❌ Error Rate: ${error_rate}% > 0.5%${NC}"
        all_passed=false
    fi
    
    echo ""
    
    if [ "$all_passed" = true ]; then
        echo -e "${GREEN}🎉 All performance targets met!${NC}"
        return 0
    else
        echo -e "${RED}⚠️  Some performance targets not met${NC}"
        return 1
    fi
}

# Test 1M LOC analysis performance
test_large_repository() {
    echo -e "${BLUE}🏗️  Testing 1M LOC analysis performance...${NC}"
    
    # This would require a large test repository
    # For now, we'll simulate with multiple smaller analyses
    echo "Note: 1M LOC test requires large repository setup"
    echo "Current test simulates with concurrent smaller analyses"
    
    # Create a test payload for concurrent analyses
    local test_payload='{
        "repository_url": "https://github.com/rust-lang/rust-clippy",
        "branch": "main",
        "enable_patterns": true,
        "enable_embeddings": false,
        "languages": ["rust"]
    }'
    
    echo "Starting concurrent analysis test..."
    local start_time=$(date +%s)
    
    # Start multiple analyses concurrently (simulating 1M LOC workload)
    local pids=()
    for i in {1..10}; do
        (
            curl -s -X POST "$SERVICE_URL/api/v1/analyze" \
                -H "Content-Type: application/json" \
                -H "x-api-key: test-api-key-123" \
                -d "$test_payload" > /dev/null
        ) &
        pids+=($!)
    done
    
    # Wait for all to complete
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "Concurrent analysis test completed in ${duration}s"
    
    if [ "$duration" -le "$TARGET_1M_LOC_TIME" ]; then
        echo -e "${GREEN}✅ Large repository performance: ${duration}s <= ${TARGET_1M_LOC_TIME}s${NC}"
    else
        echo -e "${YELLOW}⚠️  Large repository performance: ${duration}s > ${TARGET_1M_LOC_TIME}s${NC}"
        echo "Note: This is a simulation. Actual 1M LOC test needed for validation."
    fi
    echo ""
}

# Generate summary report
generate_report() {
    echo -e "${BLUE}📝 Generating summary report...${NC}"
    
    local summary_file="${RESULTS_DIR}/load_test_summary_${TIMESTAMP}.md"
    
    cat > "$summary_file" << EOF
# Load Test Summary - $(date)

## Test Configuration
- Service URL: $SERVICE_URL
- Test Duration: 210s (30s warm-up + 60s ramp-up + 120s sustained)
- Peak Load: 100 req/s
- Scenarios: Analysis Workflow (60%), Quick Pattern Detection (30%), Health Check (10%)

## Results Files
- Artillery Report: $(basename "$REPORT_FILE")
- Benchmark Results: benchmark_${TIMESTAMP}.txt
- Artillery Output: artillery_output_${TIMESTAMP}.txt

## Performance Targets
- P95 Response Time: <${TARGET_P95_RESPONSE_TIME}ms
- Success Rate: >${TARGET_SUCCESS_RATE}%
- 1M LOC Analysis: <${TARGET_1M_LOC_TIME}s
- Concurrent Analyses: ${TARGET_CONCURRENT_ANALYSES}+

## Next Steps
1. Review detailed results in JSON report
2. Monitor production metrics
3. Tune configuration based on results
4. Schedule regular load testing

EOF
    
    echo -e "${GREEN}✅ Summary report generated: $summary_file${NC}"
}

# Main execution
main() {
    check_prerequisites
    run_benchmarks
    run_load_tests
    
    if analyze_results; then
        test_large_repository
        generate_report
        echo -e "${GREEN}🎉 Load testing completed successfully!${NC}"
        exit 0
    else
        generate_report
        echo -e "${RED}⚠️  Load testing completed with issues${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
