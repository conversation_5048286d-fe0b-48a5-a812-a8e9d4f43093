#!/bin/bash
set -euo pipefail

# ============================================
# Dockerfile Validation Script
# Ensures all Tree-sitter parsers are included
# ============================================

echo "🔍 Validating Dockerfile configuration..."

# Check if Dockerfile.optimized exists
if [ ! -f "Dockerfile.optimized" ]; then
    echo "❌ Dockerfile.optimized not found!"
    exit 1
fi

# Extract Tree-sitter dependencies from Cargo.toml
echo "📦 Checking Tree-sitter language parsers..."

LANGUAGES=$(grep "tree-sitter-" Cargo.toml | grep -v "tree-sitter = " | cut -d'"' -f2 | sort)
LANGUAGE_COUNT=$(echo "$LANGUAGES" | wc -l)

echo "Found $LANGUAGE_COUNT language parsers in Cargo.toml:"
echo "$LANGUAGES" | while read -r lang; do
    echo "  ✓ $lang"
done

# Test Docker build
echo -e "\n🐳 Testing Docker build..."

# Build test image
docker build -f Dockerfile.optimized -t analysis-engine-test:validate . || {
    echo "❌ Docker build failed!"
    exit 1
}

# Check image size
IMAGE_SIZE=$(docker image inspect analysis-engine-test:validate --format='{{.Size}}' | numfmt --to=iec-i)
echo "📏 Image size: $IMAGE_SIZE"

# Calculate size in MB for comparison
IMAGE_SIZE_MB=$(docker image inspect analysis-engine-test:validate --format='{{.Size}}' | awk '{print $1/1024/1024}')
if (( $(echo "$IMAGE_SIZE_MB > 500" | bc -l) )); then
    echo "⚠️  Warning: Image size exceeds 500MB target"
else
    echo "✅ Image size is optimized"
fi

# Test container startup
echo -e "\n🚀 Testing container startup..."

# Run container
CONTAINER_ID=$(docker run -d \
    -p 8080:8080 \
    -e PORT=8080 \
    -e RUST_LOG=info \
    -e GCP_PROJECT_ID=test-project \
    -e SPANNER_INSTANCE=test-instance \
    -e SPANNER_DATABASE=test-db \
    analysis-engine-test:validate)

# Wait for startup
echo "Waiting for container to start..."
sleep 5

# Check if container is still running
if docker ps | grep -q $CONTAINER_ID; then
    echo "✅ Container started successfully"
    
    # Test health endpoint
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ Health check passed"
    else
        echo "❌ Health check failed"
        docker logs $CONTAINER_ID
    fi
else
    echo "❌ Container failed to start"
    docker logs $CONTAINER_ID
fi

# Cleanup
docker stop $CONTAINER_ID > /dev/null 2>&1
docker rm $CONTAINER_ID > /dev/null 2>&1

# Security scan with Trivy
echo -e "\n🔒 Running security scan..."
if command -v trivy &> /dev/null; then
    trivy image --severity HIGH,CRITICAL analysis-engine-test:validate
else
    echo "⚠️  Trivy not installed, skipping security scan"
    echo "   Install with: brew install aquasecurity/trivy/trivy"
fi

# Check Dockerfile best practices
echo -e "\n📋 Checking Dockerfile best practices..."

# Check for USER directive
if grep -q "USER nonroot" Dockerfile.optimized; then
    echo "✅ Non-root user configured"
else
    echo "❌ Missing non-root user configuration"
fi

# Check for multi-stage build
STAGES=$(grep -c "^FROM" Dockerfile.optimized)
if [ $STAGES -ge 2 ]; then
    echo "✅ Multi-stage build ($STAGES stages)"
else
    echo "❌ Single-stage build detected"
fi

# Check for distroless base
if grep -q "distroless" Dockerfile.optimized; then
    echo "✅ Using distroless base image"
else
    echo "⚠️  Not using distroless base image"
fi

# Check for build optimizations
if grep -q "strip" Dockerfile.optimized; then
    echo "✅ Binary stripping enabled"
else
    echo "⚠️  Binary stripping not enabled"
fi

echo -e "\n✨ Dockerfile validation complete!"

# Cleanup test image
docker rmi analysis-engine-test:validate > /dev/null 2>&1

# Summary
echo -e "\n📊 Summary:"
echo "- Language parsers: $LANGUAGE_COUNT"
echo "- Image size: $IMAGE_SIZE"
echo "- Build stages: $STAGES"
echo "- Security: Non-root user with distroless base"