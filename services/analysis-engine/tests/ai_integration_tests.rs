#[cfg(test)]
mod ai_integration_tests {
    use analysis_engine::models::*;
    use analysis_engine::services::ai_test::AIServicesTest;
    use anyhow::Result;
    use std::collections::HashMap;

    // Test constants
    const MIN_PATTERN_DETECTION_ACCURACY: f64 = 0.85; // 85% accuracy target
    const MIN_CODE_QUALITY_CONFIDENCE: f64 = 0.80;
    const MAX_RESPONSE_TIME_MS: u128 = 5000; // 5 seconds max

    #[tokio::test]
    async fn test_pattern_detection_accuracy() -> Result<()> {
        let ai_services = AIServicesTest::new().await?;
        
        // Create test cases with known patterns
        let test_cases = vec![
            // Singleton pattern
            create_test_file_with_pattern(
                "singleton.rs",
                r#"
                pub struct Singleton {
                    data: String,
                }

                impl Singleton {
                    fn new() -> &'static Singleton {
                        static mut INSTANCE: Option<Singleton> = None;
                        unsafe {
                            INSTANCE.get_or_insert(Singleton {
                                data: "singleton".to_string(),
                            })
                        }
                    }
                }
                "#,
                PatternType::DesignPattern,
                "Singleton",
            ),
            // SQL injection vulnerability
            create_test_file_with_pattern(
                "vulnerable.py",
                r#"
                def get_user(user_id):
                    query = f"SELECT * FROM users WHERE id = {user_id}"
                    cursor.execute(query)
                    return cursor.fetchone()
                "#,
                PatternType::SecurityIssue,
                "SQL Injection",
            ),
            // Long method code smell
            create_test_file_with_pattern(
                "code_smell.js",
                &generate_long_method(100),
                PatternType::CodeSmell,
                "Long Method",
            ),
        ];

        let mut correct_detections = 0;
        let mut total_tests = test_cases.len();

        for (analysis, expected_type, expected_name) in test_cases {
            let start_time = std::time::Instant::now();
            let patterns = ai_services.ai_pattern_detector
                .detect_ai_patterns(&[analysis])
                .await?;
            let elapsed = start_time.elapsed();

            assert!(
                elapsed.as_millis() < MAX_RESPONSE_TIME_MS,
                "Pattern detection took too long: {}ms",
                elapsed.as_millis()
            );

            // Check if the expected pattern was detected
            let found = patterns.iter().any(|p| {
                matches!(p.pattern_type, expected_type) &&
                p.description.as_ref()
                    .map(|d| d.contains(expected_name))
                    .unwrap_or(false)
            });

            if found {
                correct_detections += 1;
            } else {
                eprintln!(
                    "Failed to detect {} pattern in {}",
                    expected_name,
                    analysis.path
                );
            }
        }

        let accuracy = correct_detections as f64 / total_tests as f64;
        assert!(
            accuracy >= MIN_PATTERN_DETECTION_ACCURACY,
            "Pattern detection accuracy {} is below target {}",
            accuracy,
            MIN_PATTERN_DETECTION_ACCURACY
        );

        println!("Pattern detection accuracy: {:.2}%", accuracy * 100.0);
        Ok(())
    }

    #[tokio::test]
    async fn test_code_quality_six_dimensions() -> Result<()> {
        let ai_services = AIServicesTest::new().await?;
        
        // Create test files with varying quality
        let test_files = vec![
            create_high_quality_file(),
            create_medium_quality_file(),
            create_low_quality_file(),
        ];

        let metrics = RepositoryMetrics {
            total_files: test_files.len(),
            total_lines: 500,
            total_complexity: 30,
            average_complexity: Some(10.0),
            maintainability_score: Some(75.0),
            technical_debt_minutes: Some(180),
            test_coverage_estimate: Some(60.0),
        };

        let start_time = std::time::Instant::now();
        let assessment = ai_services.code_quality_assessor
            .assess_code_quality(&test_files, &metrics)
            .await?;
        let elapsed = start_time.elapsed();

        assert!(
            elapsed.as_millis() < MAX_RESPONSE_TIME_MS,
            "Code quality assessment took too long: {}ms",
            elapsed.as_millis()
        );

        // Verify all six dimensions are assessed
        assert!(assessment.maintainability_score > 0.0 && assessment.maintainability_score <= 100.0);
        assert!(assessment.readability_score > 0.0 && assessment.readability_score <= 100.0);
        assert!(assessment.testability_score > 0.0 && assessment.testability_score <= 100.0);
        assert!(assessment.modularity_score > 0.0 && assessment.modularity_score <= 100.0);
        assert!(assessment.security_score > 0.0 && assessment.security_score <= 100.0);
        assert!(assessment.performance_score > 0.0 && assessment.performance_score <= 100.0);

        // Verify confidence is reasonable
        assert!(
            assessment.confidence >= MIN_CODE_QUALITY_CONFIDENCE,
            "Code quality confidence {} is below minimum {}",
            assessment.confidence,
            MIN_CODE_QUALITY_CONFIDENCE
        );

        println!("Code quality assessment completed:");
        println!("  Overall score: {:.2}", assessment.overall_score);
        println!("  Confidence: {:.2}", assessment.confidence);

        Ok(())
    }

    #[tokio::test]
    async fn test_semantic_search_relevance() -> Result<()> {
        let ai_services = AIServicesTest::new().await?;
        
        // Create test files with specific content
        let test_files = vec![
            create_file_with_content(
                "auth.rs",
                r#"
                pub fn authenticate_user(username: &str, password: &str) -> Result<User> {
                    // Authentication logic here
                    verify_password(username, password)
                }
                "#,
            ),
            create_file_with_content(
                "database.rs",
                r#"
                pub fn connect_to_database(url: &str) -> Result<Connection> {
                    // Database connection logic
                    PostgresConnection::new(url)
                }
                "#,
            ),
            create_file_with_content(
                "utils.rs",
                r#"
                pub fn format_date(date: DateTime<Utc>) -> String {
                    date.format("%Y-%m-%d").to_string()
                }
                "#,
            ),
        ];

        // Generate embeddings
        let embeddings = ai_services.embeddings_service
            .generate_enhanced_embeddings(&test_files)
            .await?;

        // Index files
        ai_services.semantic_search_service
            .index_files(&test_files, &embeddings)
            .await?;

        // Test search queries
        let test_queries = vec![
            ("authenticate user password", "auth.rs"),
            ("database connection", "database.rs"),
            ("format date time", "utils.rs"),
        ];

        let mut successful_searches = 0;
        
        for (query, expected_file) in test_queries {
            let search_query = crate::services::semantic_search::SemanticSearchQuery {
                query: query.to_string(),
                top_k: Some(3),
                ..Default::default()
            };

            let start_time = std::time::Instant::now();
            let results = ai_services.semantic_search_service
                .search(search_query)
                .await?;
            let elapsed = start_time.elapsed();

            assert!(
                elapsed.as_millis() < MAX_RESPONSE_TIME_MS,
                "Semantic search took too long: {}ms",
                elapsed.as_millis()
            );

            // Check if expected file is in top results
            if !results.results.is_empty() && 
               results.results[0].file_path.contains(expected_file) {
                successful_searches += 1;
            } else {
                eprintln!(
                    "Failed to find {} for query '{}'",
                    expected_file, query
                );
            }
        }

        let success_rate = successful_searches as f64 / test_queries.len() as f64;
        assert!(
            success_rate >= 0.80,
            "Semantic search success rate {} is too low",
            success_rate
        );

        println!("Semantic search success rate: {:.2}%", success_rate * 100.0);
        Ok(())
    }

    #[tokio::test]
    async fn test_fallback_mechanisms() -> Result<()> {
        let ai_services = AIServicesTest::new().await?;
        
        // Test with feature toggles disabled
        std::env::set_var("ENABLE_AI_PATTERN_DETECTION", "false");
        std::env::set_var("USE_FALLBACK_EMBEDDINGS", "true");
        
        let disabled_services = AIServicesTest::new().await?;
        
        let test_file = create_test_file_with_pattern(
            "test.rs",
            "fn main() {}",
            PatternType::CodeSmell,
            "Empty Function",
        );

        // Should not fail even with AI disabled
        let patterns = disabled_services.ai_pattern_detector
            .detect_ai_patterns(&[test_file.0.clone()])
            .await?;
        
        // Should use fallback detection
        assert_eq!(patterns.len(), 0, "Should return empty when AI is disabled");

        // Test fallback embeddings
        let embeddings = disabled_services.embeddings_service
            .generate_enhanced_embeddings(&[test_file.0])
            .await?;

        assert!(
            !embeddings.is_empty(),
            "Fallback embeddings should be generated"
        );
        assert_eq!(
            embeddings[0].model,
            "fallback-hash",
            "Should use fallback embedding model"
        );

        Ok(())
    }

    // Helper functions
    fn create_test_file_with_pattern(
        path: &str,
        content: &str,
        pattern_type: PatternType,
        pattern_name: &str,
    ) -> (FileAnalysis, PatternType, &str) {
        let analysis = FileAnalysis {
            path: path.to_string(),
            language: detect_language(path),
            content_hash: "test_hash".to_string(),
            size_bytes: Some(content.len()),
            ast: create_ast_from_content(content),
            metrics: FileMetrics {
                lines_of_code: content.lines().count(),
                total_lines: Some(content.lines().count()),
                complexity: 5,
                maintainability_index: 75.0,
                function_count: Some(1),
                class_count: Some(0),
                comment_ratio: Some(0.1),
            },
            chunks: None,
            symbols: None,
        };
        (analysis, pattern_type, pattern_name)
    }

    fn create_file_with_content(path: &str, content: &str) -> FileAnalysis {
        FileAnalysis {
            path: path.to_string(),
            language: detect_language(path),
            content_hash: format!("hash_{}", path),
            size_bytes: Some(content.len()),
            ast: create_ast_from_content(content),
            metrics: FileMetrics {
                lines_of_code: content.lines().count(),
                total_lines: Some(content.lines().count()),
                complexity: 2,
                maintainability_index: 85.0,
                function_count: Some(1),
                class_count: Some(0),
                comment_ratio: Some(0.2),
            },
            chunks: None,
            symbols: None,
        }
    }

    fn create_high_quality_file() -> FileAnalysis {
        create_file_with_content(
            "high_quality.rs",
            r#"
            /// Well-documented function with clear purpose
            /// 
            /// # Arguments
            /// * `input` - The input string to process
            /// 
            /// # Returns
            /// The processed result
            pub fn process_data(input: &str) -> Result<String> {
                // Clear, concise implementation
                validate_input(input)?;
                let processed = transform(input);
                Ok(processed)
            }

            #[cfg(test)]
            mod tests {
                use super::*;

                #[test]
                fn test_process_data() {
                    assert_eq!(process_data("test").unwrap(), "processed");
                }
            }
            "#,
        )
    }

    fn create_medium_quality_file() -> FileAnalysis {
        create_file_with_content(
            "medium_quality.js",
            r#"
            function processData(input) {
                // Some validation
                if (!input) throw new Error("Invalid input");
                
                let result = input;
                // Transform data
                result = result.toUpperCase();
                result = result.trim();
                
                return result;
            }
            "#,
        )
    }

    fn create_low_quality_file() -> FileAnalysis {
        create_file_with_content(
            "low_quality.py",
            r#"
            def process(x):
                y = x
                z = y + y
                a = z * 2
                b = a / 2
                c = b - y
                return c
            "#,
        )
    }

    fn generate_long_method(lines: usize) -> String {
        let mut method = String::from("function veryLongMethod() {\n");
        for i in 0..lines {
            method.push_str(&format!("    var x{} = {};\n", i, i));
        }
        method.push_str("}\n");
        method
    }

    fn detect_language(path: &str) -> String {
        match path.split('.').last() {
            Some("rs") => "rust",
            Some("js") => "javascript",
            Some("py") => "python",
            _ => "unknown",
        }.to_string()
    }

    fn create_ast_from_content(content: &str) -> AstNode {
        AstNode {
            node_type: "source_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: vec![],
            properties: None,
            text: Some(content.to_string()),
        }
    }
}