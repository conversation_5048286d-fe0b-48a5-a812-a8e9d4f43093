use analysis_engine::backpressure::{BackpressureManager, BackpressureConfig, BackpressureDecision, BackpressureReason};
use analysis_engine::storage::CacheManager;
use analysis_engine::models::*;
use analysis_engine::api::errors::{ErrorResponse, ErrorType};

use std::time::Duration;
use uuid::Uuid;

#[tokio::test]
async fn test_backpressure_manager_creation() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Test that manager is created successfully
    let metrics = manager.get_metrics().await;
    assert_eq!(metrics.memory_usage_mb, 0);
    assert_eq!(metrics.cpu_usage_percent, 0.0);
}

#[tokio::test]
async fn test_analysis_request_allowed() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Should allow request under normal conditions
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Allow));
}

#[tokio::test]
async fn test_memory_pressure_rejection() {
    let mut config = BackpressureConfig::default();
    config.memory_threshold_mb = 100; // Low threshold for testing
    let manager = BackpressureManager::new(config);
    
    // Simulate high memory usage
    let high_memory_metrics = analysis_engine::backpressure::BackpressureMetrics {
        memory_usage_mb: 200, // Above threshold
        cpu_usage_percent: 50.0,
        active_analyses: 5,
        active_requests: 5,
        queued_requests: 10,
        rejected_requests: 0,
        avg_response_time_ms: 100,
        last_updated: 0,
    };
    
    manager.update_metrics(high_memory_metrics).await.unwrap();
    
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Reject(BackpressureReason::MemoryPressure)));
}

#[tokio::test]
async fn test_cache_manager_without_redis() {
    // Test cache manager behavior when Redis is not available
    let cache = CacheManager::new_without_redis();
    
    // Test getting non-existent analysis
    let result = cache.get_analysis("non-existent").await;
    assert!(result.is_ok());
    assert!(result.unwrap().is_none());
    
    // Test setting and getting analysis
    let analyses = vec![create_test_file_analysis()];
    let set_result = cache.set_analysis("test-id", &analyses).await;
    assert!(set_result.is_ok());
    
    let get_result = cache.get_analysis("test-id").await;
    assert!(get_result.is_ok());
    // Without Redis, this will return None as it falls back to no-op
    assert!(get_result.unwrap().is_none());
}

#[tokio::test]
async fn test_error_response_creation() {
    let error_response = ErrorResponse::validation_error("Invalid input".to_string());
    
    assert_eq!(error_response.error_type, ErrorType::Validation);
    assert!(!error_response.message.is_empty());
    assert!(error_response.timestamp < chrono::Utc::now());
}

#[tokio::test]
async fn test_error_response_types() {
    let validation_error = ErrorResponse::validation_error("Validation failed".to_string());
    assert_eq!(validation_error.error_type, ErrorType::Validation);

    let internal_error = ErrorResponse::internal_error("Internal error".to_string());
    assert_eq!(internal_error.error_type, ErrorType::Internal);

    let not_found_error = ErrorResponse::not_found_error("Resource not found".to_string());
    assert_eq!(not_found_error.error_type, ErrorType::NotFound);

    let rate_limit_error = ErrorResponse::rate_limit_error("Rate limit exceeded".to_string(), Some(60));
    assert_eq!(rate_limit_error.error_type, ErrorType::RateLimit);
}

#[tokio::test]
async fn test_analysis_warning_creation() {
    let warning = AnalysisWarning {
        warning_type: WarningType::ParseError,
        code: "PARSE_001".to_string(),
        message: "Parse error occurred".to_string(),
        file_path: Some("src/main.rs".to_string()),
        line_number: Some(42),
        column_number: Some(10),
        severity: WarningSeverity::High,
        timestamp: chrono::Utc::now(),
        context: None,
    };
    
    assert_eq!(warning.warning_type, WarningType::ParseError);
    assert_eq!(warning.severity, WarningSeverity::High);
    assert!(!warning.message.is_empty());
}

#[tokio::test]
async fn test_file_analysis_validation() {
    let mut file_analysis = create_test_file_analysis();
    
    // Test valid file analysis
    assert!(!file_analysis.path.is_empty());
    assert!(!file_analysis.language.is_empty());
    assert!(!file_analysis.content_hash.is_empty());
    
    // Test with invalid data
    file_analysis.path = String::new();
    // Should still be valid structurally, just with empty path
    assert!(file_analysis.path.is_empty());
}

#[tokio::test]
async fn test_metrics_calculation() {
    let file_analysis = create_test_file_analysis();
    
    // Test metrics are properly set
    assert!(file_analysis.metrics.lines_of_code > 0);
    assert!(file_analysis.metrics.complexity > 0);
    assert!(file_analysis.metrics.maintainability_index >= 0.0);
    assert!(file_analysis.metrics.maintainability_index <= 100.0);
}

#[tokio::test]
async fn test_progress_update_creation() {
    let progress = ProgressUpdate {
        analysis_id: "test-123".to_string(),
        progress: 0.5,
        stage: "parsing".to_string(),
        message: Some("Processing files".to_string()),
        timestamp: chrono::Utc::now(),
        files_processed: Some(50),
        total_files: Some(100),
    };
    
    assert_eq!(progress.analysis_id, "test-123");
    assert_eq!(progress.progress, 0.5);
    assert_eq!(progress.stage, "parsing");
}

#[tokio::test]
async fn test_circuit_breaker_functionality() {
    let config = BackpressureConfig {
        circuit_breaker_failure_threshold: 2,
        circuit_breaker_timeout: Duration::from_millis(100),
        ..Default::default()
    };
    let manager = BackpressureManager::new(config);
    
    // Record failures to trigger circuit breaker
    manager.record_failure("database").await;
    manager.record_failure("database").await;
    
    // Circuit breaker should be open now
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Reject(BackpressureReason::CircuitBreakerOpen(_))));
    
    // Wait for circuit breaker timeout
    tokio::time::sleep(Duration::from_millis(150)).await;
    
    // Should transition to half-open and allow requests
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Allow));
    
    // Record success to close circuit breaker
    manager.record_success("database").await;
    
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Allow));
}

#[tokio::test]
async fn test_permit_acquisition() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Test acquiring analysis permit
    let permit = manager.acquire_analysis_permit().await;
    assert!(permit.is_ok());
    
    // Test acquiring parsing permit
    let permit = manager.acquire_parsing_permit().await;
    assert!(permit.is_ok());
    
    // Test acquiring database permit
    let permit = manager.acquire_database_permit().await;
    assert!(permit.is_ok());
    
    // Test acquiring storage permit
    let permit = manager.acquire_storage_permit().await;
    assert!(permit.is_ok());
}

#[tokio::test]
async fn test_analysis_result_serialization() {
    let analysis_result = create_test_analysis_result();
    
    // Test JSON serialization
    let json_result = serde_json::to_string(&analysis_result);
    assert!(json_result.is_ok());
    
    let json_str = json_result.unwrap();
    assert!(!json_str.is_empty());
    
    // Test deserialization
    let deserialized: Result<AnalysisResult, _> = serde_json::from_str(&json_str);
    assert!(deserialized.is_ok());
    
    let deserialized_result = deserialized.unwrap();
    assert_eq!(deserialized_result.id, analysis_result.id);
    assert_eq!(deserialized_result.repository_url, analysis_result.repository_url);
}

#[tokio::test]
async fn test_error_serialization() {
    let error_response = ErrorResponse::validation_error("Test error".to_string());
    
    // Test JSON serialization
    let json_result = serde_json::to_string(&error_response);
    assert!(json_result.is_ok());
    
    let json_str = json_result.unwrap();
    assert!(!json_str.is_empty());
    assert!(json_str.contains("validation"));
    assert!(json_str.contains("Test error"));

    // Test deserialization
    let deserialized: Result<ErrorResponse, _> = serde_json::from_str(&json_str);
    assert!(deserialized.is_ok());

    let deserialized_error = deserialized.unwrap();
    assert_eq!(deserialized_error.error_type, ErrorType::Validation);
    assert_eq!(deserialized_error.message, "Test error");
}

// Helper functions
fn create_test_file_analysis() -> FileAnalysis {
    FileAnalysis {
        path: "src/main.rs".to_string(),
        language: "rust".to_string(),
        content_hash: "abc123".to_string(),
        size_bytes: Some(1024),
        ast: AstNode {
            node_type: "source_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 10, column: 0, byte: 1024 },
            },
            children: vec![],
            properties: None,
            text: None,
        },
        metrics: FileMetrics {
            lines_of_code: 100,
            total_lines: Some(120),
            complexity: 5,
            maintainability_index: 75.0,
            function_count: Some(3),
            class_count: Some(1),
            comment_ratio: Some(0.1),
        },
        chunks: Some(vec![]),
        symbols: Some(vec![]),
    }
}

fn create_test_analysis_result() -> AnalysisResult {
    AnalysisResult {
        id: Uuid::new_v4().to_string(),
        repository_url: "https://github.com/test/repo".to_string(),
        branch: "main".to_string(),
        commit_hash: Some("abc123".to_string()),
        repository_size_bytes: Some(1024),
        clone_time_ms: Some(1000),
        status: AnalysisStatus::Completed,
        started_at: chrono::Utc::now(),
        completed_at: Some(chrono::Utc::now()),
        duration_seconds: Some(60),
        progress: Some(100.0),
        current_stage: Some("Completed".to_string()),
        estimated_completion: None,
        metrics: Some(RepositoryMetrics {
            total_files: 1,
            total_lines: 120,
            total_complexity: 5,
            average_complexity: Some(5.0),
            maintainability_score: Some(75.0),
            technical_debt_minutes: Some(30),
            test_coverage_estimate: Some(0.8),
        }),
        patterns: vec![],
        languages: std::collections::HashMap::new(),
        embeddings: Some(vec![]),
        error_message: None,
        failed_files: vec![],
        successful_analyses: Some(vec![create_test_file_analysis()]),
        user_id: "user123".to_string(),
        webhook_url: None,
        file_count: 1,
        success_rate: 1.0,
        performance_metrics: Some(PerformanceMetrics::default()),
        warnings: vec![],
    }
}
