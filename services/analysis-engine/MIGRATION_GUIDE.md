# Database Migration Guide - Analysis Engine

## Overview

This guide provides comprehensive instructions for managing database migrations in the Analysis Engine service. The migration system is designed to be production-ready, safe, and atomic.

## Migration Files

### Current Migrations

1. **000_create_base_schema.sql** - Creates the initial schema
   - Creates `schema_migrations` table for tracking
   - Creates `analyses` table with basic structure
   - Creates essential indexes

2. **001_add_analysis_metadata.sql** - Adds metadata columns
   - Adds `commit_hash` for intelligent caching
   - Adds `repository_size_bytes` for performance tracking
   - Adds `clone_time_ms` for optimization metrics
   - Adds `warnings` for production monitoring
   - Creates performance-optimized indexes

3. **002_create_file_analyses_table.sql** - Creates file-level analysis storage
   - Creates `file_analyses` table with interleaving
   - Supports detailed AST, symbols, and metrics storage
   - Optimized for large-scale file analysis

4. **003_add_missing_indexes.sql** - Adds final indexes
   - Adds timestamp-based indexes for queries
   - Completes the index optimization

## Scripts

### 1. Migration Execution (`run_migrations.sh`)

Primary script for applying migrations in production.

```bash
# Run all pending migrations
./run_migrations.sh run

# Check migration status
./run_migrations.sh status

# Run with custom configuration
./run_migrations.sh -p ccl-production -i ccl-instance run

# Force continue on failures (for development)
./run_migrations.sh --force-continue run
```

**Features:**
- Automatic prerequisite validation
- Atomic migration execution
- Comprehensive error handling
- Schema backup before each migration
- Progress tracking and logging
- Safe rollback procedures

### 2. Migration Testing (`test_migrations.sh`)

Comprehensive testing script for validating migrations.

```bash
# Run all migration tests
./test_migrations.sh

# Test with custom database
./test_migrations.sh -d ccl_main_test

# Test with custom project
./test_migrations.sh -p ccl-development -i ccl-instance
```

**Test Coverage:**
- Individual migration validation
- Schema structure verification
- Data operation testing
- Index performance validation
- Rollback procedure testing
- End-to-end migration flow

### 3. Migration Rollback (`rollback_migration.sh`)

Safe rollback procedures for emergency situations.

```bash
# Rollback to specific version
./rollback_migration.sh rollback-to 001

# Rollback single migration
./rollback_migration.sh rollback-single 003

# Rollback with confirmation
./rollback_migration.sh -p ccl-production rollback-to 000
```

**Safety Features:**
- Confirmation prompts for destructive operations
- Automatic migration record cleanup
- Comprehensive logging
- Incremental rollback support

### 4. Index Validation (`validate_indexes.sh`)

Validates that all indexes are created correctly and performant.

```bash
# Validate all indexes
./validate_indexes.sh

# Validate with custom configuration
./validate_indexes.sh -p ccl-production -i ccl-instance
```

**Validation Checks:**
- Index existence verification
- Performance testing with sample queries
- Index statistics analysis
- Duplicate index detection
- JSON column index warnings

## Production Deployment Process

### Pre-deployment Checklist

1. **Environment Preparation**
   ```bash
   # Verify gcloud authentication
   gcloud auth list
   
   # Set project context
   gcloud config set project ccl-production
   
   # Verify Spanner instance access
   gcloud spanner instances list
   ```

2. **Backup Current Schema**
   ```bash
   # Create schema backup
   gcloud spanner databases ddl describe ccl_main \
     --instance=ccl-instance \
     --project=ccl-production \
     --format="value(statements)" > schema_backup_$(date +%Y%m%d_%H%M%S).sql
   ```

3. **Test in Development**
   ```bash
   # Run comprehensive tests
   ./test_migrations.sh -p ccl-development -d ccl_main_test
   
   # Validate indexes
   ./validate_indexes.sh -p ccl-development -d ccl_main_test
   ```

### Deployment Steps

1. **Check Migration Status**
   ```bash
   ./run_migrations.sh -p ccl-production -i ccl-instance status
   ```

2. **Apply Migrations**
   ```bash
   ./run_migrations.sh -p ccl-production -i ccl-instance run
   ```

3. **Validate Deployment**
   ```bash
   # Validate indexes
   ./validate_indexes.sh -p ccl-production -i ccl-instance
   
   # Test basic operations
   gcloud spanner databases execute-sql ccl_main \
     --instance=ccl-instance \
     --project=ccl-production \
     --sql="SELECT COUNT(*) FROM analyses"
   ```

4. **Application Deployment**
   ```bash
   # Deploy updated application code
   gcloud run deploy analysis-engine \
     --image=gcr.io/ccl-production/analysis-engine:latest \
     --platform=managed \
     --region=us-central1
   ```

### Post-deployment Validation

1. **Health Checks**
   ```bash
   # Check application health
   curl -f https://analysis-engine-dot-ccl-production.appspot.com/health
   
   # Verify database connectivity
   curl -f https://analysis-engine-dot-ccl-production.appspot.com/health/database
   ```

2. **Monitoring**
   - Monitor application logs for errors
   - Check Spanner metrics for performance
   - Verify index usage statistics

## Emergency Procedures

### Rollback Process

1. **Immediate Rollback**
   ```bash
   # Stop application traffic
   gcloud run services update analysis-engine \
     --no-traffic \
     --region=us-central1
   
   # Rollback database
   ./rollback_migration.sh -p ccl-production rollback-to 000
   
   # Deploy previous application version
   gcloud run deploy analysis-engine \
     --image=gcr.io/ccl-production/analysis-engine:previous \
     --platform=managed \
     --region=us-central1
   ```

2. **Gradual Recovery**
   ```bash
   # Restore traffic gradually
   gcloud run services update-traffic analysis-engine \
     --to-latest=10 \
     --region=us-central1
   
   # Monitor and increase traffic
   gcloud run services update-traffic analysis-engine \
     --to-latest=50 \
     --region=us-central1
   ```

## Schema Changes

### Adding New Columns

1. Create migration file with appropriate version number
2. Use nullable columns to avoid blocking existing data
3. Add appropriate indexes for query optimization
4. Update application code to handle new columns

### Modifying Existing Columns

⚠️ **Spanner Limitations:**
- Cannot change column types
- Cannot add NOT NULL constraints to existing columns
- Cannot modify primary key columns

**Recommended Approach:**
1. Add new column with desired type
2. Migrate data from old to new column
3. Update application to use new column
4. Drop old column in subsequent migration

### Adding Indexes

1. Create indexes concurrently when possible
2. Monitor index creation progress
3. Validate index performance after creation
4. Consider stored columns for covering indexes

## Best Practices

### Migration Development

1. **Small, Focused Migrations**
   - Each migration should address a single concern
   - Keep migrations small and testable
   - Use descriptive names and comments

2. **Backward Compatibility**
   - Ensure migrations don't break existing functionality
   - Use nullable columns for additions
   - Provide migration path for data changes

3. **Testing**
   - Test migrations in development environment
   - Validate performance impact
   - Test rollback procedures

### Production Deployment

1. **Timing**
   - Deploy during low-traffic periods
   - Allow sufficient time for migration execution
   - Have rollback plan ready

2. **Monitoring**
   - Monitor application logs during migration
   - Watch Spanner metrics for performance impact
   - Set up alerts for migration failures

3. **Communication**
   - Notify team of migration schedule
   - Document any expected downtime
   - Provide status updates during execution

## Troubleshooting

### Common Issues

1. **Migration Timeout**
   - Check Spanner quotas and limits
   - Consider breaking large migrations into smaller parts
   - Monitor resource usage during migration

2. **Index Creation Failures**
   - Verify syntax for Spanner compatibility
   - Check for JSON column indexing issues
   - Ensure sufficient resources for index creation

3. **Application Connectivity Issues**
   - Verify service account permissions
   - Check network connectivity
   - Validate connection string format

### Recovery Procedures

1. **Failed Migration**
   ```bash
   # Check migration logs
   tail -f migration_*.log
   
   # Verify current schema state
   gcloud spanner databases ddl describe ccl_main \
     --instance=ccl-instance \
     --project=ccl-production
   
   # Rollback if necessary
   ./rollback_migration.sh rollback-to [last_good_version]
   ```

2. **Index Performance Issues**
   ```bash
   # Analyze query performance
   gcloud spanner databases execute-sql ccl_main \
     --instance=ccl-instance \
     --project=ccl-production \
     --sql="SELECT * FROM SPANNER_SYS.QUERY_STATS_TOP_MINUTE"
   
   # Validate index usage
   ./validate_indexes.sh -p ccl-production
   ```

## Security Considerations

1. **Access Control**
   - Use least-privilege service accounts
   - Audit database access permissions
   - Rotate credentials regularly

2. **Data Protection**
   - Encrypt sensitive data in transit and at rest
   - Use VPC Service Controls for network security
   - Implement proper backup procedures

3. **Monitoring**
   - Enable audit logging for all database operations
   - Monitor for suspicious query patterns
   - Set up alerts for unusual activity

## Contact Information

For migration-related issues or questions:
- **Development Team:** #ccl-dev Slack channel
- **Production Issues:** #ccl-production Slack channel
- **Emergency:** Page on-call engineer through PagerDuty

## Appendix

### Environment Variables

```bash
# Required
PROJECT_ID=ccl-production
INSTANCE_ID=ccl-instance
DATABASE_ID=ccl_main

# Optional
MIGRATIONS_DIR=migrations
LOG_FILE=migration_$(date +%Y%m%d_%H%M%S).log
FORCE_CONTINUE=false
```

### Useful Commands

```bash
# Check Spanner instance status
gcloud spanner instances describe ccl-instance --project=ccl-production

# Monitor migration progress
watch -n 5 'gcloud spanner operations list --instance=ccl-instance --project=ccl-production'

# Check database size
gcloud spanner databases describe ccl_main --instance=ccl-instance --project=ccl-production

# View recent operations
gcloud spanner operations list --instance=ccl-instance --project=ccl-production --limit=10
```