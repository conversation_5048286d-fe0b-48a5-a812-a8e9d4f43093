#!/bin/bash

# Migration rollback script for Analysis Engine
# This script handles rollback of database migrations

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-ccl-development}"
INSTANCE_ID="${INSTANCE_ID:-ccl-instance}"
DATABASE_ID="${DATABASE_ID:-ccl_main}"
MIGRATIONS_DIR="${MIGRATIONS_DIR:-migrations}"
LOG_FILE="${LOG_FILE:-rollback_$(date +%Y%m%d_%H%M%S).log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

# Rollback definitions for each migration
rollback_003() {
    log_info "Rolling back migration 003: add missing indexes"
    
    local ddl="DROP INDEX IF EXISTS idx_file_analyses_created;"
    
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl="$ddl" \
        --format="text" 2>&1 | tee -a "$LOG_FILE"; then
        log_success "Migration 003 rolled back successfully."
        return 0
    else
        log_error "Failed to rollback migration 003."
        return 1
    fi
}

rollback_002() {
    log_info "Rolling back migration 002: create file_analyses table"
    
    local ddl="DROP INDEX IF EXISTS idx_file_analyses_created;
DROP INDEX IF EXISTS idx_file_analyses_embeddings;
DROP INDEX IF EXISTS idx_file_analyses_content_hash;
DROP INDEX IF EXISTS idx_file_analyses_size;
DROP INDEX IF EXISTS idx_file_analyses_complexity;
DROP INDEX IF EXISTS idx_file_analyses_language;
DROP INDEX IF EXISTS idx_file_analyses_path;
DROP TABLE IF EXISTS file_analyses;"
    
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl="$ddl" \
        --format="text" 2>&1 | tee -a "$LOG_FILE"; then
        log_success "Migration 002 rolled back successfully."
        return 0
    else
        log_error "Failed to rollback migration 002."
        return 1
    fi
}

rollback_001() {
    log_info "Rolling back migration 001: add analysis metadata"
    
    local ddl="DROP INDEX IF EXISTS idx_analyses_performance;
DROP INDEX IF EXISTS idx_analyses_repo_commit;
DROP INDEX IF EXISTS idx_analyses_commit_hash;
ALTER TABLE analyses DROP COLUMN IF EXISTS warnings;
ALTER TABLE analyses DROP COLUMN IF EXISTS clone_time_ms;
ALTER TABLE analyses DROP COLUMN IF EXISTS repository_size_bytes;
ALTER TABLE analyses DROP COLUMN IF EXISTS commit_hash;"
    
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl="$ddl" \
        --format="text" 2>&1 | tee -a "$LOG_FILE"; then
        log_success "Migration 001 rolled back successfully."
        return 0
    else
        log_error "Failed to rollback migration 001."
        return 1
    fi
}

rollback_000() {
    log_info "Rolling back migration 000: create base schema"
    
    local ddl="DROP INDEX IF EXISTS idx_analyses_completed_at;
DROP INDEX IF EXISTS idx_analyses_started_at;
DROP INDEX IF EXISTS idx_analyses_user_id;
DROP INDEX IF EXISTS idx_analyses_status;
DROP INDEX IF EXISTS idx_analyses_repository_url;
DROP TABLE IF EXISTS analyses;
DROP TABLE IF EXISTS schema_migrations;"
    
    if gcloud spanner databases ddl update "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --ddl="$ddl" \
        --format="text" 2>&1 | tee -a "$LOG_FILE"; then
        log_success "Migration 000 rolled back successfully."
        return 0
    else
        log_error "Failed to rollback migration 000."
        return 1
    fi
}

# Remove migration record
remove_migration_record() {
    local version="$1"
    
    log_info "Removing migration record for version $version"
    
    local sql="DELETE FROM schema_migrations WHERE version = '$version';"
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="$sql" &> /dev/null; then
        log_success "Migration record $version removed."
    else
        log_warn "Failed to remove migration record $version."
    fi
}

# Get applied migrations in reverse order
get_applied_migrations_reverse() {
    log_info "Getting applied migrations in reverse order..."
    
    if gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="SELECT version FROM schema_migrations ORDER BY version DESC" \
        --format="value(version)" 2>/dev/null; then
        return 0
    else
        log_warn "Could not retrieve applied migrations."
        return 1
    fi
}

# Rollback to specific version
rollback_to_version() {
    local target_version="$1"
    
    log_info "Rolling back to version $target_version"
    
    # Get applied migrations in reverse order
    local applied_file="applied_migrations_reverse.tmp"
    if ! get_applied_migrations_reverse > "$applied_file"; then
        log_error "Could not retrieve applied migrations."
        rm -f "$applied_file"
        return 1
    fi
    
    # Rollback each migration until we reach the target version
    local rollback_count=0
    while IFS= read -r version; do
        if [[ -z "$version" ]]; then
            continue
        fi
        
        # Stop if we've reached the target version
        if [[ "$version" == "$target_version" ]]; then
            log_info "Reached target version $target_version."
            break
        fi
        
        # Skip if this is below the target version
        if [[ "$version" < "$target_version" ]]; then
            log_info "Skipping version $version (below target)."
            continue
        fi
        
        log_info "Rolling back migration $version..."
        
        # Call the appropriate rollback function
        case "$version" in
            "003")
                if rollback_003; then
                    remove_migration_record "$version"
                    ((rollback_count++))
                else
                    log_error "Failed to rollback migration $version."
                    rm -f "$applied_file"
                    return 1
                fi
                ;;
            "002")
                if rollback_002; then
                    remove_migration_record "$version"
                    ((rollback_count++))
                else
                    log_error "Failed to rollback migration $version."
                    rm -f "$applied_file"
                    return 1
                fi
                ;;
            "001")
                if rollback_001; then
                    remove_migration_record "$version"
                    ((rollback_count++))
                else
                    log_error "Failed to rollback migration $version."
                    rm -f "$applied_file"
                    return 1
                fi
                ;;
            "000")
                if rollback_000; then
                    remove_migration_record "$version"
                    ((rollback_count++))
                else
                    log_error "Failed to rollback migration $version."
                    rm -f "$applied_file"
                    return 1
                fi
                ;;
            *)
                log_warn "No rollback procedure defined for migration $version."
                ;;
        esac
    done < "$applied_file"
    
    rm -f "$applied_file"
    
    if [[ "$rollback_count" -eq 0 ]]; then
        log_info "No migrations to rollback."
    else
        log_success "Successfully rolled back $rollback_count migrations."
    fi
    
    return 0
}

# Rollback single migration
rollback_single() {
    local version="$1"
    
    log_info "Rolling back single migration: $version"
    
    # Check if migration is applied
    if ! gcloud spanner databases execute-sql "$DATABASE_ID" \
        --instance="$INSTANCE_ID" \
        --project="$PROJECT_ID" \
        --sql="SELECT version FROM schema_migrations WHERE version = '$version'" \
        --format="value(version)" 2>/dev/null | grep -q "$version"; then
        log_warn "Migration $version is not applied."
        return 1
    fi
    
    # Call the appropriate rollback function
    case "$version" in
        "003")
            if rollback_003; then
                remove_migration_record "$version"
                log_success "Migration $version rolled back successfully."
            else
                log_error "Failed to rollback migration $version."
                return 1
            fi
            ;;
        "002")
            if rollback_002; then
                remove_migration_record "$version"
                log_success "Migration $version rolled back successfully."
            else
                log_error "Failed to rollback migration $version."
                return 1
            fi
            ;;
        "001")
            if rollback_001; then
                remove_migration_record "$version"
                log_success "Migration $version rolled back successfully."
            else
                log_error "Failed to rollback migration $version."
                return 1
            fi
            ;;
        "000")
            if rollback_000; then
                remove_migration_record "$version"
                log_success "Migration $version rolled back successfully."
            else
                log_error "Failed to rollback migration $version."
                return 1
            fi
            ;;
        *)
            log_error "No rollback procedure defined for migration $version."
            return 1
            ;;
    esac
    
    return 0
}

# Show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS] COMMAND

Migration rollback script for Analysis Engine.

COMMANDS:
    rollback-to VERSION     Rollback to a specific version (inclusive)
    rollback-single VERSION Rollback a single migration
    help                    Show this help message

OPTIONS:
    -p, --project PROJECT_ID     GCP project ID (default: ccl-development)
    -i, --instance INSTANCE_ID   Spanner instance ID (default: ccl-instance)
    -d, --database DATABASE_ID   Database ID (default: ccl_main)
    -l, --log-file FILE          Log file path (default: rollback_TIMESTAMP.log)
    -h, --help                   Show this help message

EXAMPLES:
    $0 rollback-to 001                           # Rollback to migration 001
    $0 rollback-single 003                       # Rollback only migration 003
    $0 -p my-project -i my-instance rollback-to 000  # Rollback with custom config

WARNING: 
    Rollbacks are destructive operations and may result in data loss.
    Always backup your database before performing rollbacks.
    Test rollbacks in a development environment first.

EOF
}

# Parse command line arguments
COMMAND=""
TARGET_VERSION=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--project)
            PROJECT_ID="$2"
            shift 2
            ;;
        -i|--instance)
            INSTANCE_ID="$2"
            shift 2
            ;;
        -d|--database)
            DATABASE_ID="$2"
            shift 2
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        rollback-to)
            COMMAND="rollback-to"
            TARGET_VERSION="$2"
            shift 2
            ;;
        rollback-single)
            COMMAND="rollback-single"
            TARGET_VERSION="$2"
            shift 2
            ;;
        help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate command and version
if [[ -z "$COMMAND" ]]; then
    log_error "No command specified."
    show_help
    exit 1
fi

if [[ -z "$TARGET_VERSION" && "$COMMAND" != "help" ]]; then
    log_error "No target version specified."
    show_help
    exit 1
fi

# Confirm destructive operation
if [[ "$COMMAND" == "rollback-to" || "$COMMAND" == "rollback-single" ]]; then
    log_warn "This is a destructive operation that may result in data loss."
    read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled by user."
        exit 0
    fi
fi

# Execute command
case "$COMMAND" in
    rollback-to)
        rollback_to_version "$TARGET_VERSION"
        ;;
    rollback-single)
        rollback_single "$TARGET_VERSION"
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        exit 1
        ;;
esac