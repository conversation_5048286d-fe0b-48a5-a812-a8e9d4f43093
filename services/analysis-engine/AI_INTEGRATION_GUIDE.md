# AI Integration Guide for Analysis Engine

## Overview
The Analysis Engine integrates with Google Vertex AI (Gemini 2.5 models) to provide advanced code analysis capabilities:
- **AI Pattern Detection** - Detects design patterns, anti-patterns, and code smells with 85%+ accuracy
- **Code Quality Assessment** - 6-dimensional quality scoring
- **Semantic Search** - AI-powered code search
- **Repository Insights** - Intelligent repository analysis

## Current Status
- ✅ Service operational on port 8001
- ✅ Health endpoint responding in ~1ms
- ✅ AI models configured (Gemini 2.5 Flash/Pro)
- ❌ **BLOCKED: IAM permissions required for Vertex AI access**

## Required IAM Permissions

### Service Account Requirements
The service account needs the following IAM roles in the `vibe-match-463114` project:

1. **roles/aiplatform.user** - For making predictions with Vertex AI models
2. **roles/serviceusage.serviceUsageConsumer** - For accessing Google Cloud APIs

### Quick Fix Commands
```bash
# Set your service account email
SERVICE_ACCOUNT_EMAIL="<EMAIL>"

# Grant required permissions
gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/serviceusage.serviceUsageConsumer"
```

## Testing AI Integration

### 1. Run the Diagnostic Script
```bash
cd services/analysis-engine
./test_ai_integration.sh
```

This script will:
- Check current IAM permissions
- Test Vertex AI API access
- Test Gemini 2.5 Flash/Pro models
- Test text-embedding-005 model
- Provide specific fix commands if permissions are missing

### 2. Run the Rust Integration Test
```bash
cd services/analysis-engine
cargo run --bin test_ai_services
```

This will test all AI services and report any permission issues.

### 3. Run Full Integration Tests
```bash
cd services/analysis-engine
cargo test --test ai_integration_tests -- --nocapture
```

## AI-Powered Endpoints

### Pattern Detection
**POST /api/v1/analysis/{id}/patterns**
- Uses Gemini 2.5 Flash for fast pattern detection
- Target: 85%+ accuracy
- Detects: Design patterns, anti-patterns, code smells, security issues, performance problems

### Code Quality Assessment
**GET /api/v1/analysis/{id}/metrics**
- Uses Gemini 2.5 Pro for comprehensive analysis
- Returns 6-dimension quality scores:
  - Maintainability
  - Readability
  - Testability
  - Modularity
  - Security
  - Performance

### Semantic Search
**POST /api/v1/search**
- Uses text-embedding-005 for vector embeddings
- 768-dimensional embeddings
- Supports natural language queries

## Configuration

### Environment Variables
```bash
# Gemini Models (July 2025 - Latest Stable)
GEMINI_MODEL_NAME=gemini-2.5-flash
GEMINI_PRO_MODEL_NAME=gemini-2.5-pro

# Feature Toggles
ENABLE_AI_PATTERN_DETECTION=true
ENABLE_CODE_QUALITY_ASSESSMENT=true
ENABLE_SEMANTIC_SEARCH=true
ENABLE_REPOSITORY_INSIGHTS=true
USE_FALLBACK_EMBEDDINGS=true
```

### Circuit Breakers
The service implements circuit breakers for resilience:
- Opens after 5 consecutive failures
- Resets after 60 seconds
- Falls back to traditional pattern detection when AI is unavailable

### Rate Limiting
- Gemini API: 60 requests/minute
- Embeddings API: 100 requests/minute
- Automatic exponential backoff implemented

## Fallback Mechanisms

When AI services are unavailable:
1. **Pattern Detection** - Falls back to AST-based pattern detection
2. **Embeddings** - Uses hash-based embeddings for basic functionality
3. **Code Quality** - Uses traditional metrics (complexity, maintainability index)

## Performance Targets

- Pattern Detection Accuracy: **85%+**
- Response Time: **<5 seconds** per analysis
- Concurrent Analyses: **50 max**
- Token Usage: ~2000-8000 tokens per file analysis

## Troubleshooting

### Common Issues

1. **403 Permission Denied**
   - Missing IAM roles
   - Run `./test_ai_integration.sh` for specific fix

2. **429 Rate Limit**
   - Too many requests
   - Circuit breaker will activate
   - Wait for automatic retry

3. **Service Unavailable**
   - Check if running on Cloud Run
   - Verify GOOGLE_APPLICATION_CREDENTIALS is set
   - Check network connectivity to Vertex AI

### Debug Commands
```bash
# Check service account permissions
gcloud projects get-iam-policy vibe-match-463114 \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:YOUR_SA_EMAIL"

# Test authentication
gcloud auth application-default print-access-token

# Check API enablement
gcloud services list --enabled --project=vibe-match-463114 | grep aiplatform
```

## Next Steps

1. **Grant IAM Permissions** - Run the commands in "Quick Fix" section
2. **Wait 1-2 minutes** - For permissions to propagate
3. **Run Tests** - Verify AI integration is working
4. **Monitor Metrics** - Check `/metrics` endpoint for AI usage stats
5. **Deploy to Cloud Run** - Service will use instance metadata for auth