use anyhow::{Context, Result};
use crate::models::{FileAnalysis, RepositoryMetrics};
use crate::services::embeddings_enhancement::{EnhancedEmbeddingsService, FeatureToggles};
use serde::{Deserialize, Serialize};
use reqwest::Client;
use std::env;
use std::time::Duration;
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

const GEMINI_TIMEOUT: Duration = Duration::from_secs(45);
const MAX_RETRIES: u32 = 3;
const INITIAL_RETRY_DELAY: Duration = Duration::from_millis(1000);

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeQualityAssessment {
    pub overall_score: f64,
    pub maintainability_score: f64,
    pub readability_score: f64,
    pub testability_score: f64,
    pub security_score: f64,
    pub performance_score: f64,
    pub architecture_score: f64,
    pub file_assessments: Vec<FileQualityAssessment>,
    pub insights: Vec<QualityInsight>,
    pub recommendations: Vec<QualityRecommendation>,
    pub metrics: QualityMetrics,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileQualityAssessment {
    pub file_path: String,
    pub language: String,
    pub overall_score: f64,
    pub maintainability_score: f64,
    pub readability_score: f64,
    pub testability_score: f64,
    pub security_score: f64,
    pub performance_score: f64,
    pub issues: Vec<QualityIssue>,
    pub strengths: Vec<String>,
    pub line_count: u32,
    pub complexity: u32,
    pub maintainability_index: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QualityInsight {
    pub category: String,
    pub title: String,
    pub description: String,
    pub severity: InsightSeverity,
    pub confidence: f64,
    pub affected_files: Vec<String>,
    pub metrics: HashMap<String, f64>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QualityRecommendation {
    pub priority: RecommendationPriority,
    pub category: String,
    pub title: String,
    pub description: String,
    pub implementation_effort: String,
    pub expected_impact: String,
    pub affected_files: Vec<String>,
    pub action_items: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QualityIssue {
    pub issue_type: String,
    pub severity: IssueSeverity,
    pub description: String,
    pub location: Option<IssueLocation>,
    pub suggestion: String,
    pub confidence: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct IssueLocation {
    pub start_line: u32,
    pub end_line: u32,
    pub start_column: u32,
    pub end_column: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QualityMetrics {
    pub total_files_analyzed: u32,
    pub total_lines_of_code: u32,
    pub average_complexity: f64,
    pub average_maintainability: f64,
    pub high_risk_files: u32,
    pub medium_risk_files: u32,
    pub low_risk_files: u32,
    pub technical_debt_hours: f64,
    pub test_coverage_estimate: f64,
    pub code_duplication_ratio: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum InsightSeverity {
    Info,
    Warning,
    Critical,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum RecommendationPriority {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum IssueSeverity {
    Info,
    Minor,
    Major,
    Critical,
}

// Gemini API structures
#[derive(Debug, Serialize)]
struct GeminiQualityRequest {
    contents: Vec<GeminiContent>,
    generation_config: GeminiGenerationConfig,
    safety_settings: Vec<GeminiSafetySettings>,
}

#[derive(Debug, Serialize)]
struct GeminiContent {
    parts: Vec<GeminiPart>,
    role: String,
}

#[derive(Debug, Serialize)]
struct GeminiPart {
    text: String,
}

#[derive(Debug, Serialize)]
struct GeminiGenerationConfig {
    temperature: f32,
    top_p: f32,
    top_k: i32,
    max_output_tokens: i32,
    response_mime_type: String,
}

#[derive(Debug, Serialize)]
struct GeminiSafetySettings {
    category: String,
    threshold: String,
}

#[derive(Debug, Deserialize)]
struct GeminiResponse {
    candidates: Vec<GeminiCandidate>,
}

#[derive(Debug, Deserialize)]
struct GeminiCandidate {
    content: GeminiResponseContent,
}

#[derive(Debug, Deserialize)]
struct GeminiResponseContent {
    parts: Vec<GeminiResponsePart>,
}

#[derive(Debug, Deserialize)]
struct GeminiResponsePart {
    text: String,
}

// Circuit breaker states
#[derive(Debug, Clone)]
enum CircuitState {
    Closed,
    Open(DateTime<Utc>),
    HalfOpen,
}

#[derive(Debug, Default)]
struct QualityAssessmentMetrics {
    total_assessments: u64,
    successful_assessments: u64,
    failed_assessments: u64,
    average_response_time_ms: f64,
    circuit_breaker_opens: u64,
    fallback_uses: u64,
    total_tokens_used: u64,
}

pub struct CodeQualityAssessor {
    client: Client,
    project_id: String,
    location: String,
    model_name: String,
    circuit_state: Arc<Mutex<CircuitState>>,
    failure_count: Arc<Mutex<u32>>,
    failure_threshold: u32,
    reset_timeout: Duration,
    feature_toggles: Arc<FeatureToggles>,
    metrics: Arc<Mutex<QualityAssessmentMetrics>>,
    embeddings_service: Arc<EnhancedEmbeddingsService>,
}

impl CodeQualityAssessor {
    pub async fn new(embeddings_service: Arc<EnhancedEmbeddingsService>) -> Result<Self> {
        let project_id = env::var("GCP_PROJECT_ID")
            .unwrap_or_else(|_| "vibe-match-463114".to_string());
        let location = env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string());
        let model_name = env::var("GEMINI_MODEL_NAME")
            .unwrap_or_else(|_| "gemini-2.5-pro".to_string());

        let client = Client::builder()
            .timeout(GEMINI_TIMEOUT)
            .build()
            .context("Failed to create HTTP client")?;

        let feature_toggles = embeddings_service.get_feature_toggles();

        Ok(Self {
            client,
            project_id,
            location,
            model_name,
            circuit_state: Arc::new(Mutex::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            failure_threshold: 5,
            reset_timeout: Duration::from_secs(60),
            feature_toggles,
            metrics: Arc::new(Mutex::new(QualityAssessmentMetrics::default())),
            embeddings_service,
        })
    }

    pub async fn assess_code_quality(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
    ) -> Result<CodeQualityAssessment> {
        if !self.feature_toggles.enable_code_quality_assessment {
            tracing::info!("Code quality assessment disabled by feature toggle");
            return Ok(self.create_fallback_assessment(analyses, repository_metrics));
        }

        // Check circuit breaker
        if !self.check_circuit_breaker().await? {
            tracing::warn!("Circuit breaker is open, using fallback quality assessment");
            self.record_fallback_use().await;
            return Ok(self.create_fallback_assessment(analyses, repository_metrics));
        }

        let start_time = std::time::Instant::now();
        
        match self.perform_ai_quality_assessment(analyses, repository_metrics).await {
            Ok(assessment) => {
                self.record_success().await;
                self.record_response_time(start_time.elapsed().as_millis() as f64).await;
                Ok(assessment)
            }
            Err(e) => {
                tracing::error!("AI quality assessment failed: {}", e);
                self.record_failure().await;
                
                // Use fallback assessment
                self.record_fallback_use().await;
                Ok(self.create_fallback_assessment(analyses, repository_metrics))
            }
        }
    }

    async fn perform_ai_quality_assessment(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
    ) -> Result<CodeQualityAssessment> {
        let prompt = self.build_quality_assessment_prompt(analyses, repository_metrics)?;
        
        let mut retry_delay = INITIAL_RETRY_DELAY;
        
        for attempt in 0..MAX_RETRIES {
            match self.call_gemini_for_quality_assessment(&prompt).await {
                Ok(assessment) => {
                    return Ok(assessment);
                }
                Err(e) => {
                    if !self.is_retryable_error(&e) || attempt == MAX_RETRIES - 1 {
                        return Err(e);
                    }
                    
                    tracing::warn!(
                        "Quality assessment attempt {} failed, retrying in {:?}: {}",
                        attempt + 1,
                        retry_delay,
                        e
                    );
                    
                    tokio::time::sleep(retry_delay).await;
                    retry_delay = std::cmp::min(retry_delay * 2, Duration::from_secs(30));
                }
            }
        }
        
        Err(anyhow::anyhow!("All quality assessment attempts exhausted"))
    }

    fn build_quality_assessment_prompt(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
    ) -> Result<String> {
        let mut prompt = String::new();
        
        prompt.push_str("You are an expert code quality analyst. Analyze the following codebase and provide a comprehensive quality assessment. Return a JSON response with the following structure:\n\n");
        
        prompt.push_str("{\n");
        prompt.push_str("  \"overall_score\": 85.5,\n");
        prompt.push_str("  \"maintainability_score\": 80.0,\n");
        prompt.push_str("  \"readability_score\": 90.0,\n");
        prompt.push_str("  \"testability_score\": 75.0,\n");
        prompt.push_str("  \"security_score\": 85.0,\n");
        prompt.push_str("  \"performance_score\": 80.0,\n");
        prompt.push_str("  \"architecture_score\": 88.0,\n");
        prompt.push_str("  \"file_assessments\": [\n");
        prompt.push_str("    {\n");
        prompt.push_str("      \"file_path\": \"path/to/file.rs\",\n");
        prompt.push_str("      \"language\": \"rust\",\n");
        prompt.push_str("      \"overall_score\": 82.0,\n");
        prompt.push_str("      \"maintainability_score\": 78.0,\n");
        prompt.push_str("      \"readability_score\": 85.0,\n");
        prompt.push_str("      \"testability_score\": 80.0,\n");
        prompt.push_str("      \"security_score\": 85.0,\n");
        prompt.push_str("      \"performance_score\": 80.0,\n");
        prompt.push_str("      \"issues\": [\n");
        prompt.push_str("        {\n");
        prompt.push_str("          \"issue_type\": \"complexity\",\n");
        prompt.push_str("          \"severity\": \"Major\",\n");
        prompt.push_str("          \"description\": \"Function has high cyclomatic complexity\",\n");
        prompt.push_str("          \"location\": {\"start_line\": 15, \"end_line\": 45, \"start_column\": 0, \"end_column\": 1},\n");
        prompt.push_str("          \"suggestion\": \"Break down into smaller functions\",\n");
        prompt.push_str("          \"confidence\": 0.9\n");
        prompt.push_str("        }\n");
        prompt.push_str("      ],\n");
        prompt.push_str("      \"strengths\": [\"Good error handling\", \"Clear variable names\"],\n");
        prompt.push_str("      \"line_count\": 150,\n");
        prompt.push_str("      \"complexity\": 8,\n");
        prompt.push_str("      \"maintainability_index\": 75.5\n");
        prompt.push_str("    }\n");
        prompt.push_str("  ],\n");
        prompt.push_str("  \"insights\": [\n");
        prompt.push_str("    {\n");
        prompt.push_str("      \"category\": \"Architecture\",\n");
        prompt.push_str("      \"title\": \"Modular Structure\",\n");
        prompt.push_str("      \"description\": \"Code follows good modular design principles\",\n");
        prompt.push_str("      \"severity\": \"Info\",\n");
        prompt.push_str("      \"confidence\": 0.85,\n");
        prompt.push_str("      \"affected_files\": [\"file1.rs\", \"file2.rs\"],\n");
        prompt.push_str("      \"metrics\": {\"modularity_score\": 0.82}\n");
        prompt.push_str("    }\n");
        prompt.push_str("  ],\n");
        prompt.push_str("  \"recommendations\": [\n");
        prompt.push_str("    {\n");
        prompt.push_str("      \"priority\": \"High\",\n");
        prompt.push_str("      \"category\": \"Testing\",\n");
        prompt.push_str("      \"title\": \"Increase Test Coverage\",\n");
        prompt.push_str("      \"description\": \"Add unit tests for critical functions\",\n");
        prompt.push_str("      \"implementation_effort\": \"Medium\",\n");
        prompt.push_str("      \"expected_impact\": \"High\",\n");
        prompt.push_str("      \"affected_files\": [\"file1.rs\"],\n");
        prompt.push_str("      \"action_items\": [\"Write unit tests\", \"Add integration tests\"]\n");
        prompt.push_str("    }\n");
        prompt.push_str("  ],\n");
        prompt.push_str("  \"metrics\": {\n");
        prompt.push_str("    \"total_files_analyzed\": 25,\n");
        prompt.push_str("    \"total_lines_of_code\": 5000,\n");
        prompt.push_str("    \"average_complexity\": 6.5,\n");
        prompt.push_str("    \"average_maintainability\": 78.0,\n");
        prompt.push_str("    \"high_risk_files\": 3,\n");
        prompt.push_str("    \"medium_risk_files\": 8,\n");
        prompt.push_str("    \"low_risk_files\": 14,\n");
        prompt.push_str("    \"technical_debt_hours\": 24.5,\n");
        prompt.push_str("    \"test_coverage_estimate\": 65.0,\n");
        prompt.push_str("    \"code_duplication_ratio\": 0.12\n");
        prompt.push_str("  }\n");
        prompt.push_str("}\n\n");
        
        prompt.push_str("Assessment criteria:\n");
        prompt.push_str("- Maintainability: Code structure, modularity, documentation\n");
        prompt.push_str("- Readability: Naming conventions, code clarity, comments\n");
        prompt.push_str("- Testability: Test coverage, testable design, dependency injection\n");
        prompt.push_str("- Security: Vulnerability patterns, input validation, secure coding\n");
        prompt.push_str("- Performance: Algorithmic efficiency, resource usage, optimization\n");
        prompt.push_str("- Architecture: Design patterns, separation of concerns, SOLID principles\n\n");
        
        prompt.push_str("Repository overview:\n");
        prompt.push_str(&format!("Total files: {}\n", repository_metrics.total_files));
        prompt.push_str(&format!("Total lines: {}\n", repository_metrics.total_lines));
        prompt.push_str(&format!("Average complexity: {:.2}\n", repository_metrics.average_complexity.unwrap_or(0.0)));
        prompt.push_str(&format!("Maintainability score: {:.2}\n", repository_metrics.maintainability_score.unwrap_or(0.0)));
        
        if let Some(technical_debt) = repository_metrics.technical_debt_minutes {
            prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
        }
        
        prompt.push_str("\nFiles to analyze:\n\n");
        
        for analysis in analyses.iter().take(10) { // Limit to first 10 files to avoid token limits
            prompt.push_str(&format!("File: {}\n", analysis.path));
            prompt.push_str(&format!("Language: {}\n", analysis.language));
            prompt.push_str(&format!("Lines: {}\n", analysis.metrics.lines_of_code));
            prompt.push_str(&format!("Complexity: {}\n", analysis.metrics.complexity));
            prompt.push_str(&format!("Maintainability: {:.2}\n", analysis.metrics.maintainability_index));
            
            if let Some(symbols) = &analysis.symbols {
                prompt.push_str("Key symbols: ");
                for symbol in symbols.iter().take(5) {
                    prompt.push_str(&format!("{} ", symbol.name));
                }
                prompt.push_str("\n");
            }
            
            if let Some(text) = &analysis.ast.text {
                let preview = text.chars().take(1500).collect::<String>();
                prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
            }
        }
        
        if analyses.len() > 10 {
            prompt.push_str(&format!("... and {} more files\n", analyses.len() - 10));
        }
        
        Ok(prompt)
    }

    async fn call_gemini_for_quality_assessment(&self, prompt: &str) -> Result<CodeQualityAssessment> {
        let auth_token = self.get_auth_token().await?;
        
        let endpoint = format!(
            "https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/{}:generateContent",
            self.location, self.project_id, self.location, self.model_name
        );

        let request = GeminiQualityRequest {
            contents: vec![GeminiContent {
                parts: vec![GeminiPart {
                    text: prompt.to_string(),
                }],
                role: "user".to_string(),
            }],
            generation_config: GeminiGenerationConfig {
                temperature: 0.1,
                top_p: 0.8,
                top_k: 40,
                max_output_tokens: 8192,
                response_mime_type: "application/json".to_string(),
            },
            safety_settings: vec![
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HARASSMENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HATE_SPEECH".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
            ],
        };

        let response = self.client
            .post(&endpoint)
            .bearer_auth(&auth_token)
            .json(&request)
            .send()
            .await
            .context("Failed to send request to Gemini API")?;

        let status = response.status();
        
        if !status.is_success() {
            let error_body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Gemini API request failed with status {}: {}",
                status,
                error_body
            ));
        }

        let gemini_response: GeminiResponse = response
            .json()
            .await
            .context("Failed to parse Gemini response")?;

        if gemini_response.candidates.is_empty() {
            return Err(anyhow::anyhow!("No candidates in Gemini response"));
        }

        let response_text = &gemini_response.candidates[0].content.parts[0].text;
        
        let mut assessment: CodeQualityAssessment = serde_json::from_str(response_text)
            .context("Failed to parse quality assessment JSON")?;

        assessment.timestamp = Utc::now();
        
        Ok(assessment)
    }

    fn create_fallback_assessment(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
    ) -> CodeQualityAssessment {
        let mut file_assessments = Vec::new();
        let mut total_score = 0.0;
        
        for analysis in analyses {
            let maintainability_score = analysis.metrics.maintainability_index;
            let complexity_score = if analysis.metrics.complexity > 10 {
                60.0
            } else if analysis.metrics.complexity > 5 {
                75.0
            } else {
                90.0
            };
            
            let readability_score = if analysis.metrics.comment_ratio > 0.2 {
                85.0
            } else {
                70.0
            };
            
            let overall_score = (maintainability_score + complexity_score + readability_score) / 3.0;
            total_score += overall_score;
            
            let mut issues = Vec::new();
            let mut strengths = Vec::new();
            
            if analysis.metrics.complexity > 10 {
                issues.push(QualityIssue {
                    issue_type: "complexity".to_string(),
                    severity: IssueSeverity::Major,
                    description: "High cyclomatic complexity detected".to_string(),
                    location: None,
                    suggestion: "Consider breaking down into smaller functions".to_string(),
                    confidence: 0.9,
                });
            }
            
            if analysis.metrics.maintainability_index > 80.0 {
                strengths.push("Good maintainability score".to_string());
            }
            
            if analysis.metrics.comment_ratio > 0.2 {
                strengths.push("Well documented code".to_string());
            }
            
            file_assessments.push(FileQualityAssessment {
                file_path: analysis.path.clone(),
                language: analysis.language.clone(),
                overall_score,
                maintainability_score,
                readability_score,
                testability_score: 70.0, // Default estimate
                security_score: 80.0,    // Default estimate
                performance_score: 75.0, // Default estimate
                issues,
                strengths,
                line_count: analysis.metrics.lines_of_code,
                complexity: analysis.metrics.complexity,
                maintainability_index: analysis.metrics.maintainability_index,
            });
        }
        
        let overall_score = if !file_assessments.is_empty() {
            total_score / file_assessments.len() as f64
        } else {
            0.0
        };
        
        CodeQualityAssessment {
            overall_score,
            maintainability_score: repository_metrics.maintainability_score.unwrap_or(0.0),
            readability_score: 75.0, // Default estimate
            testability_score: 70.0, // Default estimate
            security_score: 80.0,    // Default estimate
            performance_score: 75.0, // Default estimate
            architecture_score: 78.0, // Default estimate
            file_assessments,
            insights: vec![],
            recommendations: vec![],
            metrics: QualityMetrics {
                total_files_analyzed: repository_metrics.total_files,
                total_lines_of_code: repository_metrics.total_lines,
                average_complexity: repository_metrics.average_complexity.unwrap_or(0.0),
                average_maintainability: repository_metrics.maintainability_score.unwrap_or(0.0),
                high_risk_files: 0,
                medium_risk_files: 0,
                low_risk_files: repository_metrics.total_files,
                technical_debt_hours: repository_metrics.technical_debt_minutes.unwrap_or(0) as f64 / 60.0,
                test_coverage_estimate: repository_metrics.test_coverage_estimate.unwrap_or(0.0),
                code_duplication_ratio: 0.1, // Default estimate
            },
            timestamp: Utc::now(),
        }
    }

    async fn get_auth_token(&self) -> Result<String> {
        // Check if we're running on Cloud Run or GCE (has metadata server)
        if env::var("K_SERVICE").is_ok() || env::var("GAE_ENV").is_ok() {
            // Running on Cloud Run or App Engine - use metadata server
            let metadata_url = format!(
                "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token?scopes={}",
                "https://www.googleapis.com/auth/cloud-platform"
            );
            
            let response = self.client
                .get(&metadata_url)
                .header("Metadata-Flavor", "Google")
                .send()
                .await
                .context("Failed to fetch token from metadata server")?;
                
            if !response.status().is_success() {
                return Err(anyhow::anyhow!(
                    "Metadata server returned error: {}",
                    response.status()
                ));
            }
            
            #[derive(Deserialize)]
            struct TokenResponse {
                access_token: String,
            }
            
            let token_response: TokenResponse = response
                .json()
                .await
                .context("Failed to parse token response")?;
                
            Ok(token_response.access_token)
        } else if let Ok(_creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
            // Local development with service account key file
            use std::process::Command;
            
            let output = Command::new("gcloud")
                .args(&["auth", "application-default", "print-access-token"])
                .output()
                .context("Failed to run gcloud command")?;
                
            if !output.status.success() {
                return Err(anyhow::anyhow!(
                    "gcloud command failed: {}",
                    String::from_utf8_lossy(&output.stderr)
                ));
            }
            
            let token = String::from_utf8(output.stdout)
                .context("Invalid UTF-8 in token")?
                .trim()
                .to_string();
                
            Ok(token)
        } else {
            // No authentication available
            Err(anyhow::anyhow!(
                "No authentication method available. Set GOOGLE_APPLICATION_CREDENTIALS or run on Cloud Run"
            ))
        }
    }

    // Circuit breaker implementation
    async fn check_circuit_breaker(&self) -> Result<bool> {
        let mut state = self.circuit_state.lock().await;
        
        match *state {
            CircuitState::Closed => Ok(true),
            CircuitState::Open(reset_time) => {
                if Utc::now() > reset_time {
                    *state = CircuitState::HalfOpen;
                    tracing::info!("Quality assessment circuit breaker transitioning to half-open");
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            CircuitState::HalfOpen => Ok(true),
        }
    }

    async fn record_success(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures = 0;
        metrics.successful_assessments += 1;
        
        if matches!(*state, CircuitState::HalfOpen) {
            *state = CircuitState::Closed;
            tracing::info!("Quality assessment circuit breaker closed after successful request");
        }
    }

    async fn record_failure(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures += 1;
        metrics.failed_assessments += 1;
        
        if *failures >= self.failure_threshold {
            let reset_time = Utc::now() + chrono::Duration::from_std(self.reset_timeout)
                .unwrap_or_else(|_| chrono::Duration::seconds(300));
            *state = CircuitState::Open(reset_time);
            *failures = 0;
            metrics.circuit_breaker_opens += 1;
            
            tracing::error!(
                "Quality assessment circuit breaker opened after {} failures, will reset at {}",
                self.failure_threshold,
                reset_time
            );
        }
    }

    async fn record_fallback_use(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.fallback_uses += 1;
    }

    async fn record_response_time(&self, response_time_ms: f64) {
        let mut metrics = self.metrics.lock().await;
        metrics.total_assessments += 1;
        
        // Calculate running average
        let total_assessments = metrics.total_assessments as f64;
        metrics.average_response_time_ms = 
            ((metrics.average_response_time_ms * (total_assessments - 1.0)) + response_time_ms) / total_assessments;
    }

    fn is_retryable_error(&self, error: &anyhow::Error) -> bool {
        let error_str = error.to_string().to_lowercase();
        
        error_str.contains("timeout") ||
        error_str.contains("temporarily unavailable") ||
        error_str.contains("429") || // Rate limit
        error_str.contains("500") || // Internal server error
        error_str.contains("502") || // Bad gateway
        error_str.contains("503") || // Service unavailable
        error_str.contains("504")    // Gateway timeout
    }

    pub async fn get_metrics(&self) -> QualityAssessmentMetrics {
        let metrics = self.metrics.lock().await;
        QualityAssessmentMetrics {
            total_assessments: metrics.total_assessments,
            successful_assessments: metrics.successful_assessments,
            failed_assessments: metrics.failed_assessments,
            average_response_time_ms: metrics.average_response_time_ms,
            circuit_breaker_opens: metrics.circuit_breaker_opens,
            fallback_uses: metrics.fallback_uses,
            total_tokens_used: metrics.total_tokens_used,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::*;

    #[tokio::test]
    async fn test_fallback_assessment_creation() {
        let embeddings_service = Arc::new(
            EnhancedEmbeddingsService::new().await.unwrap()
        );
        let assessor = CodeQualityAssessor::new(embeddings_service).await.unwrap();
        
        let analysis = FileAnalysis {
            path: "test.rs".to_string(),
            language: "rust".to_string(),
            content_hash: "hash123".to_string(),
            size_bytes: Some(1000),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 10, column: 0, byte: 100 },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics: FileMetrics {
                lines_of_code: 50,
                total_lines: Some(60),
                complexity: 12, // High complexity
                maintainability_index: 85.0,
                function_count: Some(3),
                class_count: Some(1),
                comment_ratio: Some(0.25),
            },
            chunks: None,
            symbols: None,
        };
        
        let repo_metrics = RepositoryMetrics {
            total_files: 1,
            total_lines: 50,
            total_complexity: 12,
            average_complexity: Some(12.0),
            maintainability_score: Some(85.0),
            technical_debt_minutes: Some(120),
            test_coverage_estimate: Some(60.0),
        };
        
        let assessment = assessor.create_fallback_assessment(&[analysis], &repo_metrics);
        
        assert!(assessment.overall_score > 0.0);
        assert_eq!(assessment.file_assessments.len(), 1);
        assert_eq!(assessment.file_assessments[0].file_path, "test.rs");
        assert_eq!(assessment.file_assessments[0].language, "rust");
        assert!(!assessment.file_assessments[0].issues.is_empty()); // Should have complexity issue
        assert!(!assessment.file_assessments[0].strengths.is_empty()); // Should have strengths
        assert_eq!(assessment.metrics.total_files_analyzed, 1);
        assert_eq!(assessment.metrics.total_lines_of_code, 50);
    }

    #[test]
    fn test_quality_assessment_serialization() {
        let assessment = CodeQualityAssessment {
            overall_score: 85.5,
            maintainability_score: 80.0,
            readability_score: 90.0,
            testability_score: 75.0,
            security_score: 85.0,
            performance_score: 80.0,
            architecture_score: 88.0,
            file_assessments: vec![],
            insights: vec![],
            recommendations: vec![],
            metrics: QualityMetrics {
                total_files_analyzed: 25,
                total_lines_of_code: 5000,
                average_complexity: 6.5,
                average_maintainability: 78.0,
                high_risk_files: 3,
                medium_risk_files: 8,
                low_risk_files: 14,
                technical_debt_hours: 24.5,
                test_coverage_estimate: 65.0,
                code_duplication_ratio: 0.12,
            },
            timestamp: Utc::now(),
        };
        
        let json = serde_json::to_string(&assessment).unwrap();
        let deserialized: CodeQualityAssessment = serde_json::from_str(&json).unwrap();
        
        assert_eq!(assessment.overall_score, deserialized.overall_score);
        assert_eq!(assessment.maintainability_score, deserialized.maintainability_score);
        assert_eq!(assessment.metrics.total_files_analyzed, deserialized.metrics.total_files_analyzed);
    }
}