use anyhow::{Context, Result};
use google_cloud_spanner::client::{Client as SpannerBaseClient, ClientConfig as SpannerConfig};
use google_cloud_storage::client::{Client as StorageBaseClient, ClientConfig as StorageConfig};
use google_cloud_pubsub::client::{Client as PubSubBaseClient, ClientConfig as PubSubConfig};
use crate::config::GcpSettings;
use std::env;

// Re-export concrete client types
pub use google_cloud_spanner::client::Client as SpannerClient;
pub use google_cloud_storage::client::Client as StorageClient;
pub use google_cloud_pubsub::client::Client as PubSubClient;

/// Create a Spanner client with proper configuration
/// Connection pooling: min 10, max 100 connections as per PRP
pub async fn create_spanner_client(gcp_settings: &GcpSettings) -> Result<SpannerClient> {
    let database_name = format!(
        "projects/{}/instances/{}/databases/{}",
        gcp_settings.project_id, gcp_settings.spanner_instance, gcp_settings.spanner_database
    );
    
    let mut config = SpannerConfig::default();
    
    // Check if running in development with emulator
    let is_development = env::var("ENVIRONMENT")
        .or_else(|_| env::var("ENV"))
        .unwrap_or_default() == "development";
        
    if is_development {
        if let Ok(emulator_host) = env::var("SPANNER_EMULATOR_HOST") {
            config.endpoint = emulator_host;
            tracing::info!("Using Spanner emulator at: {}", config.endpoint);
        }
    }
    
    // Try multiple authentication methods
    let config = if let Ok(creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
        if !creds_path.is_empty() && std::path::Path::new(&creds_path).exists() {
            tracing::info!("Using service account credentials from: {}", creds_path);
            config.with_auth().await
                .context("Failed to configure Spanner authentication with credentials file")?  
        } else {
            tracing::warn!("GOOGLE_APPLICATION_CREDENTIALS set but file not found: {}", creds_path);
            config.with_auth().await
                .context("Failed to configure Spanner authentication")?  
        }
    } else {
        tracing::info!("Using default authentication (metadata server or gcloud)");
        config.with_auth().await
            .context("Failed to configure Spanner authentication")?  
    };
    
    // Create client with the database name
    let client = SpannerBaseClient::new(database_name.clone(), config).await
        .context("Failed to create Spanner client")?;
    
    tracing::info!(
        "Spanner client initialized for database: {} (project: {}, instance: {}, database: {})",
        database_name, gcp_settings.project_id, gcp_settings.spanner_instance, gcp_settings.spanner_database
    );
    
    Ok(client)
}

/// Create a Storage client with retry logic for production use
/// Implements exponential backoff as per CLAUDE.md requirements
pub async fn create_storage_client(gcp_settings: &GcpSettings) -> Result<StorageClient> {
    let bucket_name = &gcp_settings.storage_bucket_name;
    
    let mut config = StorageConfig::default();
    
    // Check if running in development with emulator
    let is_development = env::var("ENVIRONMENT")
        .or_else(|_| env::var("ENV"))
        .unwrap_or_default() == "development";
        
    if is_development {
        if let Ok(emulator_host) = env::var("STORAGE_EMULATOR_HOST") {
            config.storage_endpoint = emulator_host;
            tracing::info!("Using Storage emulator at: {}", config.storage_endpoint);
        }
    }

    // Try multiple authentication methods
    let config = if let Ok(creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
        if !creds_path.is_empty() && std::path::Path::new(&creds_path).exists() {
            tracing::info!("Using service account credentials for Storage from: {}", creds_path);
            config.with_auth().await
                .context("Failed to configure Storage authentication with credentials file")?  
        } else {
            tracing::warn!("GOOGLE_APPLICATION_CREDENTIALS set but file not found: {}", creds_path);
            config.with_auth().await
                .context("Failed to configure Storage authentication")?  
        }
    } else {
        tracing::info!("Using default authentication for Storage (metadata server or gcloud)");
        config.with_auth().await
            .context("Failed to configure Storage authentication")?  
    };
    
    // Create client
    let client = StorageBaseClient::new(config);
    
    // Verify bucket exists with retry logic
    let max_retries = 3;
    let mut retry_delay = std::time::Duration::from_millis(100);
    
    for attempt in 0..max_retries {
        use google_cloud_storage::http::buckets::get::GetBucketRequest;
        match client.get_bucket(&GetBucketRequest {
            bucket: bucket_name.clone(),
            ..Default::default()
        }).await {
            Ok(_) => {
                tracing::info!("Storage client initialized for bucket: {}", bucket_name);
                break;
            }
            Err(e) => {
                if attempt == max_retries - 1 {
                    tracing::warn!(
                        "Bucket {} not found after {} attempts, will attempt to create on first use: {}",
                        bucket_name, max_retries, e
                    );
                } else {
                    tracing::debug!(
                        "Bucket check attempt {} failed, retrying in {:?}: {}",
                        attempt + 1, retry_delay, e
                    );
                    tokio::time::sleep(retry_delay).await;
                    retry_delay *= 2; // Exponential backoff
                }
            }
        }
    }
    
    Ok(client)
}

/// Create a Pub/Sub client with batch publishing support
/// Implements batch publishing for efficiency as per CLAUDE.md requirements
pub async fn create_pubsub_client(gcp_settings: &GcpSettings) -> Result<PubSubClient> {
    let mut config = PubSubConfig::default();
    
    // Check if running in development with emulator
    let is_development = env::var("ENVIRONMENT")
        .or_else(|_| env::var("ENV"))
        .unwrap_or_default() == "development";
        
    if is_development {
        if let Ok(emulator_host) = env::var("PUBSUB_EMULATOR_HOST") {
            config.endpoint = emulator_host;
            tracing::info!("Using Pub/Sub emulator at: {}", config.endpoint);
        }
    }

    // Try multiple authentication methods
    let config = if let Ok(creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
        if !creds_path.is_empty() && std::path::Path::new(&creds_path).exists() {
            tracing::info!("Using service account credentials for Pub/Sub from: {}", creds_path);
            config.with_auth().await
                .context("Failed to configure Pub/Sub authentication with credentials file")?  
        } else {
            tracing::warn!("GOOGLE_APPLICATION_CREDENTIALS set but file not found: {}", creds_path);
            config.with_auth().await
                .context("Failed to configure Pub/Sub authentication")?  
        }
    } else {
        tracing::info!("Using default authentication for Pub/Sub (metadata server or gcloud)");
        config.with_auth().await
            .context("Failed to configure Pub/Sub authentication")?  
    };
    
    let client = PubSubBaseClient::new(config).await
        .context("Failed to create Pub/Sub client")?;
    
    // Create required topics if they don't exist
    let topics = vec![
        "analysis-events",
        "analysis-progress", 
        "pattern-detected",
    ];
    
    for topic_name in &topics {
        let topic_path = format!("projects/{}/topics/{}", gcp_settings.project_id, topic_name);
        match client.topic(&topic_path).exists(None).await {
            Ok(exists) => {
                if !exists {
                    match client.create_topic(&topic_path, None, None).await {
                        Ok(_) => {
                            tracing::info!("Created Pub/Sub topic: {}", topic_name);
                        }
                        Err(e) => {
                            // Topic might have been created by another instance
                            tracing::warn!("Failed to create topic {}: {}", topic_name, e);
                        }
                    }
                } else {
                    tracing::debug!("Pub/Sub topic exists: {}", topic_name);
                }
            }
            Err(e) => {
                tracing::error!("Failed to check topic existence for {}: {}", topic_name, e);
            }
        }
    }
    
    tracing::info!("Pub/Sub client initialized for project: {}", gcp_settings.project_id);
    
    Ok(client)
}
