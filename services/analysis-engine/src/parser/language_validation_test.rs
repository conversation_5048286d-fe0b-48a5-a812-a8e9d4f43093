#[cfg(test)]
use super::*;
use std::path::Path;

#[tokio::test]
async fn test_supported_languages_compilation() {
    let parser = TreeSitterParser::new().expect("Failed to create parser");
    
    // Test all supported languages have parsers
    let supported_languages = [
        // Core languages
        "rust", "javascript", "typescript", "python", "go", "java", "c", "cpp",
        
        // Web and markup languages
        "html", "css", "json", "yaml", "ruby", "bash", "markdown",
        
        // Mobile development languages
        "swift", "kotlin", "objc",
        
        // Data science and numerical computing
        "r", "julia",
        
        // Functional programming languages
        "haskell", "scala", "erlang", "elixir",
        
        // Systems programming languages
        "zig", "d",
        
        // Other languages
        "lua", "dart", "nix",
    ];
    
    for &lang in &supported_languages {
        let has_parser = parser.parser_pools.contains_key(lang);
        assert!(has_parser, "Language {} should have a parser pool", lang);
    }
    
    // Test custom parsers
    let custom_parsers = ["sql", "toml", "xml"];
    for &lang in &custom_parsers {
        let test_file = format!("test.{}", lang);
        let path = Path::new(&test_file);
        let detected_lang = parser.detect_language(path).unwrap();
        assert_eq!(detected_lang, lang, "Language detection failed for {}", lang);
    }
    
    println!("All {} tree-sitter languages and {} custom parsers validated successfully!", 
             supported_languages.len(), custom_parsers.len());
}

#[tokio::test]
async fn test_language_parsing_samples() {
    let parser = TreeSitterParser::new().expect("Failed to create parser");
    
    // Test sample code for each language
    let samples = [
        ("rust", "test.rs", "fn main() { println!(\"Hello\"); }"),
        ("javascript", "test.js", "function hello() { console.log('Hello'); }"),
        ("python", "test.py", "def hello():\n    print('Hello')"),
        ("go", "test.go", "package main\nfunc main() { fmt.Println(\"Hello\") }"),
        ("java", "test.java", "public class Test { public static void main(String[] args) {} }"),
        ("c", "test.c", "#include <stdio.h>\nint main() { printf(\"Hello\"); return 0; }"),
        ("cpp", "test.cpp", "#include <iostream>\nint main() { std::cout << \"Hello\"; return 0; }"),
        ("json", "test.json", "{\"name\": \"test\", \"value\": 42}"),
        ("yaml", "test.yaml", "name: test\nvalue: 42"),
        ("html", "test.html", "<!DOCTYPE html><html><body><h1>Test</h1></body></html>"),
        ("css", "test.css", "body { color: red; }"),
        ("bash", "test.sh", "#!/bin/bash\necho \"Hello\""),
        ("markdown", "test.md", "# Test\nThis is a test"),
        ("sql", "test.sql", "SELECT * FROM users WHERE id = 1;"),
        ("toml", "test.toml", "[package]\nname = \"test\"\nversion = \"1.0.0\""),
        ("xml", "test.xml", "<?xml version=\"1.0\"?><root><item>test</item></root>"),
    ];
    
    for (lang, filename, content) in samples {
        let path = Path::new(filename);
        let result = parser.parse_content(path, content).await;
        
        match result {
            Ok(analysis) => {
                assert_eq!(analysis.language, lang);
                assert!(!analysis.content_hash.is_empty());
                println!("✓ {} parsing successful", lang);
            }
            Err(e) => {
                // Some languages might fail due to missing dependencies, but that's okay
                println!("⚠ {} parsing failed: {} (this might be expected)", lang, e.message);
            }
        }
    }
}

#[tokio::test]
async fn test_language_detection_accuracy() {
    let parser = TreeSitterParser::new().expect("Failed to create parser");
    
    let test_cases = [
        ("test.rs", "rust"),
        ("test.py", "python"),
        ("test.js", "javascript"),
        ("test.ts", "typescript"),
        ("test.go", "go"),
        ("test.java", "java"),
        ("test.c", "c"),
        ("test.cpp", "cpp"),
        ("test.html", "html"),
        ("test.css", "css"),
        ("test.json", "json"),
        ("test.yaml", "yaml"),
        ("test.yml", "yaml"),
        ("test.rb", "ruby"),
        ("test.sh", "bash"),
        ("test.md", "markdown"),
        ("test.sql", "sql"),
        ("test.toml", "toml"),
        ("test.xml", "xml"),
        ("test.swift", "swift"),
        ("test.kt", "kotlin"),
        ("test.m", "objc"),
        ("test.r", "r"),
        ("test.jl", "julia"),
        ("test.hs", "haskell"),
        ("test.scala", "scala"),
        ("test.erl", "erlang"),
        ("test.ex", "elixir"),
        ("test.zig", "zig"),
        ("test.d", "d"),
        ("test.lua", "lua"),
        ("test.dart", "dart"),
        ("test.nix", "nix"),
    ];
    
    for (filename, expected_lang) in test_cases {
        let path = Path::new(filename);
        let detected_lang = parser.detect_language(path).unwrap();
        assert_eq!(detected_lang, expected_lang, 
                   "Language detection failed for {}, expected {}, got {}", 
                   filename, expected_lang, detected_lang);
    }
    
    println!("All {} language detection tests passed!", test_cases.len());
}

#[tokio::test]
async fn test_parser_pool_creation() {
    let parser = TreeSitterParser::new().expect("Failed to create parser");
    
    // Verify parser pools are created for available languages
    let expected_pool_count = parser.parser_pools.len();
    assert!(expected_pool_count > 20, "Should have created pools for 20+ languages, got {}", expected_pool_count);
    
    // Test that we can get parsers from pools
    for (lang, pool) in &parser.parser_pools {
        let parser_result = pool.get_parser().await;
        assert!(parser_result.is_ok(), "Failed to get parser for {}", lang);
        
        if let Ok(parser) = parser_result {
            pool.return_parser(parser);
        }
    }
    
    println!("Parser pools created for {} languages", expected_pool_count);
}