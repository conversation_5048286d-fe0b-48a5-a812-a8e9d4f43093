use anyhow::Result;
use crate::models::{Symbol, SymbolType, Range, Position, ParseError, ParseErrorType};
use std::collections::HashMap;
use regex::Regex;

/// SQL parser adapter using sqlparser crate
pub struct SqlAdapter;

impl SqlAdapter {
    pub fn parse_sql(content: &str) -> Result<Vec<Symbol>, ParseError> {
        use sqlparser::dialect::{GenericDialect, PostgreSqlDialect, MySqlDialect, SQLiteDialect};
        use sqlparser::parser::Parser;
        
        // Try multiple dialects for broader compatibility
        let dialects: Vec<Box<dyn sqlparser::dialect::Dialect>> = vec![
            Box::new(GenericDialect {}),
            Box::new(PostgreSqlDialect {}),
            Box::new(MySqlDialect {}),
            Box::new(SQLiteDialect {}),
        ];
        
        let mut symbols = Vec::new();
        let mut parse_success = false;
        
        for dialect in dialects {
            match Parser::parse_sql(&*dialect, content) {
                Ok(statements) => {
                    parse_success = true;
                    for statement in statements {
                        extract_sql_symbols(&statement, &mut symbols);
                    }
                    break;
                }
                Err(_) => continue,
            }
        }
        
        if !parse_success {
            return Err(ParseError {
                file_path: String::new(),
                error_type: ParseErrorType::ParseError,
                message: "Failed to parse SQL with any dialect".to_string(),
                position: None,
            });
        }
        
        Ok(symbols)
    }
}

fn extract_sql_symbols(statement: &sqlparser::ast::Statement, symbols: &mut Vec<Symbol>) {
    use sqlparser::ast::*;
    
    match statement {
        Statement::CreateTable { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Class, // Tables as classes
                range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateView { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Class, // Views as classes
                range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateFunction { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Function,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 0, column: 0, byte: 0 },
                },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateProcedure { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Function,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 0, column: 0, byte: 0 },
                },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateIndex { name, .. } => {
            if let Some(name) = name {
                symbols.push(Symbol {
                    name: name.to_string(),
                    symbol_type: SymbolType::Variable, // Indexes as variables
                    range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                    visibility: None,
                    signature: None,
                    documentation: None,
                });
            }
        }
        _ => {
            // Handle other statement types as needed
        }
    }
}

/// XML parser adapter using quick-xml crate
pub struct XmlAdapter;

impl XmlAdapter {
    pub fn parse_xml(content: &str) -> Result<Vec<Symbol>, ParseError> {
        use quick_xml::events::Event;
        use quick_xml::Reader;
        
        let mut reader = Reader::from_str(content);
        reader.trim_text(true);
        
        let mut symbols = Vec::new();
        let mut buf = Vec::new();
        let mut current_line = 1;
        let mut element_stack = Vec::new();
        
        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) => {
                    let name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    let position = reader.buffer_position();
                    
                    // Track nested structure
                    element_stack.push(name.clone());
                    
                    // Add element as symbol
                    symbols.push(Symbol {
                        name: name.clone(),
                        symbol_type: if element_stack.len() == 1 {
                            SymbolType::Namespace // Root elements
                        } else {
                            SymbolType::Class // Nested elements
                        },
                        range: Range {
                            start: Position {
                                line: current_line as u32,
                                column: 0,
                                byte: position as u32,
                            },
                            end: Position {
                                line: current_line as u32,
                                column: 0,
                                byte: position as u32,
                            },
                        },
                        visibility: None,
                        signature: None,
                        documentation: None,
                    });
                    
                    // Extract attributes as variables
                    for attr in e.attributes() {
                        if let Ok(attr) = attr {
                            let attr_name = String::from_utf8_lossy(attr.key.as_ref()).to_string();
                            symbols.push(Symbol {
                                name: format!("{}.{}", name, attr_name),
                                symbol_type: SymbolType::Variable,
                                range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                                visibility: None,
                                signature: None,
                                documentation: None,
                            });
                        }
                    }
                }
                Ok(Event::End(_)) => {
                    element_stack.pop();
                }
                Ok(Event::Text(e)) => {
                    // Count newlines in text
                    let text = e.unescape().unwrap_or_default();
                    current_line += text.matches('\n').count() as u32;
                }
                Ok(Event::Eof) => break,
                Err(e) => {
                    return Err(ParseError {
                        file_path: String::new(),
                        error_type: ParseErrorType::ParseError,
                        message: format!("XML parse error: {}", e),
                        position: Some(Position {
                            line: current_line,
                            column: 0,
                            byte: 0,
                        }),
                    });
                }
                _ => {}
            }
            
            buf.clear();
        }
        
        Ok(symbols)
    }
}

/// TOML parser adapter
pub struct TomlAdapter;

impl TomlAdapter {
    pub fn parse_toml(content: &str) -> Result<Vec<Symbol>, ParseError> {
        match content.parse::<toml::Value>() {
            Ok(value) => {
                let mut symbols = Vec::new();
                extract_toml_symbols(&value, "", &mut symbols);
                Ok(symbols)
            }
            Err(e) => Err(ParseError {
                file_path: String::new(),
                error_type: ParseErrorType::ParseError,
                message: format!("TOML parse error: {}", e),
                position: None,
            }),
        }
    }
}

fn extract_toml_symbols(value: &toml::Value, prefix: &str, symbols: &mut Vec<Symbol>) {
    match value {
        toml::Value::Table(table) => {
            for (key, val) in table {
                let full_key = if prefix.is_empty() {
                    key.clone()
                } else {
                    format!("{}.{}", prefix, key)
                };
                
                symbols.push(Symbol {
                    name: full_key.clone(),
                    symbol_type: match val {
                        toml::Value::Table(_) => SymbolType::Namespace,
                        toml::Value::Array(_) => SymbolType::Class,
                        _ => SymbolType::Variable,
                    },
                    range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                    visibility: None,
                    signature: None,
                    documentation: None,
                });
                
                // Recursively extract nested tables
                if let toml::Value::Table(_) = val {
                    extract_toml_symbols(val, &full_key, symbols);
                }
            }
        }
        _ => {
            // Top-level non-table values
            if prefix.is_empty() {
                symbols.push(Symbol {
                    name: "root".to_string(),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                    visibility: None,
                    signature: None,
                    documentation: None,
                });
            }
        }
    }
}

/// Markdown documentation analysis adapter
pub struct MarkdownAdapter;

impl MarkdownAdapter {
    pub fn parse_markdown(content: &str) -> Result<Vec<Symbol>, ParseError> {
        let mut symbols = Vec::new();
        let lines: Vec<&str> = content.lines().collect();
        
        // Track heading structure
        let mut heading_stack = Vec::new();
        let mut current_line = 0;
        let mut in_code_block = false;
        let mut code_block_lang = None;
        let mut current_code_block = String::new();
        let mut code_block_start = 0;
        
        // Regex patterns for various markdown elements
        let heading_regex = Regex::new(r"^(#{1,6})\s+(.+)$").unwrap();
        let code_block_regex = Regex::new(r"^```(\w+)?").unwrap();
        let inline_code_regex = Regex::new(r"`([^`]+)`").unwrap();
        let link_regex = Regex::new(r"\[([^\]]+)\]\(([^)]+)\)").unwrap();
        let image_regex = Regex::new(r"!\[([^\]]*)\]\(([^)]+)\)").unwrap();
        let reference_regex = Regex::new(r"\[([^\]]+)\]: (.+)").unwrap();
        let task_regex = Regex::new(r"^(\s*)- \[([x ])\] (.+)$").unwrap();
        let list_regex = Regex::new(r"^(\s*)[-*+] (.+)$").unwrap();
        let table_regex = Regex::new(r"^\|(.+)\|$").unwrap();
        
        for (line_idx, line) in lines.iter().enumerate() {
            current_line = line_idx + 1;
            
            // Handle code blocks
            if let Some(captures) = code_block_regex.captures(line) {
                if !in_code_block {
                    // Starting a code block
                    in_code_block = true;
                    code_block_lang = captures.get(1).map(|m| m.as_str().to_string());
                    code_block_start = current_line;
                    current_code_block.clear();
                } else {
                    // Ending a code block
                    in_code_block = false;
                    
                    // Add code block as symbol
                    let lang = code_block_lang.as_deref().unwrap_or("unknown");
                    symbols.push(Symbol {
                        name: format!("code_block_{}", lang),
                        symbol_type: SymbolType::Function,
                        range: Range {
                            start: Position { 
                                line: code_block_start as u32, 
                                column: 0, 
                                byte: 0 
                            },
                            end: Position { 
                                line: current_line as u32, 
                                column: line.len() as u32, 
                                byte: 0 
                            },
                        },
                        visibility: None,
                        signature: Some(current_code_block.clone()),
                        documentation: Some(format!("Code block in {}", lang)),
                    });
                    
                    code_block_lang = None;
                    current_code_block.clear();
                }
                continue;
            }
            
            // If we're in a code block, accumulate the content
            if in_code_block {
                current_code_block.push_str(line);
                current_code_block.push('\n');
                continue;
            }
            
            // Handle headings
            if let Some(captures) = heading_regex.captures(line) {
                let level = captures.get(1).unwrap().as_str().len();
                let title = captures.get(2).unwrap().as_str().trim();
                
                // Update heading stack
                heading_stack.retain(|(l, _)| *l < level);
                heading_stack.push((level, title.to_string()));
                
                // Create hierarchical name
                let hierarchical_name = heading_stack.iter()
                    .map(|(_, name)| name.clone())
                    .collect::<Vec<_>>()
                    .join(" > ");
                
                symbols.push(Symbol {
                    name: hierarchical_name,
                    symbol_type: match level {
                        1 => SymbolType::Namespace,
                        2 => SymbolType::Class,
                        _ => SymbolType::Function,
                    },
                    range: Range {
                        start: Position { 
                            line: current_line as u32, 
                            column: 0, 
                            byte: 0 
                        },
                        end: Position { 
                            line: current_line as u32, 
                            column: line.len() as u32, 
                            byte: 0 
                        },
                    },
                    visibility: None,
                    signature: Some(format!("H{} {}", level, title)),
                    documentation: Some(format!("Heading level {} in documentation", level)),
                });
                continue;
            }
            
            // Handle task lists
            if let Some(captures) = task_regex.captures(line) {
                let indent = captures.get(1).unwrap().as_str().len();
                let checked = captures.get(2).unwrap().as_str() == "x";
                let task_text = captures.get(3).unwrap().as_str();
                
                symbols.push(Symbol {
                    name: format!("task: {}", task_text),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                        start: Position { 
                            line: current_line as u32, 
                            column: indent as u32, 
                            byte: 0 
                        },
                        end: Position { 
                            line: current_line as u32, 
                            column: line.len() as u32, 
                            byte: 0 
                        },
                    },
                    visibility: None,
                    signature: Some(format!("Task: {} ({})", task_text, if checked { "completed" } else { "pending" })),
                    documentation: Some("Task item in documentation".to_string()),
                });
                continue;
            }
            
            // Handle regular lists
            if let Some(captures) = list_regex.captures(line) {
                let indent = captures.get(1).unwrap().as_str().len();
                let item_text = captures.get(2).unwrap().as_str();
                
                symbols.push(Symbol {
                    name: format!("list_item: {}", item_text),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                        start: Position { 
                            line: current_line as u32, 
                            column: indent as u32, 
                            byte: 0 
                        },
                        end: Position { 
                            line: current_line as u32, 
                            column: line.len() as u32, 
                            byte: 0 
                        },
                    },
                    visibility: None,
                    signature: Some(format!("List item: {}", item_text)),
                    documentation: Some("List item in documentation".to_string()),
                });
            }
            
            // Handle table rows
            if table_regex.is_match(line) {
                let cells: Vec<&str> = line.split('|').filter(|s| !s.trim().is_empty()).collect();
                if !cells.is_empty() {
                    symbols.push(Symbol {
                        name: format!("table_row_{}", current_line),
                        symbol_type: SymbolType::Class,
                        range: Range {
                            start: Position { 
                                line: current_line as u32, 
                                column: 0, 
                                byte: 0 
                            },
                            end: Position { 
                                line: current_line as u32, 
                                column: line.len() as u32, 
                                byte: 0 
                            },
                        },
                        visibility: None,
                        signature: Some(format!("Table row with {} columns", cells.len())),
                        documentation: Some("Table row in documentation".to_string()),
                    });
                }
            }
            
            // Handle links
            for captures in link_regex.captures_iter(line) {
                let link_text = captures.get(1).unwrap().as_str();
                let link_url = captures.get(2).unwrap().as_str();
                
                symbols.push(Symbol {
                    name: format!("link: {}", link_text),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                        start: Position { 
                            line: current_line as u32, 
                            column: 0, 
                            byte: 0 
                        },
                        end: Position { 
                            line: current_line as u32, 
                            column: line.len() as u32, 
                            byte: 0 
                        },
                    },
                    visibility: None,
                    signature: Some(format!("Link: {} -> {}", link_text, link_url)),
                    documentation: Some("Link in documentation".to_string()),
                });
            }
            
            // Handle images
            for captures in image_regex.captures_iter(line) {
                let alt_text = captures.get(1).unwrap().as_str();
                let image_url = captures.get(2).unwrap().as_str();
                
                symbols.push(Symbol {
                    name: format!("image: {}", alt_text),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                        start: Position { 
                            line: current_line as u32, 
                            column: 0, 
                            byte: 0 
                        },
                        end: Position { 
                            line: current_line as u32, 
                            column: line.len() as u32, 
                            byte: 0 
                        },
                    },
                    visibility: None,
                    signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
                    documentation: Some("Image in documentation".to_string()),
                });
            }
            
            // Handle reference links
            for captures in reference_regex.captures_iter(line) {
                let ref_name = captures.get(1).unwrap().as_str();
                let ref_url = captures.get(2).unwrap().as_str();
                
                symbols.push(Symbol {
                    name: format!("reference: {}", ref_name),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                        start: Position { 
                            line: current_line as u32, 
                            column: 0, 
                            byte: 0 
                        },
                        end: Position { 
                            line: current_line as u32, 
                            column: line.len() as u32, 
                            byte: 0 
                        },
                    },
                    visibility: None,
                    signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
                    documentation: Some("Reference link in documentation".to_string()),
                });
            }
            
            // Handle inline code
            for captures in inline_code_regex.captures_iter(line) {
                let code = captures.get(1).unwrap().as_str();
                
                symbols.push(Symbol {
                    name: format!("inline_code: {}", code),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                        start: Position { 
                            line: current_line as u32, 
                            column: 0, 
                            byte: 0 
                        },
                        end: Position { 
                            line: current_line as u32, 
                            column: line.len() as u32, 
                            byte: 0 
                        },
                    },
                    visibility: None,
                    signature: Some(format!("Inline code: {}", code)),
                    documentation: Some("Inline code in documentation".to_string()),
                });
            }
        }
        
        Ok(symbols)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_sql_parsing() {
        let sql = r#"
            CREATE TABLE users (
                id INT PRIMARY KEY,
                name VARCHAR(100)
            );
            
            CREATE VIEW active_users AS
            SELECT * FROM users WHERE active = true;
            
            CREATE FUNCTION get_user_name(user_id INT)
            RETURNS VARCHAR(100);
        "#;
        
        let symbols = SqlAdapter::parse_sql(sql).unwrap();
        assert!(symbols.len() >= 3);
        
        let names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(names.contains(&"users"));
        assert!(names.contains(&"active_users"));
    }
    
    #[test]
    fn test_xml_parsing() {
        let xml = r#"
            <root>
                <users>
                    <user id="1" name="John">
                        <email><EMAIL></email>
                    </user>
                </users>
            </root>
        "#;
        
        let symbols = XmlAdapter::parse_xml(xml).unwrap();
        assert!(symbols.len() > 0);
        
        let names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(names.contains(&"root"));
        assert!(names.contains(&"users"));
        assert!(names.contains(&"user"));
    }
    
    #[test]
    fn test_toml_parsing() {
        let toml = r#"
            [package]
            name = "my-app"
            version = "1.0.0"
            
            [dependencies]
            serde = "1.0"
            tokio = { version = "1.0", features = ["full"] }
        "#;
        
        let symbols = TomlAdapter::parse_toml(toml).unwrap();
        assert!(symbols.len() > 0);
        
        let names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(names.contains(&"package"));
        assert!(names.contains(&"package.name"));
        assert!(names.contains(&"dependencies"));
    }

    #[test]
    fn test_markdown_parsing() {
        let markdown = r#"
# Main Title

This is a paragraph with `inline code` and a [link](https://example.com).

## Section 1

Some content here.

### Subsection 1.1

- Item 1
- Item 2
- [x] Completed task
- [ ] Pending task

```rust
fn main() {
    println!("Hello, world!");
}
```

## Section 2

| Column 1 | Column 2 |
|----------|----------|
| Value 1  | Value 2  |

![Image](image.png)

[reference]: https://example.com/reference
        "#;
        
        let symbols = MarkdownAdapter::parse_markdown(markdown).unwrap();
        assert!(symbols.len() > 0);
        
        let names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(names.contains(&"Main Title"));
        assert!(names.contains(&"Main Title > Section 1"));
        assert!(names.contains(&"Main Title > Section 1 > Subsection 1.1"));
        
        // Check for various markdown elements
        let has_code_block = symbols.iter().any(|s| s.name.starts_with("code_block_"));
        assert!(has_code_block);
        
        let has_task = symbols.iter().any(|s| s.name.starts_with("task:"));
        assert!(has_task);
        
        let has_link = symbols.iter().any(|s| s.name.starts_with("link:"));
        assert!(has_link);
    }
}