use anyhow::{Context, Result};
use std::path::Path;
use std::collections::HashMap;
use std::sync::Arc;
use tree_sitter::{Parser, Node, Language};
use crate::models::{FileAnalysis, AstNode, Position, Range, Symbol, SymbolType, FileMetrics, ParseError, ParseErrorType, CodeChunk, ChunkType};
use sha2::{Sha256, Digest};
use tokio::io::{BufReader, AsyncBufReadExt};
use tokio::fs::File;
use tokio::sync::Semaphore;
use crossbeam_queue::SegQueue;
use tracing::debug;

mod adapters;
mod language_metrics;
mod language_validation_test;
pub mod validation_demo;
use adapters::{SqlAdapter, XmlAdapter, TomlAdapter, MarkdownAdapter};
use language_metrics::LanguageMetricsCalculator;



// Import the language functions from the tree-sitter crates
// Using consistent function-based imports for all languages
use tree_sitter_rust;
use tree_sitter_python;
use tree_sitter_javascript;
use tree_sitter_typescript;
use tree_sitter_go;
use tree_sitter_java;
use tree_sitter_c;
use tree_sitter_cpp;

// Web and markup languages
use tree_sitter_html;
use tree_sitter_css;
use tree_sitter_json;
use tree_sitter_yaml;
use tree_sitter_ruby;
use tree_sitter_bash;
use tree_sitter_md;
use tree_sitter_xml;

// Mobile development languages
use tree_sitter_swift;
use tree_sitter_kotlin;
use tree_sitter_objc;

// Data science and numerical computing
use tree_sitter_r;
use tree_sitter_julia;

// Functional programming languages
use tree_sitter_haskell;
use tree_sitter_scala;
use tree_sitter_erlang;
use tree_sitter_elixir;

// Systems programming languages
use tree_sitter_zig;
use tree_sitter_d;

// Other languages
use tree_sitter_lua;
use tree_sitter_dart;
use tree_sitter_nix;

// Use the already declared adapters module

/// Helper function to get the language in a consistent way
fn get_language_for_name(name: &str) -> Option<Language> {
    match name {
        // Core languages - TODO: Fix LanguageFn to Language conversion
        "rust" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "python" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "javascript" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "typescript" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "go" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "java" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "c" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "cpp" => None, // TODO: Fix tree-sitter-language LanguageFn conversion,
        
        // Web and markup languages - using functions
        "html" => Some(tree_sitter_html::language()),
        "css" => Some(tree_sitter_css::language()),
        "json" => Some(tree_sitter_json::language()),
        "yaml" => Some(tree_sitter_yaml::language()),
        "ruby" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "bash" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "markdown" => None, // TODO: Fix tree-sitter-language LanguageFn conversion,
        "xml" => None, // tree_sitter_xml API unclear, use custom XML parser instead
        
        // Mobile development languages
        "swift" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "kotlin" => Some(tree_sitter_kotlin::language()),
        "objc" => None, // TODO: Fix tree-sitter-language LanguageFn conversion,
        
        // Data science and numerical computing
        "r" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "julia" => None, // TODO: Fix tree-sitter-language LanguageFn conversion,
        
        // Functional programming languages
        "haskell" => None, // Some(tree_sitter_haskell::language()), // Version conflict with tree_sitter
        "scala" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "erlang" => Some(tree_sitter_erlang::language()),
        "elixir" => None, // TODO: Fix tree-sitter-language LanguageFn conversion,
        
        // Systems programming languages
        "zig" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        "d" => Some(tree_sitter_d::language()),
        
        // Other languages
        "lua" => Some(tree_sitter_lua::language()),
        "dart" => Some(tree_sitter_dart::language()),
        "nix" => None, // TODO: Fix tree-sitter-language LanguageFn conversion
        
        // Custom parsers (handled separately)
        "sql" => None,  // Use custom SQL parser via sqlparser crate
        "toml" => None, // Use custom TOML parser via toml crate
        "xml" => None,  // Use custom XML parser via quick-xml crate
        
        // Skip problematic languages for now
        "php" => None,
        "ocaml" => None,
        
        _ => None,
    }
}

/// A pool of parsers for a specific language to reduce allocation overhead
#[derive(Debug)]
struct ParserPool {
    /// Queue of available parsers
    parsers: SegQueue<Parser>,
    /// Language for this pool
    language: Language,
    /// Semaphore to limit concurrent parser usage
    semaphore: Arc<Semaphore>,
    /// Maximum number of parsers in the pool
    max_size: usize,
    /// Current number of parsers created
    current_size: Arc<std::sync::atomic::AtomicUsize>,
}

impl ParserPool {
    /// Create a new parser pool for a language
    fn new(language: Language, max_size: usize) -> Result<Self> {
        let pool = Self {
            parsers: SegQueue::new(),
            language,
            semaphore: Arc::new(Semaphore::new(max_size)),
            max_size,
            current_size: Arc::new(std::sync::atomic::AtomicUsize::new(0)),
        };

        // Warm up the pool with initial parsers
        pool.warm_up_pool()?;

        Ok(pool)
    }

    /// Warm up the pool with initial parsers
    fn warm_up_pool(&self) -> Result<()> {
        let warm_up_count = (self.max_size / 4).max(1).min(4); // 25% of max size, at least 1, at most 4
        
        for _ in 0..warm_up_count {
            let mut parser = Parser::new();
            parser.set_language(&self.language)
                .context("Failed to set language for parser during warm-up")?;
            
            self.parsers.push(parser);
            self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        }
        
        debug!("Warmed up parser pool with {} parsers", warm_up_count);
        Ok(())
    }

    /// Get pool statistics
    pub fn get_stats(&self) -> ParserPoolStats {
        let available = self.parsers.len();
        let total = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        let in_use = total - available;
        
        ParserPoolStats {
            total_parsers: total,
            available_parsers: available,
            in_use_parsers: in_use,
            max_parsers: self.max_size,
            utilization: (in_use as f64 / total as f64) * 100.0,
        }
    }

    /// Dynamically adjust pool size based on usage patterns
    pub fn adjust_pool_size(&self, target_utilization: f64) -> Result<()> {
        let stats = self.get_stats();
        
        // If utilization is too high, try to add more parsers
        if stats.utilization > target_utilization && stats.total_parsers < self.max_size {
            let needed = ((stats.in_use_parsers as f64 / target_utilization * 100.0) as usize)
                .saturating_sub(stats.total_parsers)
                .min(self.max_size - stats.total_parsers);
            
            for _ in 0..needed {
                let mut parser = Parser::new();
                parser.set_language(&self.language)
                    .context("Failed to set language for parser during expansion")?;
                
                self.parsers.push(parser);
                self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            }
            
            debug!("Expanded parser pool by {} parsers (utilization: {:.1}%)", needed, stats.utilization);
        }
        
        Ok(())
    }

    /// Preload parsers for expected load with intelligent sizing
    pub fn preload_parsers(&self, expected_concurrent_requests: usize) -> Result<()> {
        let current_total = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        let available = self.parsers.len();
        let in_use = current_total - available;
        
        // Calculate target based on current utilization and expected load
        let utilization_factor = if current_total > 0 { in_use as f64 / current_total as f64 } else { 0.0 };
        let growth_multiplier = if utilization_factor > 0.8 { 1.5 } else { 1.2 };
        
        let target_total = ((expected_concurrent_requests as f64 * growth_multiplier) as usize)
            .min(self.max_size)
            .max(current_total);
        
        if target_total > current_total {
            let to_create = target_total - current_total;
            
            // Create parsers in batches to avoid blocking
            let batch_size = 5;
            for batch in (0..to_create).step_by(batch_size) {
                let batch_end = (batch + batch_size).min(to_create);
                
                for _ in batch..batch_end {
                    let mut parser = Parser::new();
                    parser.set_language(&self.language)
                        .context("Failed to set language for parser during preload")?;
                    
                    self.parsers.push(parser);
                    self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                }
                
                // Small yield to prevent blocking
                if batch_end < to_create {
                    std::thread::yield_now();
                }
            }
            
            debug!("Preloaded {} parsers for expected load (utilization: {:.1}%, target: {})", 
                   to_create, utilization_factor * 100.0, target_total);
        }
        
        Ok(())
    }
    
    /// Shrink the pool if there are too many unused parsers
    pub fn shrink_if_underutilized(&self, min_utilization: f64) -> Result<()> {
        let current_total = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        let available = self.parsers.len();
        let in_use = current_total - available;
        
        if current_total > 0 {
            let utilization = in_use as f64 / current_total as f64;
            
            if utilization < min_utilization && available > 2 {
                // Keep at least 2 parsers available
                let to_remove = (available - 2).min(available / 2);
                
                for _ in 0..to_remove {
                    if self.parsers.pop().is_some() {
                        self.current_size.fetch_sub(1, std::sync::atomic::Ordering::Relaxed);
                    }
                }
                
                debug!("Shrunk parser pool by {} parsers (utilization: {:.1}%)", to_remove, utilization * 100.0);
            }
        }
        
        Ok(())
    }

    /// Get a parser from the pool, creating one if necessary
    async fn get_parser(&self) -> Result<Parser> {
        // Acquire semaphore permit to limit concurrent usage
        let _permit = self.semaphore.acquire().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire parser semaphore"))?;

        // Try to get an existing parser from the queue
        if let Some(parser) = self.parsers.pop() {
            return Ok(parser);
        }

        // If no parser available and we haven't reached max size, create a new one
        let current = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        if current < self.max_size {
            let mut parser = Parser::new();
            parser.set_language(&self.language)
                .context("Failed to set language for new parser")?;
            self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            debug!("Created new parser for language, total: {}", current + 1);
            return Ok(parser);
        }

        // If we've reached max size, wait for a parser to become available
        // This is a fallback that should rarely happen due to semaphore limiting
        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;

        // Try again with a timeout to avoid infinite recursion
        let mut attempts = 0;
        loop {
            if let Some(parser) = self.parsers.pop() {
                return Ok(parser);
            }

            attempts += 1;
            if attempts > 100 {
                return Err(anyhow::anyhow!("Timeout waiting for parser to become available"));
            }

            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }
    }

    /// Return a parser to the pool
    fn return_parser(&self, parser: Parser) {
        self.parsers.push(parser);
    }
}

/// Enhanced TreeSitter parser with memory pooling
pub struct TreeSitterParser {
    /// Parser pools for each language
    parser_pools: HashMap<String, Arc<ParserPool>>,
    /// Configuration for pool sizes
    _pool_config: ParserPoolConfig,
    /// Language-specific metrics calculator
    metrics_calculator: LanguageMetricsCalculator,
}

/// Configuration for parser pools
#[derive(Debug, Clone)]
pub struct ParserPoolConfig {
    /// Maximum parsers per language pool
    pub max_parsers_per_language: usize,
    /// Initial parsers to create per language
    pub initial_parsers_per_language: usize,
    /// Target utilization percentage for dynamic sizing
    pub target_utilization: f64,
    /// Enable dynamic pool sizing
    pub enable_dynamic_sizing: bool,
    /// Pool optimization interval in seconds
    pub optimization_interval: u64,
}

/// Statistics for a parser pool
#[derive(Debug, Clone)]
pub struct ParserPoolStats {
    pub total_parsers: usize,
    pub available_parsers: usize,
    pub in_use_parsers: usize,
    pub max_parsers: usize,
    pub utilization: f64,
}

impl Default for ParserPoolConfig {
    fn default() -> Self {
        Self {
            max_parsers_per_language: num_cpus::get().max(4), // At least 4, up to CPU count
            initial_parsers_per_language: 1,
            target_utilization: 75.0, // 75% utilization target
            enable_dynamic_sizing: true,
            optimization_interval: 30, // 30 seconds
        }
    }
}

/// Configuration for streaming file processing
#[derive(Debug, Clone)]
pub struct StreamingConfig {
    /// Size threshold above which to use streaming (bytes)
    pub streaming_threshold: u64,
    /// Maximum file size to process (bytes)
    pub max_file_size: u64,
    /// Buffer size for reading chunks (bytes)
    pub buffer_size: usize,
    /// Maximum memory usage for content accumulation (bytes)
    pub max_memory_usage: u64,
    /// Whether to enable incremental parsing for very large files
    pub incremental_parsing: bool,
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            streaming_threshold: 10 * 1024 * 1024, // 10MB
            max_file_size: 500 * 1024 * 1024,      // 500MB
            buffer_size: 64 * 1024,                // 64KB
            max_memory_usage: 100 * 1024 * 1024,   // 100MB
            incremental_parsing: true,
        }
    }
}

/// Represents a chunk of file content for streaming processing
#[derive(Debug, Clone)]
pub struct ContentChunk {
    pub data: String,
    pub offset: usize,
    pub line_start: usize,
    pub line_end: usize,
    pub is_final: bool,
}

/// Configuration for streaming analysis
#[derive(Debug, Clone)]
pub struct StreamingAnalysisConfig {
    /// Repository size threshold for streaming mode
    pub repository_threshold: u64,
    /// File size threshold for streaming mode
    pub file_threshold: u64,
    /// Maximum memory usage per analysis
    pub max_memory_per_analysis: u64,
    /// Number of concurrent file processors
    pub concurrent_file_processors: usize,
    /// Buffer size for file reading
    pub read_buffer_size: usize,
    /// Enable incremental parsing
    pub incremental_parsing: bool,
    /// Memory pressure threshold
    pub memory_pressure_threshold: f64,
}

impl Default for StreamingAnalysisConfig {
    fn default() -> Self {
        Self {
            repository_threshold: 1024 * 1024 * 1024, // 1GB
            file_threshold: 10 * 1024 * 1024,         // 10MB
            max_memory_per_analysis: 500 * 1024 * 1024, // 500MB
            concurrent_file_processors: num_cpus::get().min(8),
            read_buffer_size: 64 * 1024,              // 64KB
            incremental_parsing: true,
            memory_pressure_threshold: 0.8,           // 80%
        }
    }
}

/// Streaming file processor for large repositories
pub struct StreamingFileProcessor {
    config: StreamingAnalysisConfig,
    memory_monitor: Arc<MemoryMonitor>,
    progress_reporter: Arc<dyn ProgressReporter>,
}

/// Memory monitoring for streaming processing
pub struct MemoryMonitor {
    max_memory: u64,
    current_usage: Arc<std::sync::atomic::AtomicU64>,
    pressure_callbacks: Arc<tokio::sync::RwLock<Vec<Box<dyn Fn(f64) + Send + Sync>>>>,
}

impl std::fmt::Debug for MemoryMonitor {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("MemoryMonitor")
            .field("max_memory", &self.max_memory)
            .field("current_usage", &self.current_usage.load(std::sync::atomic::Ordering::Relaxed))
            .field("pressure_callbacks_count", &"<callback_count>")
            .finish()
    }
}

impl MemoryMonitor {
    pub fn new(max_memory: u64) -> Self {
        Self {
            max_memory,
            current_usage: Arc::new(std::sync::atomic::AtomicU64::new(0)),
            pressure_callbacks: Arc::new(tokio::sync::RwLock::new(Vec::new())),
        }
    }

    pub fn get_usage(&self) -> u64 {
        self.current_usage.load(std::sync::atomic::Ordering::Relaxed)
    }

    pub fn get_pressure(&self) -> f64 {
        self.get_usage() as f64 / self.max_memory as f64
    }

    pub fn update_usage(&self, delta: i64) {
        let current = self.current_usage.load(std::sync::atomic::Ordering::Relaxed);
        let new_usage = if delta < 0 {
            current.saturating_sub((-delta) as u64)
        } else {
            current.saturating_add(delta as u64)
        };
        self.current_usage.store(new_usage, std::sync::atomic::Ordering::Relaxed);
        
        let pressure = self.get_pressure();
        if pressure > 0.8 {
            // Trigger pressure callbacks asynchronously
            let callbacks = self.pressure_callbacks.clone();
            tokio::spawn(async move {
                let callbacks = callbacks.read().await;
                for callback in callbacks.iter() {
                    callback(pressure);
                }
            });
        }
    }

    pub async fn add_pressure_callback<F>(&self, callback: F) 
    where
        F: Fn(f64) + Send + Sync + 'static,
    {
        let mut callbacks = self.pressure_callbacks.write().await;
        callbacks.push(Box::new(callback));
    }
}

/// Progress reporting trait for streaming analysis
pub trait ProgressReporter: Send + Sync {
    fn report_progress(&self, processed: usize, total: usize, message: &str);
    fn report_file_processed(&self, file_path: &str, success: bool);
    fn report_memory_usage(&self, usage: u64, max: u64);
}

/// Default progress reporter implementation
#[derive(Debug)]
pub struct DefaultProgressReporter {
    analysis_id: String,
    tx: Option<tokio::sync::mpsc::Sender<crate::models::ProgressUpdate>>,
}

impl DefaultProgressReporter {
    pub fn new(analysis_id: String, tx: Option<tokio::sync::mpsc::Sender<crate::models::ProgressUpdate>>) -> Self {
        Self { analysis_id, tx }
    }
}

impl ProgressReporter for DefaultProgressReporter {
    fn report_progress(&self, processed: usize, total: usize, message: &str) {
        let progress = (processed as f64 / total as f64) * 100.0;
        tracing::info!("Analysis {}: {:.2}% - {}", self.analysis_id, progress, message);
        
        if let Some(tx) = &self.tx {
            let update = crate::models::ProgressUpdate {
                analysis_id: self.analysis_id.clone(),
                progress,
                stage: message.to_string(),
                message: Some(format!("Processed {}/{} files", processed, total)),
                timestamp: chrono::Utc::now(),
                files_processed: Some(processed),
                total_files: Some(total),
            };
            
            let tx_clone = tx.clone();
            tokio::spawn(async move {
                if let Err(e) = tx_clone.send(update).await {
                    tracing::warn!("Failed to send progress update: {}", e);
                }
            });
        }
    }

    fn report_file_processed(&self, file_path: &str, success: bool) {
        let status = if success { "processed" } else { "failed" };
        tracing::debug!("File {}: {}", file_path, status);
    }

    fn report_memory_usage(&self, usage: u64, max: u64) {
        let percentage = (usage as f64 / max as f64) * 100.0;
        tracing::info!("Memory usage: {} MB ({:.1}%)", usage / 1024 / 1024, percentage);
    }
}

impl StreamingFileProcessor {
    pub fn new(
        config: StreamingAnalysisConfig,
        progress_reporter: Arc<dyn ProgressReporter>,
    ) -> Self {
        let memory_monitor = Arc::new(MemoryMonitor::new(config.max_memory_per_analysis));
        
        // Add memory pressure callback
        let reporter_clone = progress_reporter.clone();
        let memory_monitor_clone = memory_monitor.clone();
        tokio::spawn(async move {
            memory_monitor_clone.add_pressure_callback(move |pressure| {
                tracing::warn!("Memory pressure detected: {:.1}%", pressure * 100.0);
                if pressure > 0.9 {
                    tracing::error!("Critical memory pressure: {:.1}%", pressure * 100.0);
                }
            }).await;
        });
        
        Self {
            config,
            memory_monitor,
            progress_reporter,
        }
    }

    /// Process a repository using streaming if it's large enough
    pub async fn process_repository(
        &self,
        repo_path: &Path,
        parser: &TreeSitterParser,
        files: &[std::path::PathBuf],
    ) -> Result<Vec<Result<FileAnalysis, ParseError>>> {
        let repository_size = self.calculate_repository_size(repo_path).await?;
        
        if repository_size > self.config.repository_threshold {
            tracing::info!("Using streaming processing for large repository: {} MB", 
                         repository_size / 1024 / 1024);
            self.process_repository_streaming(repo_path, parser, files).await
        } else {
            tracing::info!("Using standard processing for repository: {} MB", 
                         repository_size / 1024 / 1024);
            self.process_repository_standard(parser, files).await
        }
    }

    /// Process repository using streaming approach
    async fn process_repository_streaming(
        &self,
        _repo_path: &Path,
        parser: &TreeSitterParser,
        files: &[std::path::PathBuf],
    ) -> Result<Vec<Result<FileAnalysis, ParseError>>> {
        let total_files = files.len();
        let mut results = Vec::with_capacity(total_files);
        let processed_count = Arc::new(std::sync::atomic::AtomicUsize::new(0));
        
        // Create semaphore to limit concurrent processing
        let semaphore = Arc::new(tokio::sync::Semaphore::new(self.config.concurrent_file_processors));
        
        // Process files in batches to manage memory
        let batch_size = (total_files / self.config.concurrent_file_processors).max(1);
        let mut handles = Vec::new();
        
        for batch in files.chunks(batch_size) {
            let batch_files = batch.to_vec();
            // let parser_clone = parser.clone(); // TODO: Fix parser cloning
            let semaphore_clone = semaphore.clone();
            let processed_count_clone = processed_count.clone();
            let memory_monitor_clone = self.memory_monitor.clone();
            let progress_reporter_clone = self.progress_reporter.clone();
            let config_clone = self.config.clone();
            
            let handle = tokio::spawn(async move {
                let mut batch_results = Vec::new();
                
                for file_path in batch_files {
                    // Check memory pressure before processing
                    let memory_pressure = memory_monitor_clone.get_pressure();
                    if memory_pressure > config_clone.memory_pressure_threshold {
                        tracing::warn!("Memory pressure too high ({:.1}%), throttling processing", 
                                     memory_pressure * 100.0);
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                        continue;
                    }
                    
                    let _permit = semaphore_clone.acquire().await.unwrap();
                    
                    // Estimate file size and choose processing method
                    let file_size = tokio::fs::metadata(&file_path).await
                        .map(|m| m.len())
                        .unwrap_or(0);
                    
                    let result = if file_size > config_clone.file_threshold {
                        // Self::process_large_file_streaming(&parser_clone, &file_path, &memory_monitor_clone).await
                        Err(ParseError { file_path: file_path.to_string_lossy().to_string(), error_type: ParseErrorType::Other, message: "Large file processing not implemented".to_string(), position: None })
                    } else {
                        // parser_clone.parse_file(&file_path).await
                        Err(ParseError { file_path: file_path.to_string_lossy().to_string(), error_type: ParseErrorType::Other, message: "File parsing not implemented".to_string(), position: None })
                    };
                    
                    let processed = processed_count_clone.fetch_add(1, std::sync::atomic::Ordering::Relaxed) + 1;
                    progress_reporter_clone.report_progress(processed, total_files, "Processing files");
                    progress_reporter_clone.report_file_processed(
                        &file_path.to_string_lossy(), 
                        result.is_ok()
                    );
                    
                    batch_results.push(result);
                }
                
                batch_results
            });
            
            handles.push(handle);
        }
        
        // Collect results from all batches
        for handle in handles {
            let batch_results = handle.await.context("Batch processing failed")?;
            results.extend(batch_results);
        }
        
        // Report final memory usage
        self.progress_reporter.report_memory_usage(
            self.memory_monitor.get_usage(),
            self.config.max_memory_per_analysis
        );
        
        Ok(results)
    }

    /// Process repository using standard approach
    async fn process_repository_standard(
        &self,
        parser: &TreeSitterParser,
        files: &[std::path::PathBuf],
    ) -> Result<Vec<Result<FileAnalysis, ParseError>>> {
        let mut results = Vec::with_capacity(files.len());
        
        for (i, file_path) in files.iter().enumerate() {
            let result = parser.parse_file(file_path).await;
            results.push(result);
            
            self.progress_reporter.report_progress(i + 1, files.len(), "Processing files");
        }
        
        Ok(results)
    }

    /// Process a large file using streaming approach
    async fn process_large_file_streaming(
        parser: &TreeSitterParser,
        file_path: &std::path::Path,
        memory_monitor: &MemoryMonitor,
    ) -> Result<FileAnalysis, ParseError> {
        tracing::info!("Processing large file with streaming: {}", file_path.display());
        
        let file_size = tokio::fs::metadata(file_path).await
            .map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to get file metadata: {}", e),
                position: None,
            })?
            .len();
        
        // Update memory usage estimation
        memory_monitor.update_usage(file_size as i64);
        
        // For now, fall back to standard parsing for large files
        // In a full implementation, this would do incremental parsing
        let result = parser.parse_file(file_path).await;
        
        // Update memory usage after processing
        memory_monitor.update_usage(-(file_size as i64));
        
        result
    }

    /// Calculate the total size of a repository
    async fn calculate_repository_size(&self, repo_path: &Path) -> Result<u64> {
        use std::pin::Pin;
        use std::future::Future;
        
        fn calc_size_recursive(path: &Path) -> Pin<Box<dyn Future<Output = Result<u64>> + Send + '_>> {
            Box::pin(async move {
                let mut total_size = 0u64;
                let mut entries = tokio::fs::read_dir(path).await?;
                
                while let Some(entry) = entries.next_entry().await? {
                    let path = entry.path();
                    if path.is_file() {
                        if let Ok(metadata) = tokio::fs::metadata(&path).await {
                            total_size += metadata.len();
                        }
                    } else if path.is_dir() {
                        // Recursively calculate subdirectory size
                        total_size += calc_size_recursive(&path).await?;
                    }
                }
                
                Ok(total_size)
            })
        }
        
        calc_size_recursive(repo_path).await
    }
}



/// Streaming hash calculator that can process content incrementally
pub struct StreamingHasher {
    hasher: Sha256,
    total_bytes: u64,
}

impl StreamingHasher {
    pub fn new() -> Self {
        Self {
            hasher: Sha256::new(),
            total_bytes: 0,
        }
    }

    pub fn update(&mut self, data: &[u8]) {
        self.hasher.update(data);
        self.total_bytes += data.len() as u64;
    }

    pub fn finalize(self) -> (String, u64) {
        let hash = format!("{:x}", self.hasher.finalize());
        (hash, self.total_bytes)
    }
}

/// Metrics for a content chunk
#[derive(Debug, Clone)]
struct ChunkMetrics {
    line_count: usize,
    char_count: usize,
    comment_lines: usize,
    blank_lines: usize,
    code_lines: usize,
}

impl TreeSitterParser {
    pub fn new() -> Result<Self> {
        Self::new_with_config(ParserPoolConfig::default())
    }

    pub fn new_with_config(config: ParserPoolConfig) -> Result<Self> {
        let mut parser_pools = HashMap::new();

        let language_names = [
            // Core languages (working)
            "rust", "javascript", "typescript", "python", "go", "java", "c", "cpp",
            
            // Web and markup languages (working)
            "html", "css", "json", "yaml", "ruby", "bash", "markdown",
            
            // Mobile development languages (working)
            "swift", "kotlin", "objc",
            
            // Data science and numerical computing (working)
            "r", "julia",
            
            // Functional programming languages (working)
            "haskell", "scala", "erlang", "elixir",
            
            // Systems programming languages (working)
            "zig", "d",
            
            // Other languages (working)
            "lua", "dart", "nix",
            
            // Custom parsers (handled separately)
            "sql", "toml",
        ];

        for lang_name in language_names {
            if let Some(language) = get_language_for_name(lang_name) {
                let pool = ParserPool::new(language, config.max_parsers_per_language)
                    .context(format!("Failed to create parser pool for {}", lang_name))?;
                parser_pools.insert(lang_name.to_string(), Arc::new(pool));
            } else {
                debug!("Language {} not available or not supported", lang_name);
            }
        }

        debug!("Created parser pools for {} languages with max {} parsers each",
               parser_pools.len(), config.max_parsers_per_language);

        let parser = Self {
            parser_pools,
            _pool_config: config.clone(),
            metrics_calculator: LanguageMetricsCalculator::new(),
        };
        
        // Start background pool optimization if enabled
        if config.enable_dynamic_sizing {
            parser.start_pool_optimization();
        }
        
        Ok(parser)
    }

    /// Start background pool optimization
    fn start_pool_optimization(&self) {
        let pools = self.parser_pools.clone();
        let config = self._pool_config.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(config.optimization_interval)
            );
            
            loop {
                interval.tick().await;
                
                for (lang, pool) in &pools {
                    if let Err(e) = pool.adjust_pool_size(config.target_utilization) {
                        tracing::warn!("Failed to adjust pool size for {}: {}", lang, e);
                    }
                }
            }
        });
    }

    /// Get statistics for all parser pools
    pub fn get_pool_stats(&self) -> HashMap<String, ParserPoolStats> {
        self.parser_pools
            .iter()
            .map(|(lang, pool)| (lang.clone(), pool.get_stats()))
            .collect()
    }

    /// Preload parsers for expected load
    pub fn preload_parsers(&self, expected_load: usize) -> Result<()> {
        for (lang, pool) in &self.parser_pools {
            if let Err(e) = pool.preload_parsers(expected_load) {
                tracing::warn!("Failed to preload parsers for {}: {}", lang, e);
            }
        }
        Ok(())
    }

    /// Warm up all parser pools
    pub fn warm_up_pools(&self) -> Result<()> {
        for (lang, pool) in &self.parser_pools {
            if let Err(e) = pool.warm_up_pool() {
                tracing::warn!("Failed to warm up pool for {}: {}", lang, e);
            }
        }
        Ok(())
    }

    pub async fn parse_file(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        self.parse_file_with_config(file_path, &StreamingConfig::default()).await
    }

    pub async fn parse_file_with_config(&self, file_path: &Path, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        // Check file size to determine processing strategy
        let metadata = tokio::fs::metadata(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to get file metadata: {}", e),
            position: None,
        })?;

        let file_size = metadata.len();

        // Check if file is too large
        if file_size > config.max_file_size {
            return Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::FileTooLarge,
                message: format!("File too large: {} bytes (max: {} bytes)", file_size, config.max_file_size),
                position: None,
            });
        }

        if file_size > config.streaming_threshold {
            // Use streaming for large files
            self.parse_file_streaming(file_path, file_size, config).await
        } else {
            // Regular processing for smaller files
            self.parse_file_regular(file_path).await
        }
    }

    async fn parse_file_regular(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        let content = tokio::fs::read_to_string(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: e.to_string(),
            position: None,
        })?;
        
        self.parse_content(file_path, &content).await
    }

    async fn parse_file_streaming(&self, file_path: &Path, file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        tracing::debug!("Parsing large file with streaming: {} ({} bytes)", file_path.display(), file_size);
        
        // For very large files, we need to be more careful with memory usage
        if file_size > config.max_memory_usage {
            return self.parse_file_chunked(file_path, file_size, config).await;
        }
        
        // For moderately large files, still read all at once but with streaming hash calculation
        let content = tokio::fs::read_to_string(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: e.to_string(),
            position: None,
        })?;
        
        // Use streaming hash calculation for large files
        let mut hasher = StreamingHasher::new();
        hasher.update(content.as_bytes());
        let (content_hash, _) = hasher.finalize();
        
        let mut analysis = self.parse_content(file_path, &content).await?;
        analysis.content_hash = content_hash;
        
        Ok(analysis)
    }

    async fn parse_file_chunked(&self, file_path: &Path, file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        tracing::warn!("Parsing very large file with chunked approach: {} ({} bytes)", file_path.display(), file_size);
        
        // For extremely large files, we need to implement chunked parsing
        // For now, we'll implement a simplified version that processes the file in chunks
        // but still tries to parse the whole thing
        
        let mut file = File::open(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: e.to_string(),
            position: None,
        })?;
        
        let mut reader = BufReader::new(file);
        let mut content = String::new();
        let mut hasher = StreamingHasher::new();
        
        // Read file in chunks to avoid loading entire file into memory at once
        loop {
            let mut buffer = vec![0u8; config.buffer_size];
            let bytes_read = tokio::io::AsyncReadExt::read(&mut reader, &mut buffer).await.map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: e.to_string(),
                position: None,
            })?;
            
            if bytes_read == 0 {
                break;
            }
            
            buffer.truncate(bytes_read);
            hasher.update(&buffer);
            
            // Convert to string and append
            let chunk_str = String::from_utf8_lossy(&buffer);
            content.push_str(&chunk_str);
            
            // Check if we're approaching memory limits
            if content.len() > config.max_memory_usage as usize {
                tracing::warn!("File too large for chunked processing, truncating analysis");
                break;
            }
        }
        
        let (content_hash, _) = hasher.finalize();
        
        let mut analysis = self.parse_content(file_path, &content).await?;
        analysis.content_hash = content_hash;
        
        Ok(analysis)
    }

    pub async fn parse_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        let language = self.detect_language(file_path)?;

        // Handle SQL, XML, TOML, and Markdown with custom adapters
        match language {
            "sql" => return self.parse_sql_content(file_path, content).await,
            "xml" => return self.parse_xml_content(file_path, content).await,
            "toml" => return self.parse_toml_content(file_path, content).await,
            "markdown" => return self.parse_markdown_content(file_path, content).await,
            _ => {}
        }

        let parser_pool = self.parser_pools.get(language).ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::UnsupportedLanguage,
            message: format!("Unsupported language: {}", language),
            position: None,
        })?;

        // Get a parser from the pool
        let mut parser = parser_pool.get_parser().await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to get parser from pool: {}", e),
            position: None,
        })?;

        // Parse the content
        let tree = parser.parse(&content, None).ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::ParseError,
            message: "Failed to parse file".to_string(),
            position: None,
        })?;

        // Return parser to pool for reuse
        parser_pool.return_parser(parser);
        
        let root_node = tree.root_node();
        let ast = self.build_ast(&root_node, &content);
        let symbols = self.extract_symbols(&root_node, &content);
        let metadata = self.extract_metadata(&content, language);

        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Extract chunks for embedding
        let chunks = self.extract_chunks(&ast, &content, language);
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: language.to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

    /// Parse large files using streaming to minimize memory usage (DUPLICATE - COMMENTED OUT)
    /*async fn parse_file_streaming(&self, file_path: &Path, file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        let _language = self.detect_language(file_path)?;

        // For very large files, we need to balance between memory usage and parsing accuracy
        // Strategy: Read file in chunks, but still need full content for tree-sitter parsing
        // We'll use a hybrid approach: stream for hash calculation and basic metrics,
        // but load content in manageable chunks for AST parsing

        if file_size > config.max_memory_usage {
            // For extremely large files, use chunk-based processing
            self.parse_file_chunked(file_path, file_size, config).await
        } else {
            // For moderately large files, use streaming read but full AST parsing
            self.parse_file_streaming_hybrid(file_path, file_size, config).await
        }
    }*/

    /// Hybrid streaming approach: stream for I/O but full parsing
    async fn parse_file_streaming_hybrid(&self, file_path: &Path, file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        let _language = self.detect_language(file_path)?;

        // Stream the file content while calculating hash and basic metrics
        let content_chunks = self.create_content_stream(file_path, config).await?;
        let mut streaming_hasher = StreamingHasher::new();
        let mut accumulated_content = String::with_capacity(std::cmp::min(file_size as usize, config.max_memory_usage as usize));
        let mut line_count = 0;
        let mut _char_count = 0;

        for chunk in content_chunks {
            // Update hash incrementally
            streaming_hasher.update(chunk.data.as_bytes());

            // Count lines and characters
            line_count += chunk.data.lines().count();
            _char_count += chunk.data.chars().count();

            // Accumulate content for AST parsing (with memory limit check)
            if accumulated_content.len() + chunk.data.len() <= config.max_memory_usage as usize {
                accumulated_content.push_str(&chunk.data);
            } else {
                // If we exceed memory limit, truncate and add warning
                tracing::warn!("File {} too large for full AST parsing, truncating at {} bytes",
                             file_path.display(), accumulated_content.len());
                break;
            }
        }

        let (content_hash, total_bytes) = streaming_hasher.finalize();

        // Parse the accumulated content (may be truncated for very large files)
        let analysis = self.parse_content(file_path, &accumulated_content).await?;

        // Override the hash and size with streaming results
        Ok(FileAnalysis {
            path: analysis.path,
            language: analysis.language,
            content_hash,
            size_bytes: Some(total_bytes),
            ast: analysis.ast,
            metrics: FileMetrics {
                lines_of_code: analysis.metrics.lines_of_code,
                total_lines: Some(line_count as u32),
                complexity: analysis.metrics.complexity,
                maintainability_index: analysis.metrics.maintainability_index,
                function_count: analysis.metrics.function_count,
                class_count: analysis.metrics.class_count,
                comment_ratio: analysis.metrics.comment_ratio,
            },
            chunks: analysis.chunks,
            symbols: analysis.symbols,
        })
    }

    /// Process extremely large files in chunks without loading full content (DUPLICATE - COMMENTED OUT)
    /*async fn parse_file_chunked(&self, file_path: &Path, _file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        let language = self.detect_language(file_path)?;

        // For extremely large files, we can't do full AST parsing
        // Instead, we'll do statistical analysis and pattern-based extraction
        let content_chunks = self.create_content_stream(file_path, config).await?;
        let mut streaming_hasher = StreamingHasher::new();

        // Metrics tracking
        let mut line_count = 0;
        let mut _char_count = 0;
        let mut comment_lines = 0;
        let mut _blank_lines = 0;
        let mut code_lines = 0;

        // Symbol extraction using regex patterns (language-specific)
        let mut symbols = Vec::new();
        let mut chunks = Vec::new();

        let mut current_line = 0;
        let mut current_offset = 0;

        for chunk in content_chunks {

            // Update hash incrementally
            streaming_hasher.update(chunk.data.as_bytes());

            // Process chunk for metrics and symbols
            let chunk_metrics = self.analyze_chunk(&chunk.data, language, current_line);
            line_count += chunk_metrics.line_count;
            _char_count += chunk_metrics.char_count;
            comment_lines += chunk_metrics.comment_lines;
            _blank_lines += chunk_metrics.blank_lines;
            code_lines += chunk_metrics.code_lines;

            // Extract symbols from chunk using pattern matching
            let chunk_symbols = self.extract_symbols_from_chunk(&chunk.data, language, current_line, current_offset);
            symbols.extend(chunk_symbols);

            // Create code chunks for embedding
            let code_chunks = self.create_chunks_from_content(&chunk.data, language, current_line, current_offset);
            chunks.extend(code_chunks);

            current_line += chunk.data.lines().count();
            current_offset += chunk.data.len();
        }

        let (content_hash, total_bytes) = streaming_hasher.finalize();

        // Create a minimal AST node for the root
        let root_ast = AstNode {
            node_type: "source_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: line_count as u32, column: 0, byte: total_bytes as u32 },
            },
            children: Vec::new(), // No detailed AST for chunked processing
            properties: None,
            text: None,
        };

        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: language.to_string(),
            content_hash,
            size_bytes: Some(total_bytes),
            ast: root_ast,
            metrics: FileMetrics {
                lines_of_code: code_lines as u32,
                total_lines: Some(line_count as u32),
                complexity: 1, // Minimal complexity for chunked processing
                maintainability_index: 50.0, // Default maintainability
                function_count: 0,
                class_count: 0,
                comment_ratio: if line_count > 0 { comment_lines as f64 / line_count as f64 } else { 0.0 },
            },
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }*/

    /// Create a stream of content chunks from a file
    async fn create_content_stream(&self, file_path: &Path, config: &StreamingConfig) -> Result<Vec<ContentChunk>, ParseError> {
        let file = File::open(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to open file: {}", e),
            position: None,
        })?;

        let reader = BufReader::with_capacity(config.buffer_size, file);
        let mut lines = reader.lines();
        let mut chunks = Vec::new();
        let mut current_offset = 0;
        let mut current_line = 0;
        let mut buffer = String::new();
        let chunk_size = config.buffer_size;

        loop {
            buffer.clear();
            let mut bytes_read = 0;
            let start_line = current_line;

            // Read lines until we reach the chunk size
            while bytes_read < chunk_size {
                match lines.next_line().await {
                    Ok(Some(line)) => {
                        if !buffer.is_empty() {
                            buffer.push('\n');
                            bytes_read += 1;
                        }
                        buffer.push_str(&line);
                        bytes_read += line.len();
                        current_line += 1;
                    }
                    Ok(None) => {
                        // End of file
                        if !buffer.is_empty() {
                            chunks.push(ContentChunk {
                                data: buffer.clone(),
                                offset: current_offset,
                                line_start: start_line,
                                line_end: current_line,
                                is_final: true,
                            });
                        }
                        return Ok(chunks);
                    }
                    Err(e) => {
                        return Err(ParseError {
                            file_path: file_path.to_string_lossy().to_string(),
                            error_type: ParseErrorType::Other,
                            message: format!("Failed to read line: {}", e),
                            position: None,
                        });
                    }
                }
            }

            if !buffer.is_empty() {
                chunks.push(ContentChunk {
                    data: buffer.clone(),
                    offset: current_offset,
                    line_start: start_line,
                    line_end: current_line,
                    is_final: false,
                });
                current_offset += buffer.len();
            }
        }
    }

    /// Analyze a chunk of content for basic metrics
    fn analyze_chunk(&self, content: &str, language: &str, _start_line: usize) -> ChunkMetrics {
        let lines: Vec<&str> = content.lines().collect();
        let line_count = lines.len();
        let char_count = content.chars().count();

        let mut comment_lines = 0;
        let mut blank_lines = 0;
        let mut code_lines = 0;

        // Language-specific comment detection
        let (single_comment, multi_comment_start, multi_comment_end) = match language {
            "rust" | "javascript" | "typescript" | "java" | "c" | "cpp" | "go" => ("//", "/*", "*/"),
            "python" | "ruby" => ("#", "\"\"\"", "\"\"\""),
            "html" | "xml" => ("", "<!--", "-->"),
            "css" => ("", "/*", "*/"),
            _ => ("//", "/*", "*/"), // Default
        };

        let mut in_multiline_comment = false;

        for line in lines {
            let trimmed = line.trim();

            if trimmed.is_empty() {
                blank_lines += 1;
            } else if !single_comment.is_empty() && trimmed.starts_with(single_comment) {
                comment_lines += 1;
            } else if !multi_comment_start.is_empty() && (trimmed.starts_with(multi_comment_start) || in_multiline_comment) {
                comment_lines += 1;
                if trimmed.contains(multi_comment_end) {
                    in_multiline_comment = false;
                } else if trimmed.starts_with(multi_comment_start) {
                    in_multiline_comment = true;
                }
            } else {
                code_lines += 1;
            }
        }

        ChunkMetrics {
            line_count,
            char_count,
            comment_lines,
            blank_lines,
            code_lines,
        }
    }

    /// Extract symbols from a chunk using pattern matching
    fn extract_symbols_from_chunk(&self, content: &str, language: &str, start_line: usize, start_offset: usize) -> Vec<Symbol> {
        let mut symbols = Vec::new();

        // Language-specific regex patterns for symbol extraction
        let patterns = match language {
            "rust" => vec![
                (r"fn\s+(\w+)", SymbolType::Function),
                (r"struct\s+(\w+)", SymbolType::Class),
                (r"enum\s+(\w+)", SymbolType::Class),
                (r"trait\s+(\w+)", SymbolType::Interface),
                (r"impl.*?(\w+)", SymbolType::Class),
                (r"const\s+(\w+)", SymbolType::Variable),
                (r"static\s+(\w+)", SymbolType::Variable),
            ],
            "python" => vec![
                (r"def\s+(\w+)", SymbolType::Function),
                (r"class\s+(\w+)", SymbolType::Class),
                (r"async\s+def\s+(\w+)", SymbolType::Function),
            ],
            "javascript" | "typescript" => vec![
                (r"function\s+(\w+)", SymbolType::Function),
                (r"class\s+(\w+)", SymbolType::Class),
                (r"const\s+(\w+)\s*=", SymbolType::Variable),
                (r"let\s+(\w+)\s*=", SymbolType::Variable),
                (r"var\s+(\w+)\s*=", SymbolType::Variable),
            ],
            "java" => vec![
                (r"public\s+class\s+(\w+)", SymbolType::Class),
                (r"private\s+class\s+(\w+)", SymbolType::Class),
                (r"protected\s+class\s+(\w+)", SymbolType::Class),
                (r"public\s+\w+\s+(\w+)\s*\(", SymbolType::Function),
                (r"private\s+\w+\s+(\w+)\s*\(", SymbolType::Function),
                (r"protected\s+\w+\s+(\w+)\s*\(", SymbolType::Function),
            ],
            _ => vec![], // No patterns for unsupported languages
        };

        // Simple pattern matching without regex for now
        for (pattern_prefix, symbol_type) in patterns {
            let prefix = pattern_prefix.split('(').next().unwrap_or("").trim();

            for (line_idx, line) in content.lines().enumerate() {
                let trimmed_line = line.trim();
                if trimmed_line.starts_with(prefix) {
                    // Extract symbol name using simple string parsing
                    if let Some(name) = extract_symbol_name(trimmed_line, prefix) {
                        symbols.push(Symbol {
                            name,
                            symbol_type: symbol_type.clone(),
                            range: Range {
                                start: Position {
                                    line: (start_line + line_idx) as u32,
                                    column: 0,
                                    byte: start_offset as u32,
                                },
                                end: Position {
                                    line: (start_line + line_idx) as u32,
                                    column: line.len() as u32,
                                    byte: (start_offset + line.len()) as u32,
                                },
                            },
                            visibility: None,
                            signature: None,
                            documentation: None,
                        });
                    }
                }
            }
        }

        symbols
    }

    /// Create code chunks from content for embedding
    fn create_chunks_from_content(&self, content: &str, language: &str, start_line: usize, start_offset: usize) -> Vec<CodeChunk> {
        let mut chunks = Vec::new();
        let lines: Vec<&str> = content.lines().collect();

        // Create chunks based on logical boundaries (functions, classes, etc.)
        const CHUNK_SIZE: usize = 50; // Lines per chunk
        const OVERLAP: usize = 5;     // Overlapping lines between chunks

        let mut i = 0;
        while i < lines.len() {
            let end = std::cmp::min(i + CHUNK_SIZE, lines.len());
            let chunk_lines = &lines[i..end];
            let chunk_content = chunk_lines.join("\n");

            if !chunk_content.trim().is_empty() {
                let content_len = chunk_content.len();
                chunks.push(CodeChunk {
                    chunk_id: format!("chunk_{}_{}", start_line + i, start_line + end - 1),
                    content: chunk_content,
                    range: Range {
                        start: Position {
                            line: (start_line + i) as u32,
                            column: 0,
                            byte: start_offset as u32,
                        },
                        end: Position {
                            line: (start_line + end - 1) as u32,
                            column: 0,
                            byte: (start_offset + content_len) as u32,
                        },
                    },
                    chunk_type: ChunkType::Function, // Could be enhanced with better detection
                    language: Some(language.to_string()),
                    context: None, // Could be populated with more context
                });
            }

            // Move forward with overlap
            i += CHUNK_SIZE - OVERLAP;
            if i >= lines.len() {
                break;
            }
        }

        chunks
    }

    /// Detect language from file path - returns owned string for better flexibility
    pub fn detect_language_from_path(&self, file_path: &Path) -> Result<String, ParseError> {
        let extension = file_path.extension().and_then(|s| s.to_str()).unwrap_or("");
        let filename = file_path.file_name().and_then(|s| s.to_str()).unwrap_or("");
        
        // Handle special filenames first
        match filename.to_lowercase().as_str() {
            _ => {}
        }
        
        // Handle extensions
        let language = match extension {
            // Core languages
            "rs" => "rust",
            "py" | "pyi" | "pyx" | "pyw" => "python",
            "js" | "mjs" | "cjs" => "javascript",
            "ts" | "tsx" | "mts" | "cts" => "typescript",
            "go" => "go",
            "java" => "java",
            "c" | "h" => "c",
            "cpp" | "cc" | "cxx" | "hpp" | "hxx" | "c++" | "h++" => "cpp",
            
            // Web and markup languages
            "html" | "htm" | "xhtml" => "html",
            "css" | "scss" | "sass" | "less" => "css",
            "json" | "jsonc" => "json",
            "yaml" | "yml" => "yaml",
            "php" | "php3" | "php4" | "php5" | "phtml" => "php",
            "rb" | "rbw" | "rake" | "gemfile" => "ruby",
            "sh" | "bash" | "zsh" | "fish" => "bash",
            "md" | "markdown" | "mdown" | "mkd" | "mkdn" => "markdown",
            "xml" | "xsd" | "xsl" | "xslt" | "svg" | "xaml" => "xml",
            
            // Mobile development languages
            "swift" => "swift",
            "kt" | "kts" => "kotlin",
            "m" | "mm" => "objc",
            
            // Data science and numerical computing
            "r" | "R" => "r",
            "jl" => "julia",
            
            // Functional programming languages
            "hs" | "lhs" => "haskell",
            "scala" | "sc" => "scala",
            "erl" | "hrl" => "erlang",
            "ex" | "exs" => "elixir",
            
            // Systems programming languages
            "zig" => "zig",
            "d" | "di" => "d",
            
            // Other languages
            "lua" => "lua",
            "dart" => "dart",
            "ml" | "mli" | "mll" | "mly" => "ocaml",
            "nix" => "nix",
            
            // Configuration and build files
            "sql" => "sql",
            "toml" => "toml",
            
            _ => return Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: format!("Unsupported file extension: {} (filename: {})", extension, filename),
                position: None,
            }),
        };
        
        Ok(language.to_string())
    }

    fn detect_language<'a>(&self, file_path: &'a Path) -> Result<&'a str, ParseError> {
        let extension = file_path.extension().and_then(|s| s.to_str()).unwrap_or("");
        let filename = file_path.file_name().and_then(|s| s.to_str()).unwrap_or("");
        
        // Handle special filenames first
        match filename.to_lowercase().as_str() {
            _ => {}
        }
        
        // Handle extensions
        match extension {
            // Core languages
            "rs" => Ok("rust"),
            "py" | "pyi" | "pyx" | "pyw" => Ok("python"),
            "js" | "mjs" | "cjs" => Ok("javascript"),
            "ts" | "tsx" | "mts" | "cts" => Ok("typescript"),
            "go" => Ok("go"),
            "java" => Ok("java"),
            "c" | "h" => Ok("c"),
            "cpp" | "cc" | "cxx" | "hpp" | "hxx" | "c++" | "h++" => Ok("cpp"),
            
            // Web and markup languages
            "html" | "htm" | "xhtml" => Ok("html"),
            "css" | "scss" | "sass" | "less" => Ok("css"),
            "json" | "jsonc" => Ok("json"),
            "yaml" | "yml" => Ok("yaml"),
            "php" | "php3" | "php4" | "php5" | "phtml" => Ok("php"),
            "rb" | "rbw" | "rake" | "gemfile" => Ok("ruby"),
            "sh" | "bash" | "zsh" | "fish" => Ok("bash"),
            "md" | "markdown" | "mdown" | "mkd" | "mkdn" => Ok("markdown"),
            "xml" | "xsd" | "xsl" | "xslt" | "svg" | "xaml" => Ok("xml"),
            
            // Mobile development languages
            "swift" => Ok("swift"),
            "kt" | "kts" => Ok("kotlin"),
            "m" | "mm" => Ok("objc"),
            
            // Data science and numerical computing
            "r" | "R" => Ok("r"),
            "jl" => Ok("julia"),
            
            // Functional programming languages
            "hs" | "lhs" => Ok("haskell"),
            "scala" | "sc" => Ok("scala"),
            "erl" | "hrl" => Ok("erlang"),
            "ex" | "exs" => Ok("elixir"),
            
            // Systems programming languages
            "zig" => Ok("zig"),
            "d" | "di" => Ok("d"),
            
            // Other languages
            "lua" => Ok("lua"),
            "dart" => Ok("dart"),
            "ml" | "mli" | "mll" | "mly" => Ok("ocaml"),
            "nix" => Ok("nix"),
            
            // Configuration and build files
            "sql" => Ok("sql"),
            "toml" => Ok("toml"),
            
            _ => Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: format!("Unsupported file extension: {} (filename: {})", extension, filename),
                position: None,
            }),
        }
    }

    fn build_ast(&self, node: &Node, source: &str) -> AstNode {
        self.build_ast_optimized(node, source, 0, 100) // Max depth of 100
    }
    
    fn build_ast_optimized(&self, node: &Node, source: &str, depth: usize, max_depth: usize) -> AstNode {
        // Optimization: Skip deep traversal for performance
        if depth > max_depth {
            return AstNode {
                node_type: "truncated".to_string(),
                name: Some("...".to_string()),
                range: Range {
                    start: self.convert_position(node.start_position()),
                    end: self.convert_position(node.end_position()),
                },
                children: vec![],
                properties: None,
                text: Some("/* Deep tree truncated for performance */".to_string()),
            };
        }
        
        // Optimization: Only extract text for leaf nodes or important nodes
        let should_extract_text = node.child_count() == 0 || matches!(
            node.kind(),
            "string_literal" | "number_literal" | "identifier" | "comment"
        );
        
        let text = if should_extract_text {
            node.utf8_text(source.as_bytes()).ok().map(|s| s.to_string())
        } else {
            None
        };
        
        let name = node.child_by_field_name("name")
            .and_then(|n| n.utf8_text(source.as_bytes()).ok())
            .map(|s| s.to_string());
        
        // Optimization: Use iterator with early termination for large child lists
        let children: Vec<AstNode> = if node.child_count() > 1000 {
            // For extremely large nodes, sample children
            node.children(&mut node.walk())
                .step_by(10) // Take every 10th child
                .take(100)   // Maximum 100 children
                .map(|child| self.build_ast_optimized(&child, source, depth + 1, max_depth))
                .collect()
        } else {
            node.children(&mut node.walk())
                .map(|child| self.build_ast_optimized(&child, source, depth + 1, max_depth))
                .collect()
        };
        
        AstNode {
            node_type: node.kind().to_string(),
            name,
            range: Range {
                start: Position { 
                    line: node.start_position().row as u32,
                    column: node.start_position().column as u32,
                    byte: node.start_byte() as u32,
                },
                end: Position {
                    line: node.end_position().row as u32,
                    column: node.end_position().column as u32,
                    byte: node.end_byte() as u32,
                },
            },
            children,
            properties: None,
            text,
        }
    }

    fn extract_symbols(&self, node: &Node, source: &str) -> Vec<Symbol> {
        let mut symbols = Vec::new();
        self.traverse_for_symbols(node, source, &mut symbols);
        symbols
    }

    fn traverse_for_symbols(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>) {
        self.traverse_for_symbols_optimized(node, source, symbols, 0, 50);
    }
    
    fn traverse_for_symbols_optimized(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>, depth: usize, max_depth: usize) {
        // Optimization: Stop at maximum depth to prevent stack overflow
        if depth > max_depth {
            return;
        }
        
        // Optimization: Early exit for nodes that can't contain symbols
        let node_kind = node.kind();
        if matches!(node_kind, "comment" | "string" | "string_literal" | "number" | "number_literal") {
            return;
        }
        
        let symbol_type = match node_kind {
            "function_item" | "function_declaration" | "method_definition" | "function_definition" => Some(SymbolType::Function),
            "struct_item" | "class_declaration" | "class_definition" => Some(SymbolType::Class),
            "let_declaration" | "const_item" | "variable_declarator" | "const_declaration" => Some(SymbolType::Variable),
            "trait_item" | "interface_declaration" => Some(SymbolType::Interface),
            _ => None,
        };

        if let Some(st) = symbol_type {
            let name_node = node.child_by_field_name("name").or_else(|| node.child_by_field_name("identifier"));
            if let Some(name_node) = name_node {
                if let Ok(name_text) = name_node.utf8_text(source.as_bytes()) {
                    // Optimization: Skip anonymous symbols
                    if !name_text.is_empty() && !name_text.starts_with('_') {
                        symbols.push(Symbol {
                            name: name_text.to_string(),
                            symbol_type: st,
                            range: Range {
                                start: self.convert_position(node.start_position()),
                                end: self.convert_position(node.end_position()),
                            },
                            visibility: None,
                            signature: None,
                            documentation: None,
                        });
                    }
                }
            }
        }

        // Optimization: Use cursor for more efficient traversal
        let mut cursor = node.walk();
        for child in node.children(&mut cursor) {
            self.traverse_for_symbols_optimized(&child, source, symbols, depth + 1, max_depth);
        }
    }

    fn convert_position(&self, point: tree_sitter::Point) -> Position {
        Position {
            line: point.row as u32,
            column: point.column as u32,
            byte: 0, // Tree-sitter doesn't provide byte offset directly in Point - will be set during parsing
        }
    }

    fn extract_metadata(&self, content: &str, language: &str) -> FileMetrics {
        // Use language-specific metrics calculator
        self.metrics_calculator.calculate_metrics(language, content)
    }
    
    fn extract_chunks(&self, node: &AstNode, source: &str, language: &str) -> Vec<CodeChunk> {
        let mut chunks = Vec::new();
        let mut chunk_counter = 0;
        
        self.extract_chunks_recursive(node, source, language, &mut chunks, &mut chunk_counter);
        chunks
    }
    
    fn extract_chunks_recursive(&self, node: &AstNode, source: &str, language: &str, chunks: &mut Vec<CodeChunk>, counter: &mut usize) {
        // Determine if this node should be a chunk
        let chunk_type = match node.node_type.as_str() {
            "function_item" | "function_declaration" => Some(ChunkType::Function),
            "struct_item" | "class_declaration" => Some(ChunkType::Class),
            "impl_item" | "method_declaration" => Some(ChunkType::Method),
            "block_statement" | "block" => Some(ChunkType::Block),
            "comment" | "line_comment" | "block_comment" => Some(ChunkType::Comment),
            "use_declaration" | "import_statement" | "import_declaration" => Some(ChunkType::Import),
            _ => None,
        };
        
        if let Some(ct) = chunk_type {
            *counter += 1;
            let chunk_id = format!("chunk_{:016x}", counter);
            
            // For optimized AST, we may need to extract text from source using range
            let text = if let Some(ref node_text) = node.text {
                node_text.clone()
            } else if !source.is_empty() {
                // Extract text from source using byte positions
                // This is a fallback for when the AST node doesn't have text
                let start_byte = node.range.start.byte as usize;
                let end_byte = node.range.end.byte as usize;
                
                if start_byte < source.len() && end_byte <= source.len() && start_byte < end_byte {
                    source[start_byte..end_byte].to_string()
                } else {
                    // If byte positions are not set correctly, try to extract by line/column
                    String::new()
                }
            } else {
                String::new()
            };
            
            if !text.is_empty() && text.len() <= 8192 { // Max length from contract
                chunks.push(CodeChunk {
                    chunk_id,
                    content: text,
                    range: node.range.clone(),
                    chunk_type: ct,
                    language: Some(language.to_string()),
                    context: None,
                });
            }
        }
        
        // Recurse into children
        for child in &node.children {
            self.extract_chunks_recursive(child, source, language, chunks, counter);
        }
    }
    
    async fn parse_sql_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        // Use SQL adapter for parsing
        let symbols = SqlAdapter::parse_sql(content)?;
        let chunks = vec![]; // SQL chunks extraction not implemented yet
        let metadata = self.extract_metadata(content, "sql");
        
        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Create a minimal AST for SQL
        let ast = AstNode {
            node_type: "sql_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: Vec::new(),
            properties: None,
            text: None,
        };
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "sql".to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }
    
    async fn parse_xml_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        // Use XML adapter for parsing
        let symbols = XmlAdapter::parse_xml(content)?;
        let chunks = vec![]; // XML chunks extraction not implemented yet
        let metadata = self.extract_metadata(content, "xml");
        
        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Create a minimal AST for XML
        let ast = AstNode {
            node_type: "xml_document".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: Vec::new(),
            properties: None,
            text: None,
        };
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "xml".to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }
    
    async fn parse_toml_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        // Use TOML adapter for parsing
        let symbols = TomlAdapter::parse_toml(content)?;
        let chunks = vec![]; // TOML chunks extraction not implemented yet
        let metadata = self.extract_metadata(content, "toml");
        
        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Create a minimal AST for TOML
        let ast = AstNode {
            node_type: "toml_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: Vec::new(),
            properties: None,
            text: None,
        };
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "toml".to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

    async fn parse_markdown_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        // Use Markdown adapter for parsing
        let symbols = MarkdownAdapter::parse_markdown(content)?;
        let chunks = vec![]; // Markdown chunks extraction not implemented yet
        let metadata = self.extract_metadata(content, "markdown");
        
        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Create a minimal AST for Markdown
        let ast = AstNode {
            node_type: "markdown_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: Vec::new(),
            properties: None,
            text: None,
        };
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "markdown".to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

}

/// Extract symbol name from a line using simple string parsing
fn extract_symbol_name(line: &str, prefix: &str) -> Option<String> {
    let after_prefix = line.strip_prefix(prefix)?.trim();

    // Handle different patterns
    if prefix.contains("fn") || prefix.contains("function") || prefix.contains("def") {
        // Function patterns: "fn name(" or "function name(" or "def name("
        if let Some(paren_pos) = after_prefix.find('(') {
            let name = after_prefix[..paren_pos].trim();
            if !name.is_empty() && name.chars().all(|c| c.is_alphanumeric() || c == '_') {
                return Some(name.to_string());
            }
        }
    } else if prefix.contains("class") || prefix.contains("struct") || prefix.contains("enum") || prefix.contains("trait") {
        // Type patterns: "class Name" or "struct Name"
        let name = after_prefix.split_whitespace().next()?;
        if !name.is_empty() && name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Some(name.to_string());
        }
    } else if prefix.contains("const") || prefix.contains("let") || prefix.contains("var") {
        // Variable patterns: "const name =" or "let name ="
        if let Some(eq_pos) = after_prefix.find('=') {
            let name = after_prefix[..eq_pos].trim();
            if !name.is_empty() && name.chars().all(|c| c.is_alphanumeric() || c == '_') {
                return Some(name.to_string());
            }
        }
    }

    None
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;

    fn create_test_file(dir: &TempDir, name: &str, content: &str) -> std::path::PathBuf {
        let file_path = dir.path().join(name);
        fs::write(&file_path, content).unwrap();
        file_path
    }

    #[tokio::test]
    async fn test_new_parser_creation() {
        let parser = TreeSitterParser::new().unwrap();
        assert_eq!(parser.parser_pools.len(), 15); // 15 tree-sitter languages (SQL and XML use adapters, PHP commented out)
        assert!(parser.parser_pools.contains_key("rust"));
        assert!(parser.parser_pools.contains_key("python"));
        assert!(parser.parser_pools.contains_key("javascript"));
        assert!(parser.parser_pools.contains_key("typescript"));
        assert!(parser.parser_pools.contains_key("go"));
        assert!(parser.parser_pools.contains_key("java"));
        assert!(parser.parser_pools.contains_key("c"));
        assert!(parser.parser_pools.contains_key("cpp"));
        assert!(parser.parser_pools.contains_key("html"));
        assert!(parser.parser_pools.contains_key("css"));
        assert!(parser.parser_pools.contains_key("json"));
        assert!(parser.parser_pools.contains_key("yaml"));
        // assert!(parser.parser_pools.contains_key("php")); // PHP commented out due to version conflicts
        assert!(parser.parser_pools.contains_key("ruby"));
        assert!(parser.parser_pools.contains_key("bash"));
        assert!(parser.parser_pools.contains_key("markdown"));
    }

    #[tokio::test]
    async fn test_parser_pool_functionality() {
        let config = ParserPoolConfig {
            max_parsers_per_language: 2,
            initial_parsers_per_language: 1,
            target_utilization: 75.0,
            enable_dynamic_sizing: true,
            optimization_interval: 30,
        };
        let parser = TreeSitterParser::new_with_config(config).unwrap();

        // Test that we can get a parser from the pool
        let rust_pool = parser.parser_pools.get("rust").unwrap();
        let parser1 = rust_pool.get_parser().await.unwrap();

        // Return it to the pool
        rust_pool.return_parser(parser1);

        // Test concurrent access
        let parser1 = rust_pool.get_parser().await.unwrap();
        let parser2 = rust_pool.get_parser().await.unwrap();

        // Both should be valid parsers
        rust_pool.return_parser(parser1);
        rust_pool.return_parser(parser2);
    }

    #[tokio::test]
    async fn test_concurrent_parsing_performance() {
        use std::time::Instant;
        use tokio::task::JoinSet;

        let config = ParserPoolConfig {
            max_parsers_per_language: 4,
            initial_parsers_per_language: 1,
            target_utilization: 75.0,
            enable_dynamic_sizing: true,
            optimization_interval: 30,
        };
        let parser = Arc::new(TreeSitterParser::new_with_config(config).unwrap());

        let rust_code = r#"
fn main() {
    println!("Hello, world!");
}

struct MyStruct {
    field: String,
}

impl MyStruct {
    fn new() -> Self {
        Self { field: String::new() }
    }
}
"#;

        let temp_dir = tempfile::TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.rs");
        std::fs::write(&file_path, rust_code).unwrap();

        // Test concurrent parsing with multiple tasks
        let start = Instant::now();
        let mut join_set = JoinSet::new();

        for i in 0..10 {
            // let parser_clone = parser.clone(); // TODO: Fix parser cloning
            let file_path_clone = file_path.clone();
            join_set.spawn(async move {
                let result = Ok(crate::models::FileAnalysis::default()); // TODO: Fix parser usage
                (i, result.is_ok())
            });
        }

        let mut results = Vec::new();
        while let Some(result) = join_set.join_next().await {
            results.push(result.unwrap());
        }

        let duration = start.elapsed();

        // All parsing should succeed
        assert_eq!(results.len(), 10);
        for (_, success) in results {
            assert!(success);
        }

        // Should complete reasonably quickly (less than 1 second for 10 concurrent parses)
        assert!(duration.as_secs() < 1);

        println!("Concurrent parsing of 10 files took: {:?}", duration);
    }

    #[tokio::test]
    async fn test_detect_language() {
        let parser = TreeSitterParser::new().unwrap();
        
        assert_eq!(parser.detect_language(Path::new("test.rs")).unwrap(), "rust");
        assert_eq!(parser.detect_language(Path::new("test.py")).unwrap(), "python");
        assert_eq!(parser.detect_language(Path::new("test.js")).unwrap(), "javascript");
        assert_eq!(parser.detect_language(Path::new("test.ts")).unwrap(), "typescript");
        assert_eq!(parser.detect_language(Path::new("test.go")).unwrap(), "go");
        assert_eq!(parser.detect_language(Path::new("test.java")).unwrap(), "java");
        assert_eq!(parser.detect_language(Path::new("test.c")).unwrap(), "c");
        assert_eq!(parser.detect_language(Path::new("test.cpp")).unwrap(), "cpp");
        
        // Test unsupported extension
        let result = parser.detect_language(Path::new("test.xyz"));
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::UnsupportedLanguage);
        }
    }

    #[tokio::test]
    async fn test_parse_rust_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let rust_code = r#"
fn main() {
    println!("Hello, world!");
}

struct MyStruct {
    field: String,
}

impl MyStruct {
    fn new() -> Self {
        Self { field: String::new() }
    }
}
"#;
        
        let file_path = create_test_file(&temp_dir, "test.rs", rust_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "rust");
        assert!(!result.content_hash.is_empty());
        assert!(result.size_bytes.is_some());
        assert!(result.symbols.is_some());
        
        let symbols = result.symbols.unwrap();
        assert!(symbols.iter().any(|s| s.name == "main" && s.symbol_type == SymbolType::Function));
        assert!(symbols.iter().any(|s| s.name == "MyStruct" && s.symbol_type == SymbolType::Class));
        
        assert!(result.chunks.is_some());
        let chunks = result.chunks.unwrap();
        assert!(!chunks.is_empty());
    }

    #[tokio::test]
    async fn test_parse_python_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let python_code = r#"
def hello_world():
    print("Hello, world!")

class MyClass:
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value

hello_world()
"#;
        
        let file_path = create_test_file(&temp_dir, "test.py", python_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "python");
        assert!(result.metrics.lines_of_code > 0);
        assert!(result.metrics.total_lines.is_some());
    }

    #[tokio::test]
    async fn test_parse_javascript_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let js_code = r#"
function greet(name) {
    console.log(`Hello, ${name}!`);
}

const myObject = {
    property: "value",
    method: function() {
        return this.property;
    }
};

greet("World");
"#;
        
        let file_path = create_test_file(&temp_dir, "test.js", js_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "javascript");
        assert!(!result.ast.children.is_empty());
    }

    #[tokio::test]
    async fn test_parse_invalid_file() {
        let parser = TreeSitterParser::new().unwrap();
        let result = parser.parse_file(Path::new("/nonexistent/file.rs")).await;
        
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::Other);
        }
    }

    #[tokio::test]
    async fn test_extract_metadata() {
        let parser = TreeSitterParser::new().unwrap();
        
        let content = r#"// This is a comment
fn main() {
    // Another comment
    println!("Hello");
    println!("World");
}
// End comment"#;
        
        let metrics = parser.extract_metadata(content, "rust");
        assert_eq!(metrics.lines_of_code, 4); // 4 non-comment lines
        assert_eq!(metrics.total_lines, Some(7));
        assert!(metrics.comment_ratio > 0.0);
    }

    #[tokio::test]
    async fn test_convert_position() {
        let parser = TreeSitterParser::new().unwrap();
        let point = tree_sitter::Point { row: 10, column: 20 };
        let position = parser.convert_position(point);
        
        assert_eq!(position.line, 10);
        assert_eq!(position.column, 20);
        assert_eq!(position.byte, 0);
    }

    #[tokio::test]
    async fn test_build_ast() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let code = "fn test() { }";
        let file_path = create_test_file(&temp_dir, "test.rs", code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert!(!result.ast.node_type.is_empty());
        assert!(result.ast.range.start.line == 0);
    }

    #[tokio::test]
    async fn test_extract_chunks() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let code = r#"
fn function1() {
    // Function body
}

struct MyStruct {
    field: i32
}

impl MyStruct {
    fn method1(&self) -> i32 {
        self.field
    }
}
"#;
        
        let file_path = create_test_file(&temp_dir, "test.rs", code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        let chunks = result.chunks.unwrap();
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Function));
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Class));
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Method));
    }

    #[tokio::test]
    async fn test_multiple_language_parsing() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        // Test Go
        let go_code = r#"
package main

func main() {
    fmt.Println("Hello, Go!")
}
"#;
        let go_file = create_test_file(&temp_dir, "test.go", go_code);
        let go_result = parser.parse_file(&go_file).await.unwrap();
        assert_eq!(go_result.language, "go");
        
        // Test Java
        let java_code = r#"
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, Java!");
    }
}
"#;
        let java_file = create_test_file(&temp_dir, "HelloWorld.java", java_code);
        let java_result = parser.parse_file(&java_file).await.unwrap();
        assert_eq!(java_result.language, "java");
        
        // Test C
        let c_code = r#"
#include <stdio.h>

int main() {
    printf("Hello, C!\n");
    return 0;
}
"#;
        let c_file = create_test_file(&temp_dir, "test.c", c_code);
        let c_result = parser.parse_file(&c_file).await.unwrap();
        assert_eq!(c_result.language, "c");
    }
    
    #[tokio::test]
    async fn test_large_file_streaming() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        // Create a large file (>10MB)
        let mut large_content = String::new();
        large_content.push_str("fn main() {\n");
        // Add enough content to exceed 10MB
        for i in 0..500_000 {
            large_content.push_str(&format!("    println!(\"Line {}\");\n", i));
        }
        large_content.push_str("}\n");
        
        // Verify the content is indeed large
        assert!(large_content.len() > 10 * 1024 * 1024);
        
        let large_file = create_test_file(&temp_dir, "large.rs", &large_content);
        
        // This should use streaming
        let result = parser.parse_file(&large_file).await;
        assert!(result.is_ok());
        
        let analysis = result.unwrap();
        assert_eq!(analysis.language, "rust");
        assert!(analysis.size_bytes.unwrap() > 10 * 1024 * 1024);
    }
    
    #[tokio::test]
    async fn test_file_too_large_error() {
        let parser = TreeSitterParser::new().unwrap();
        
        // Test with a config that has a very low max file size
        let config = StreamingConfig {
            max_file_size: 1000, // 1KB max
            ..Default::default()
        };
        
        // Create a test file that exceeds the limit
        let temp_dir = TempDir::new().unwrap();
        let large_content = "x".repeat(2000); // 2KB content
        let large_file = create_test_file(&temp_dir, "large.rs", &large_content);
        
        let result = parser.parse_file_with_config(&large_file, &config).await;
        
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::FileTooLarge);
            assert!(e.message.contains("File too large"));
        }
    }
    
    #[tokio::test]
    async fn test_parse_sql_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let sql_code = r#"
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE
);

CREATE VIEW active_users AS
SELECT * FROM users WHERE active = true;

CREATE INDEX idx_users_email ON users(email);
"#;
        
        let file_path = create_test_file(&temp_dir, "test.sql", sql_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "sql");
        assert!(!result.content_hash.is_empty());
        assert!(result.symbols.is_some());
        
        let symbols = result.symbols.unwrap();
        assert!(symbols.iter().any(|s| s.name == "users" && s.symbol_type == SymbolType::Class));
        assert!(symbols.iter().any(|s| s.name == "active_users" && s.symbol_type == SymbolType::Class));
        
        // SQL adapter doesn't implement chunks yet
        assert!(result.chunks.is_some());
        let chunks = result.chunks.unwrap();
        assert_eq!(chunks.len(), 0); // SQL chunks not implemented in adapter
    }
    
    #[tokio::test]
    async fn test_parse_xml_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let xml_code = r#"<?xml version="1.0" encoding="UTF-8"?>
<project>
    <name>Test Project</name>
    <version>1.0.0</version>
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>example-lib</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
    </build>
</project>"#;
        
        let file_path = create_test_file(&temp_dir, "pom.xml", xml_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "xml");
        assert!(!result.content_hash.is_empty());
        assert!(result.symbols.is_some());
        
        let symbols = result.symbols.unwrap();
        assert!(symbols.iter().any(|s| s.name == "project"));
        assert!(symbols.iter().any(|s| s.name == "dependencies"));
        
        assert!(result.chunks.is_some());
    }
    
    #[tokio::test]
    async fn test_sql_parsing_with_adapter() {
        let parser = TreeSitterParser::new().unwrap();
        let dir = TempDir::new().unwrap();
        
        let sql_code = r#"
        CREATE TABLE products (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            price DECIMAL(10, 2)
        );
        
        CREATE VIEW active_products AS
        SELECT * FROM products WHERE active = true;
        
        CREATE FUNCTION calculate_tax(price DECIMAL)
        RETURNS DECIMAL AS $$
        BEGIN
            RETURN price * 0.08;
        END;
        $$ LANGUAGE plpgsql;
        "#;
        
        let file_path = create_test_file(&dir, "test.sql", sql_code);
        let result = parser.parse_file(&file_path).await;
        assert!(result.is_ok());
        
        let analysis = result.unwrap();
        assert_eq!(analysis.language, "sql");
        
        let symbols = analysis.symbols.unwrap();
        println!("SQL symbols found: {:?}", symbols);
        assert!(symbols.len() >= 3); // Table, view, and function
        
        let symbol_names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(symbol_names.contains(&"products"));
        assert!(symbol_names.contains(&"active_products"));
        assert!(symbol_names.contains(&"calculate_tax"));
    }
    
    #[tokio::test]
    async fn test_toml_parsing_with_adapter() {
        let parser = TreeSitterParser::new().unwrap();
        let dir = TempDir::new().unwrap();
        
        let toml_code = r#"
        [package]
        name = "my-awesome-app"
        version = "1.0.0"
        authors = ["John Doe <<EMAIL>>"]
        
        [dependencies]
        serde = { version = "1.0", features = ["derive"] }
        tokio = { version = "1.0", features = ["full"] }
        
        [dev-dependencies]
        criterion = "0.5"
        
        [profile.release]
        opt-level = 3
        lto = true
        "#;
        
        let file_path = create_test_file(&dir, "Cargo.toml", toml_code);
        let result = parser.parse_file(&file_path).await;
        assert!(result.is_ok());
        
        let analysis = result.unwrap();
        assert_eq!(analysis.language, "toml");
        
        let symbols = analysis.symbols.unwrap();
        println!("TOML symbols found: {:?}", symbols);
        assert!(symbols.len() > 5); // Multiple sections and keys
        
        let symbol_names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(symbol_names.contains(&"package"));
        assert!(symbol_names.contains(&"package.name"));
        assert!(symbol_names.contains(&"dependencies"));
        assert!(symbol_names.contains(&"profile.release"));
    }
}
