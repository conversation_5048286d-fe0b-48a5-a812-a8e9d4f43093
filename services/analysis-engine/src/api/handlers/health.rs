use axum::{
    extract::State,
    http::<PERSON><PERSON><PERSON>,
    J<PERSON>,
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use crate::api::AppState;
use crate::backpressure::{BackpressureMetrics, CircuitBreakerStatus};
use crate::metrics::prometheus::{ACTIVE_ANALYSES, MEMORY_USAGE_BYTES, DB_CONNECTIONS_ACTIVE};
use std::{env, collections::HashMap};

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub service: String,
    pub version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReadyResponse {
    pub ready: bool,
    pub service: String,
    pub checks: HealthChecks,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthChecks {
    pub spanner: bool,
    pub storage: bool,
    pub pubsub: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthStatusResponse {
    pub auth_method: String,
    pub project_id: String,
    pub environment: String,
    pub credentials_configured: bool,
    pub service_availability: HashMap<String, bool>,
    pub debug_info: HashMap<String, String>,
}

// GET /health - Basic health check
pub async fn health() -> impl IntoResponse {
    Json(HealthResponse {
        status: "healthy".to_string(),
        service: "analysis-engine".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
    })
}

// GET /health/live - Kubernetes liveness probe
pub async fn liveness() -> impl IntoResponse {
    // Liveness probe just checks if the service is responsive
    // It doesn't check external dependencies
    (StatusCode::OK, Json(serde_json::json!({
        "status": "alive",
        "service": "analysis-engine",
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// GET /ready - Readiness check with dependency validation
pub async fn ready(State(state): State<AppState>) -> impl IntoResponse {
    // Check Spanner connectivity (optional service)
    let spanner_ok = if state.spanner.is_some() {
        check_spanner(&state).await
    } else {
        // Spanner is optional, so we consider it "ok" if not configured
        true
    };
    
    // Check Storage connectivity
    let storage_ok = check_storage(&state).await;
    
    // Check Pub/Sub connectivity
    let pubsub_ok = check_pubsub(&state).await;

    // For readiness, we require at least storage to be available
    // Spanner is optional (can run in memory mode)
    let all_ready = storage_ok && pubsub_ok;

    let response = ReadyResponse {
        ready: all_ready,
        service: "analysis-engine".to_string(),
        checks: HealthChecks {
            spanner: spanner_ok,
            storage: storage_ok,
            pubsub: pubsub_ok,
        },
    };

    if all_ready {
        (StatusCode::OK, Json(response))
    } else {
        (StatusCode::SERVICE_UNAVAILABLE, Json(response))
    }
}

// GET /metrics - Prometheus metrics endpoint
pub async fn metrics() -> impl IntoResponse {
    use prometheus::{Encoder, TextEncoder};
    
    let encoder = TextEncoder::new();
    let metric_families = prometheus::gather();
    let mut buffer = vec![];
    
    match encoder.encode(&metric_families, &mut buffer) {
        Ok(_) => (
            StatusCode::OK,
            [(axum::http::header::CONTENT_TYPE, "text/plain; version=0.0.4")],
            buffer,
        ),
        Err(_) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            [(axum::http::header::CONTENT_TYPE, "text/plain")],
            b"Failed to encode metrics".to_vec(),
        ),
    }
}

async fn check_spanner(state: &AppState) -> bool {
    // Simple health check - try to execute a basic query
    match &state.spanner {
        Some(spanner) => match spanner.health_check().await {
            Ok(_) => true,
            Err(e) => {
                tracing::warn!("Spanner health check failed: {}", e);
                false
            }
        },
        None => {
            tracing::warn!("Spanner not configured - running in memory mode");
            false
        }
    }
}

async fn check_storage(state: &AppState) -> bool {
    // Simple health check - try to check bucket access
    match state.storage.health_check().await {
        Ok(_) => true,
        Err(e) => {
            tracing::warn!("Storage health check failed: {}", e);
            false
        }
    }
}

async fn check_pubsub(state: &AppState) -> bool {
    // Simple health check - try to check topic access
    match state.pubsub.health_check().await {
        Ok(_) => true,
        Err(e) => {
            tracing::warn!("PubSub health check failed: {}", e);
            false
        }
    }
}

// GET /health/auth - Authentication debugging endpoint
pub async fn auth_status(State(state): State<AppState>) -> impl IntoResponse {
    let mut debug_info = HashMap::new();
    let mut service_availability = HashMap::new();
    
    // Detect authentication method
    let auth_method = if let Ok(creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
        if !creds_path.is_empty() {
            debug_info.insert("credentials_path".to_string(), creds_path.clone());
            debug_info.insert("credentials_exists".to_string(), 
                std::path::Path::new(&creds_path).exists().to_string());
            "service_account_file".to_string()
        } else {
            "default_credentials".to_string()
        }
    } else {
        "metadata_server_or_gcloud".to_string()
    };
    
    // Check environment
    let environment = env::var("ENVIRONMENT")
        .or_else(|_| env::var("ENV"))
        .unwrap_or_else(|_| "unknown".to_string());
    
    // Check emulator configuration
    if let Ok(spanner_emu) = env::var("SPANNER_EMULATOR_HOST") {
        debug_info.insert("spanner_emulator".to_string(), spanner_emu);
    }
    if let Ok(storage_emu) = env::var("STORAGE_EMULATOR_HOST") {
        debug_info.insert("storage_emulator".to_string(), storage_emu);
    }
    if let Ok(pubsub_emu) = env::var("PUBSUB_EMULATOR_HOST") {
        debug_info.insert("pubsub_emulator".to_string(), pubsub_emu);
    }
    
    // Check service availability
    service_availability.insert("spanner".to_string(), state.spanner.is_some());
    service_availability.insert("storage".to_string(), true); // Storage is always available
    service_availability.insert("pubsub".to_string(), true); // PubSub is always available
    service_availability.insert("redis".to_string(), state.redis.is_some());
    
    // Add runtime info
    debug_info.insert("service_version".to_string(), env!("CARGO_PKG_VERSION").to_string());
    
    Json(AuthStatusResponse {
        auth_method,
        project_id: state.config.gcp.project_id.clone(),
        environment,
        credentials_configured: env::var("GOOGLE_APPLICATION_CREDENTIALS").is_ok(),
        service_availability,
        debug_info,
    })
}

// GET /backpressure - Get backpressure status and metrics
pub async fn backpressure_status(State(state): State<AppState>) -> impl IntoResponse {
    let metrics = state.backpressure_manager.get_metrics().await;

    let system_status = if metrics.memory_usage_mb > 3000 || metrics.cpu_usage_percent > 80.0 {
        "under_pressure".to_string()
    } else if metrics.memory_usage_mb > 2000 || metrics.cpu_usage_percent > 60.0 {
        "moderate_load".to_string()
    } else {
        "healthy".to_string()
    };

    let circuit_breakers = state.backpressure_manager.get_circuit_breaker_status().await;

    (StatusCode::OK, Json(BackpressureStatusResponse {
        metrics,
        active_analyses: state.active_analyses.len(),
        system_status,
        circuit_breakers,
    }))
}

#[derive(Debug, Serialize)]
pub struct BackpressureStatusResponse {
    pub metrics: BackpressureMetrics,
    pub active_analyses: usize,
    pub system_status: String,
    pub circuit_breakers: CircuitBreakerStatus,
}

// GET /circuit-breakers - Get circuit breaker status for all services
pub async fn circuit_breaker_status(State(state): State<AppState>) -> impl IntoResponse {
    let circuit_breaker_status = state.backpressure_manager.get_circuit_breaker_status().await;

    (StatusCode::OK, Json(circuit_breaker_status))
}

// GET /health/detailed - Comprehensive health check with all metrics
pub async fn detailed_health(State(state): State<AppState>) -> impl IntoResponse {
    let mut details = HashMap::new();
    
    // Service information
    details.insert("service".to_string(), serde_json::json!({
        "name": "analysis-engine",
        "version": env!("CARGO_PKG_VERSION"),
        "uptime": chrono::Utc::now().to_rfc3339()
    }));
    
    // Dependencies health
    let spanner_health = if state.spanner.is_some() {
        match check_spanner(&state).await {
            true => "healthy",
            false => "unhealthy"
        }
    } else {
        "not_configured"
    };
    
    let storage_health = match check_storage(&state).await {
        true => "healthy",
        false => "unhealthy"
    };
    
    let pubsub_health = match check_pubsub(&state).await {
        true => "healthy",
        false => "unhealthy"
    };
    
    let redis_health = if state.redis.is_some() {
        "healthy"
    } else {
        "not_configured"
    };
    
    details.insert("dependencies".to_string(), serde_json::json!({
        "spanner": spanner_health,
        "storage": storage_health,
        "pubsub": pubsub_health,
        "redis": redis_health
    }));
    
    // Current metrics
    details.insert("metrics".to_string(), serde_json::json!({
        "active_analyses": ACTIVE_ANALYSES.get(),
        "memory_usage_bytes": MEMORY_USAGE_BYTES.get(),
        "db_connections_active": DB_CONNECTIONS_ACTIVE.get()
    }));
    
    // Backpressure status
    let bp_metrics = state.backpressure_manager.get_metrics().await;
    details.insert("backpressure".to_string(), serde_json::json!({
        "memory_usage_mb": bp_metrics.memory_usage_mb,
        "cpu_usage_percent": bp_metrics.cpu_usage_percent,
        "active_requests": bp_metrics.active_requests,
        "queued_requests": bp_metrics.queued_requests
    }));
    
    // Circuit breaker status
    let cb_status = state.backpressure_manager.get_circuit_breaker_status().await;
    details.insert("circuit_breakers".to_string(), serde_json::json!({
        "database": cb_status.database,
        "storage": cb_status.storage,
        "pubsub": cb_status.pubsub,
        "embeddings": cb_status.embeddings,
        "redis": cb_status.redis
    }));
    
    let overall_health = if storage_health == "healthy" && pubsub_health == "healthy" {
        "healthy"
    } else {
        "degraded"
    };
    
    (StatusCode::OK, Json(serde_json::json!({
        "status": overall_health,
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "details": details
    })))
}