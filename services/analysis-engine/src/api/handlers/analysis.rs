use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    Json,
    response::IntoResponse,
};
use uuid::Uuid;
use chrono::Utc;
use serde::Deserialize;
use crate::api::{AppState, errors::{ErrorResponse, ErrorType}};
use crate::models::*;
use crate::audit::{AuditLogger, AuditEventBuilder, AuditAction, AuditOutcome, AuditSeverity};
use crate::backpressure::{BackpressureDecision, BackpressureReason};
// use crate::contracts; // Module is empty, commenting out for now

#[derive(Debug, Deserialize)]
pub struct WarningQueryParams {
    pub min_severity: Option<WarningSeverity>,
    pub file_path: Option<String>,
    pub warning_type: Option<WarningType>,
}

/// Helper function to convert warning severity to numeric level for comparison
fn warning_severity_level(severity: &WarningSeverity) -> u8 {
    match severity {
        WarningSeverity::Low => 1,
        WarningSeverity::Medium => 2,
        WarningSeverity::High => 3,
        WarningSeverity::Critical => 4,
    }
}



// POST /api/v1/analysis - Create new analysis
pub async fn create_analysis(
    State(state): State<AppState>,
    headers: axum::http::HeaderMap,
    Json(request): Json<AnalysisRequest>,
) -> impl IntoResponse {
    // Extract user ID from headers (assuming it's set by auth middleware)
    let _user_id = headers
        .get("x-user-id")
        .and_then(|v| v.to_str().ok())
        .unwrap_or("anonymous")
        .to_string();

    // Validate request
    if let Err(e) = request.validate() {
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::validation_error(e)),
        ).into_response();
    }

    // Check backpressure before proceeding
    match state.backpressure_manager.check_analysis_request().await {
        BackpressureDecision::Allow => {
            // Proceed with analysis
        }
        BackpressureDecision::Reject(reason) => {
            let (status, message, retry_after) = match reason {
                BackpressureReason::ConcurrencyLimit => {
                    (StatusCode::TOO_MANY_REQUESTS, "Maximum concurrent analyses limit reached".to_string(), Some(60))
                }
                BackpressureReason::MemoryPressure => {
                    (StatusCode::SERVICE_UNAVAILABLE, "System under memory pressure".to_string(), Some(120))
                }
                BackpressureReason::CpuPressure => {
                    (StatusCode::SERVICE_UNAVAILABLE, "System under CPU pressure".to_string(), Some(90))
                }
                BackpressureReason::QueueOverflow => {
                    (StatusCode::TOO_MANY_REQUESTS, "Request queue overflow".to_string(), Some(180))
                }
                BackpressureReason::CircuitBreakerOpen(service) => {
                    (StatusCode::SERVICE_UNAVAILABLE, format!("Service {} temporarily unavailable", service), Some(300))
                }
                BackpressureReason::SystemOverload => {
                    (StatusCode::SERVICE_UNAVAILABLE, "System overloaded".to_string(), Some(240))
                }
            };

            return (
                status,
                Json(ErrorResponse::rate_limit_error(
                    message,
                    retry_after,
                )),
            ).into_response();
        }
        BackpressureDecision::Throttle(duration) => {
            // Apply throttling delay
            tokio::time::sleep(duration).await;
        }
    }

    // Generate analysis ID
    let analysis_id = Uuid::new_v4().to_string();

    // Audit log analysis creation
    let user_id = "anonymous".to_string(); // TODO: Extract from request context
    let audit_logger = AuditLogger::new(state.spanner.clone());
    let audit_event = AuditEventBuilder::new(AuditAction::AnalysisStarted)
        .user_id(user_id.clone())
        .resource("analysis", &analysis_id)
        .outcome(AuditOutcome::Success)
        .severity(AuditSeverity::Info)
        .metadata(serde_json::json!({
            "repository_url": request.repository_url,
            "branch": request.branch,
            "webhook_url": request.webhook_url
        }))
        .build();

    if let Err(e) = audit_logger.log_event(audit_event).await {
        tracing::error!("Failed to log audit event: {}", e);
    }

    // Create response
    let response = AnalysisResponse {
        analysis_id: analysis_id.clone(),
        status: AnalysisStatus::Pending,
        created_at: Utc::now(),
        estimated_completion: Some(Utc::now() + chrono::Duration::minutes(5)),
        repository: RepositoryInfo {
            url: request.repository_url.clone(),
            branch: request.branch.clone().unwrap_or_else(|| "main".to_string()),
            commit_sha: None,
            size_bytes: None,
        },
        webhook_url: request.webhook_url.clone(),
        progress_url: Some(format!("/ws/analysis/{}", analysis_id)),
    };

    // Start analysis in background
    let state_clone = state.clone();
    let analysis_id_clone = analysis_id.clone();
    let progress_broadcast = state.progress_broadcast.clone();
    tokio::spawn(async move {
        if let Err(e) = state_clone
            .analysis_service
            .start_analysis(analysis_id_clone, request, user_id, progress_broadcast)
            .await
        {
            tracing::error!("Failed to start analysis: {}", e);
        }
    });

    // Add to active analyses
    state
        .active_analyses
        .insert(analysis_id, AnalysisStatus::Pending);

    (StatusCode::CREATED, Json(response)).into_response()
}

// GET /api/v1/analysis - List analyses
pub async fn list_analyses(
    State(state): State<AppState>,
    Query(params): Query<ListAnalysesParams>,
) -> impl IntoResponse {
    match &state.spanner {
        Some(spanner) => match spanner.list_analyses(&params).await {
            Ok(analyses) => (
                StatusCode::OK,
                Json(serde_json::json!({
                    "analyses": analyses,
                    "total_count": analyses.len(), // This should be a separate count query for production
                    "page": params.page.unwrap_or(1),
                    "per_page": params.per_page.unwrap_or(20),
                })),
            )
                .into_response(),
            Err(e) => (
                ErrorResponse::internal_error(e.to_string())
            )
                .into_response(),
        },
        None => {
            let mut error = ErrorResponse::new(ErrorType::ExternalDependency, "Database service unavailable".to_string());
            error.error_code = Some("DATABASE_UNAVAILABLE".to_string());
            error.user_message = Some("The database service is currently unavailable. Please try again later.".to_string());
            error.retryable = true;
            error.retry_after_seconds = Some(30);
            error.into_response()
        }
    }
}

// GET /api/v1/analysis/{id} - Get analysis result (internal model)
pub async fn get_analysis(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match &state.spanner {
        Some(spanner) => match spanner.get_analysis(&id).await {
            Ok(Some(analysis)) => (StatusCode::OK, Json(analysis)).into_response(),
            Ok(None) => (
                ErrorResponse::not_found_error("Analysis".to_string())
            )
                .into_response(),
            Err(e) => (
                ErrorResponse::internal_error(e.to_string())
            )
                .into_response(),
        },
        None => {
            let mut error = ErrorResponse::new(ErrorType::ExternalDependency, "Database service unavailable".to_string());
            error.error_code = Some("DATABASE_UNAVAILABLE".to_string());
            error.user_message = Some("The database service is currently unavailable. Please try again later.".to_string());
            error.retryable = true;
            error.retry_after_seconds = Some(30);
            error.into_response()
        }
    }
}

// GET /api/v1/analysis/{id}/results - Get analysis result (public contract)
pub async fn get_analysis_results(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match &state.spanner {
        Some(spanner) => match spanner.get_analysis(&id).await {
            Ok(Some(analysis)) => {
                // let output: contracts::AnalysisOutput = analysis.into();
                // For now, return the analysis directly until contracts module is implemented
                (StatusCode::OK, Json(analysis)).into_response()
            }
            Ok(None) => (
                ErrorResponse::not_found_error("Analysis".to_string())
            )
                .into_response(),
            Err(e) => (
                ErrorResponse::internal_error(e.to_string())
            )
                .into_response(),
        },
        None => {
            let mut error = ErrorResponse::new(ErrorType::ExternalDependency, "Database service unavailable".to_string());
            error.error_code = Some("DATABASE_UNAVAILABLE".to_string());
            error.user_message = Some("The database service is currently unavailable. Please try again later.".to_string());
            error.retryable = true;
            error.retry_after_seconds = Some(30);
            error.into_response()
        }
    }
}


// GET /api/v1/analysis/{id}/status - Get analysis status
pub async fn get_analysis_status(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    if let Some(status) = state.active_analyses.get(&id) {
        return (StatusCode::OK, Json(serde_json::json!({
            "analysis_id": id,
            "status": status.value(),
            "progress": 0.0,
            "current_stage": "Initializing",
            "started_at": Utc::now(),
            "estimated_completion": Utc::now() + chrono::Duration::minutes(5),
        }))).into_response()
    }

    match &state.spanner {
        Some(spanner) => match spanner.get_analysis(&id).await {
            Ok(Some(analysis)) => (
                StatusCode::OK,
                Json(serde_json::json!({
                    "analysis_id": analysis.id,
                    "status": analysis.status,
                    "progress": analysis.progress.unwrap_or(0.0),
                    "current_stage": analysis.current_stage,
                    "started_at": analysis.started_at,
                    "estimated_completion": analysis.estimated_completion,
                    "error_message": analysis.error_message,
                })),
            )
                .into_response(),
            Ok(None) => (
                ErrorResponse::not_found_error("Analysis".to_string())
            )
                .into_response(),
            Err(e) => (
                ErrorResponse::internal_error(e.to_string())
            )
                .into_response(),
        },
        None => {
            // In-memory fallback - just return not found if not in active analyses
            (
                ErrorResponse::not_found_error("Analysis".to_string())
            )
                .into_response()
        }
    }
}

// DELETE /api/v1/analysis/{id} - Cancel analysis
pub async fn cancel_analysis(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    if state.active_analyses.remove(&id).is_some() {
        // Also update the status in Spanner
        if let Some(spanner) = &state.spanner {
            if let Ok(Some(mut analysis)) = spanner.get_analysis(&id).await {
                analysis.status = AnalysisStatus::Cancelled;
                analysis.completed_at = Some(Utc::now());
                if let Err(e) = spanner.store_analysis(&analysis).await {
                    tracing::error!("Failed to update analysis status to cancelled: {}", e);
                }
            }
        }
        StatusCode::NO_CONTENT
    } else {
        StatusCode::NOT_FOUND
    }
}

// GET /api/v1/analysis/{id}/download - Download results
pub async fn download_analysis(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match state.storage.get_analysis_results(&id).await {
        Ok(analysis) => (StatusCode::OK, Json(analysis)).into_response(),
        Err(e) => (
            ErrorResponse::internal_error(e.to_string())
        )
            .into_response(),
    }
}

// GET /api/v1/analysis/{id}/warnings - Get analysis warnings with filtering
pub async fn get_analysis_warnings(
    State(state): State<AppState>,
    Path(id): Path<String>,
    Query(params): Query<WarningQueryParams>,
) -> impl IntoResponse {
    match &state.spanner {
        Some(spanner) => match spanner.get_analysis(&id).await {
            Ok(Some(analysis)) => {
                let mut warnings = analysis.warnings;

                // Filter by severity if specified
                if let Some(min_severity) = params.min_severity {
                    warnings.retain(|w| warning_severity_level(&w.severity) >= warning_severity_level(&min_severity));
                }

                // Filter by file path if specified
                if let Some(file_path) = params.file_path {
                    warnings.retain(|w| w.file_path.as_ref().map_or(false, |p| p.contains(&file_path)));
                }

                // Filter by warning type if specified
                if let Some(warning_type) = params.warning_type {
                    warnings.retain(|w| w.warning_type == warning_type);
                }

                // Aggregate warnings by type for summary
                let mut warning_summary = std::collections::HashMap::new();
                for warning in &warnings {
                    let count = warning_summary.entry(warning.warning_type.clone()).or_insert(0);
                    *count += 1;
                }

                (StatusCode::OK, Json(serde_json::json!({
                    "analysis_id": analysis.id,
                    "total_warnings": warnings.len(),
                    "warnings": warnings,
                    "summary": warning_summary,
                    "repository_url": analysis.repository_url,
                    "branch": analysis.branch,
                    "commit_hash": analysis.commit_hash,
                }))).into_response()
            }
            Ok(None) => (
                ErrorResponse::not_found_error("Analysis".to_string())
            )
                .into_response(),
            Err(e) => (
                ErrorResponse::internal_error(e.to_string())
            )
                .into_response(),
        },
        None => {
            // In-memory mode - warnings not available
            (StatusCode::SERVICE_UNAVAILABLE, Json(serde_json::json!({
                "error": "Warning data not available in memory mode"
            }))).into_response()
        }
    }
}

// GET /api/v1/analysis/{id}/metrics - Get metrics only
pub async fn get_analysis_metrics(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match &state.spanner {
        Some(spanner) => match spanner.get_analysis_metrics(&id).await {
            Ok(Some(metrics)) => (StatusCode::OK, Json(metrics)).into_response(),
            Ok(None) => (
                ErrorResponse::not_found_error("Analysis".to_string())
            )
                .into_response(),
            Err(e) => (
                ErrorResponse::internal_error(e.to_string())
            )
                .into_response(),
        },
        None => {
            let mut error = ErrorResponse::new(ErrorType::ExternalDependency, "Database service unavailable".to_string());
            error.error_code = Some("DATABASE_UNAVAILABLE".to_string());
            error.user_message = Some("The database service is currently unavailable. Please try again later.".to_string());
            error.retryable = true;
            error.retry_after_seconds = Some(30);
            error.into_response()
        }
    }
}

// GET /api/v1/analysis/{id}/patterns - Get patterns only
pub async fn get_analysis_patterns(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match &state.spanner {
        Some(spanner) => match spanner.get_analysis_patterns(&id).await {
            Ok(Some(patterns)) => (StatusCode::OK, Json(patterns)).into_response(),
            Ok(None) => (
                ErrorResponse::not_found_error("Analysis".to_string())
            )
                .into_response(),
            Err(e) => (
                ErrorResponse::internal_error(e.to_string())
            )
                .into_response(),
        },
        None => {
            let mut error = ErrorResponse::new(ErrorType::ExternalDependency, "Database service unavailable".to_string());
            error.error_code = Some("DATABASE_UNAVAILABLE".to_string());
            error.user_message = Some("The database service is currently unavailable. Please try again later.".to_string());
            error.retryable = true;
            error.retry_after_seconds = Some(30);
            error.into_response()
        }
    }
}

// GET /api/v1/languages - Get supported languages
pub async fn supported_languages() -> impl IntoResponse {
    // Languages with tree-sitter support
    let tree_sitter_languages = vec![
        "rust", "javascript", "typescript", "python", "go", 
        "java", "c", "cpp", "html", "css", "json", "yaml", 
        "ruby", "bash", "markdown"
    ];
    
    // Languages with custom adapter support
    let adapter_languages = vec![
        "sql", "xml", "toml"
    ];
    
    // Languages detected by tokei but not yet parsed (future expansion)
    let future_languages = vec![
        "php", "swift", "kotlin", "objc", "r", "julia",
        "haskell", "scala", "clojure", "erlang", "elixir"
    ];
    
    // Combine all currently supported languages
    let mut all_languages = tree_sitter_languages.clone();
    all_languages.extend(adapter_languages.clone());
    all_languages.sort();
    
    // Language categories for better organization
    let categories = serde_json::json!({
        "systems": ["rust", "c", "cpp", "go"],
        "web": ["javascript", "typescript", "html", "css", "json", "yaml"],
        "general": ["python", "java", "ruby"],
        "data": ["sql", "xml", "toml"],
        "scripting": ["bash", "python"],
        "documentation": ["markdown"]
    });
    
    // Parser information
    let parser_info = serde_json::json!({
        "tree_sitter": {
            "count": tree_sitter_languages.len(),
            "languages": tree_sitter_languages,
            "description": "Full AST parsing with symbol extraction"
        },
        "adapters": {
            "count": adapter_languages.len(),
            "languages": adapter_languages,
            "description": "Custom parsers for specialized formats"
        },
        "detection_only": {
            "count": future_languages.len(),
            "languages": future_languages,
            "description": "Language detection available, parsing in development"
        }
    });
    
    Json(serde_json::json!({
        "languages": all_languages,
        "total": all_languages.len(),
        "version": "2.0.0",
        "categories": categories,
        "parser_info": parser_info,
        "capabilities": {
            "ast_parsing": tree_sitter_languages.len() + adapter_languages.len(),
            "symbol_extraction": tree_sitter_languages.len() + adapter_languages.len(),
            "metrics_calculation": all_languages.len(),
            "language_detection": all_languages.len() + future_languages.len()
        },
        "note": "All languages support metrics calculation and language detection. AST parsing and symbol extraction available for listed languages."
    }))
}