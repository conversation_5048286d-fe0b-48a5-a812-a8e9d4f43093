pub mod prometheus;

use crate::models::{RepositoryMetrics, FileAnalysis, FileMetrics};
use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};

pub struct MetricsService;

/// Comprehensive performance monitoring system
pub struct PerformanceMonitor {
    /// Real-time metrics collection
    metrics_collector: Arc<RwLock<MetricsCollector>>,
    /// System resource monitor
    system_monitor: Arc<SystemResourceMonitor>,
    /// Bottleneck detector
    bottleneck_detector: Arc<BottleneckDetector>,
    /// Performance alerting system
    alerting_system: Arc<AlertingSystem>,
    /// Configuration
    config: PerformanceMonitorConfig,
}

/// Configuration for performance monitoring
#[derive(Debug, Clone)]
pub struct PerformanceMonitorConfig {
    /// Collection interval in seconds
    pub collection_interval: u64,
    /// Memory usage threshold for alerts (percentage)
    pub memory_alert_threshold: f64,
    /// CPU usage threshold for alerts (percentage)
    pub cpu_alert_threshold: f64,
    /// Response time threshold for alerts (milliseconds)
    pub response_time_alert_threshold: u64,
    /// Error rate threshold for alerts (percentage)
    pub error_rate_alert_threshold: f64,
    /// Enable detailed bottleneck analysis
    pub enable_bottleneck_analysis: bool,
    /// Maximum metrics history to keep (entries)
    pub max_metrics_history: usize,
}

impl Default for PerformanceMonitorConfig {
    fn default() -> Self {
        Self {
            collection_interval: 30,
            memory_alert_threshold: 80.0,
            cpu_alert_threshold: 85.0,
            response_time_alert_threshold: 30000, // 30 seconds
            error_rate_alert_threshold: 5.0,
            enable_bottleneck_analysis: true,
            max_metrics_history: 1000,
        }
    }
}

/// Real-time metrics collection
pub struct MetricsCollector {
    /// Current metrics snapshot
    current_metrics: PerformanceMetrics,
    /// Historical metrics for trend analysis
    metrics_history: Vec<PerformanceMetrics>,
    /// Analysis operation timings
    operation_timings: HashMap<String, Vec<Duration>>,
    /// Error counts by type
    error_counts: HashMap<String, u64>,
    /// Active analysis count
    active_analyses: u64,
}

/// Performance metrics snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Memory usage metrics
    pub memory_usage_mb: u64,
    pub memory_peak_mb: u64,
    pub memory_available_mb: u64,
    /// CPU usage metrics
    pub cpu_usage_percent: f64,
    pub cpu_load_1m: f64,
    pub cpu_load_5m: f64,
    /// Analysis performance metrics
    pub active_analyses: u64,
    pub completed_analyses: u64,
    pub failed_analyses: u64,
    pub avg_analysis_time_ms: f64,
    pub p95_analysis_time_ms: f64,
    pub p99_analysis_time_ms: f64,
    /// Parser performance metrics
    pub parser_pool_utilization: f64,
    pub avg_parse_time_ms: f64,
    pub parse_success_rate: f64,
    /// Cache performance metrics
    pub cache_hit_rate: f64,
    pub cache_size_mb: u64,
    pub cache_evictions: u64,
    /// Database performance metrics
    pub database_connections: u64,
    pub avg_db_query_time_ms: f64,
    pub db_connection_pool_utilization: f64,
    /// System throughput metrics
    pub requests_per_second: f64,
    pub files_processed_per_second: f64,
    /// Error rates
    pub error_rate_percent: f64,
    pub critical_errors: u64,
    pub warnings: u64,
    /// Bottleneck indicators
    pub bottleneck_score: f64,
    pub primary_bottleneck: Option<String>,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            timestamp: chrono::Utc::now(),
            memory_usage_mb: 0,
            memory_peak_mb: 0,
            memory_available_mb: 0,
            cpu_usage_percent: 0.0,
            cpu_load_1m: 0.0,
            cpu_load_5m: 0.0,
            active_analyses: 0,
            completed_analyses: 0,
            failed_analyses: 0,
            avg_analysis_time_ms: 0.0,
            p95_analysis_time_ms: 0.0,
            p99_analysis_time_ms: 0.0,
            parser_pool_utilization: 0.0,
            avg_parse_time_ms: 0.0,
            parse_success_rate: 0.0,
            cache_hit_rate: 0.0,
            cache_size_mb: 0,
            cache_evictions: 0,
            database_connections: 0,
            avg_db_query_time_ms: 0.0,
            db_connection_pool_utilization: 0.0,
            requests_per_second: 0.0,
            files_processed_per_second: 0.0,
            error_rate_percent: 0.0,
            critical_errors: 0,
            warnings: 0,
            bottleneck_score: 0.0,
            primary_bottleneck: None,
        }
    }
}

/// System resource monitoring
pub struct SystemResourceMonitor {
    /// System metrics update interval
    update_interval: Duration,
    /// Current system stats
    current_stats: Arc<RwLock<SystemStats>>,
}

/// System statistics
#[derive(Debug, Clone, Default)]
pub struct SystemStats {
    pub memory_total: u64,
    pub memory_used: u64,
    pub memory_available: u64,
    pub cpu_usage: f64,
    pub load_average: (f64, f64, f64),
    pub disk_usage: u64,
    pub disk_available: u64,
    pub network_bytes_sent: u64,
    pub network_bytes_received: u64,
    pub open_file_descriptors: u64,
    pub threads_count: u64,
}

/// Bottleneck detection system
pub struct BottleneckDetector {
    /// Bottleneck analysis configuration
    config: BottleneckDetectorConfig,
    /// Detected bottlenecks
    detected_bottlenecks: Arc<RwLock<Vec<DetectedBottleneck>>>,
}

/// Bottleneck detector configuration
#[derive(Debug, Clone)]
pub struct BottleneckDetectorConfig {
    /// Memory usage threshold for bottleneck detection
    pub memory_threshold: f64,
    /// CPU usage threshold for bottleneck detection
    pub cpu_threshold: f64,
    /// Response time threshold for bottleneck detection
    pub response_time_threshold: u64,
    /// Queue size threshold for bottleneck detection
    pub queue_size_threshold: usize,
    /// Analysis window size for bottleneck detection
    pub analysis_window_size: usize,
}

impl Default for BottleneckDetectorConfig {
    fn default() -> Self {
        Self {
            memory_threshold: 80.0,
            cpu_threshold: 85.0,
            response_time_threshold: 30000,
            queue_size_threshold: 100,
            analysis_window_size: 10,
        }
    }
}

/// Detected bottleneck
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectedBottleneck {
    /// Bottleneck type
    pub bottleneck_type: BottleneckType,
    /// Severity level
    pub severity: BottleneckSeverity,
    /// Description
    pub description: String,
    /// Metric value that triggered the detection
    pub metric_value: f64,
    /// Threshold that was exceeded
    pub threshold: f64,
    /// Detection timestamp
    pub detected_at: chrono::DateTime<chrono::Utc>,
    /// Suggested remediation
    pub remediation: Option<String>,
}

/// Types of bottlenecks
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BottleneckType {
    Memory,
    CPU,
    Parser,
    Database,
    Cache,
    Network,
    Disk,
    Queue,
}

/// Bottleneck severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BottleneckSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Performance alerting system
pub struct AlertingSystem {
    /// Alert configuration
    config: AlertingConfig,
    /// Active alerts
    active_alerts: Arc<RwLock<Vec<PerformanceAlert>>>,
    /// Alert history
    alert_history: Arc<RwLock<Vec<PerformanceAlert>>>,
}

/// Alerting configuration
#[derive(Debug, Clone)]
pub struct AlertingConfig {
    /// Enable alerting
    pub enabled: bool,
    /// Alert thresholds
    pub thresholds: AlertThresholds,
    /// Alert cooldown period in seconds
    pub cooldown_period: u64,
    /// Maximum alerts to keep in history
    pub max_alert_history: usize,
}

/// Alert thresholds
#[derive(Debug, Clone)]
pub struct AlertThresholds {
    pub memory_critical: f64,
    pub memory_warning: f64,
    pub cpu_critical: f64,
    pub cpu_warning: f64,
    pub response_time_critical: u64,
    pub response_time_warning: u64,
    pub error_rate_critical: f64,
    pub error_rate_warning: f64,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            memory_critical: 90.0,
            memory_warning: 80.0,
            cpu_critical: 95.0,
            cpu_warning: 85.0,
            response_time_critical: 60000, // 60 seconds
            response_time_warning: 30000,  // 30 seconds
            error_rate_critical: 10.0,
            error_rate_warning: 5.0,
        }
    }
}

impl Default for AlertingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            thresholds: AlertThresholds::default(),
            cooldown_period: 300, // 5 minutes
            max_alert_history: 100,
        }
    }
}

/// Performance alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAlert {
    /// Alert ID
    pub id: String,
    /// Alert type
    pub alert_type: AlertType,
    /// Alert severity
    pub severity: AlertSeverity,
    /// Alert message
    pub message: String,
    /// Metric value that triggered the alert
    pub metric_value: f64,
    /// Threshold that was exceeded
    pub threshold: f64,
    /// Alert timestamp
    pub triggered_at: chrono::DateTime<chrono::Utc>,
    /// Alert resolved timestamp
    pub resolved_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Whether alert is active
    pub active: bool,
}

/// Alert types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertType {
    MemoryUsage,
    CpuUsage,
    ResponseTime,
    ErrorRate,
    SystemOverload,
    DatabaseConnections,
    CachePerformance,
    ParserPoolExhaustion,
}

/// Alert severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
}

impl MetricsCollector {
    pub fn new() -> Self {
        Self {
            current_metrics: PerformanceMetrics::default(),
            metrics_history: Vec::new(),
            operation_timings: HashMap::new(),
            error_counts: HashMap::new(),
            active_analyses: 0,
        }
    }
    
    /// Update system metrics from system monitor
    pub fn update_system_metrics(&mut self, system_stats: &SystemStats) {
        self.current_metrics.timestamp = chrono::Utc::now();
        self.current_metrics.memory_usage_mb = system_stats.memory_used / 1024 / 1024;
        self.current_metrics.memory_available_mb = system_stats.memory_available / 1024 / 1024;
        self.current_metrics.cpu_usage_percent = system_stats.cpu_usage;
        self.current_metrics.cpu_load_1m = system_stats.load_average.0;
        self.current_metrics.cpu_load_5m = system_stats.load_average.1;
        self.current_metrics.active_analyses = self.active_analyses;
    }
    
    /// Get current performance metrics
    pub fn get_current_metrics(&self) -> PerformanceMetrics {
        self.current_metrics.clone()
    }
    
    /// Get performance metrics history
    pub fn get_metrics_history(&self) -> Vec<PerformanceMetrics> {
        self.metrics_history.clone()
    }
    
    /// Record operation timing
    pub fn record_operation_timing(&mut self, operation: &str, duration: Duration) {
        self.operation_timings.entry(operation.to_string())
            .or_insert_with(Vec::new)
            .push(duration);
    }
    
    /// Record error occurrence
    pub fn record_error(&mut self, error_type: &str) {
        *self.error_counts.entry(error_type.to_string()).or_insert(0) += 1;
    }
    
    /// Update active analysis count
    pub fn update_active_analyses(&mut self, count: u64) {
        self.active_analyses = count;
    }
    
    /// Cleanup old metrics data
    pub fn cleanup_old_data(&mut self, max_history: usize) {
        if self.metrics_history.len() > max_history {
            self.metrics_history.drain(0..self.metrics_history.len() - max_history);
        }
    }
}

impl SystemResourceMonitor {
    pub fn new() -> Self {
        let monitor = Self {
            update_interval: Duration::from_secs(30),
            current_stats: Arc::new(RwLock::new(SystemStats::default())),
        };
        
        // Start background monitoring
        monitor.start_monitoring();
        
        monitor
    }
    
    /// Start background system monitoring
    fn start_monitoring(&self) {
        let current_stats = self.current_stats.clone();
        let update_interval = self.update_interval;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(update_interval);
            
            loop {
                interval.tick().await;
                
                // Collect current system statistics
                let stats = Self::collect_current_stats().await;
                
                // Update the stored stats
                {
                    let mut current = current_stats.write().await;
                    *current = stats;
                }
            }
        });
    }
    
    /// Collect current system statistics
    async fn collect_current_stats() -> SystemStats {
        let mut stats = SystemStats::default();
        
        // Collect memory information
        if let Ok(memory_info) = Self::get_memory_info() {
            stats.memory_total = memory_info.total;
            stats.memory_used = memory_info.used;
            stats.memory_available = memory_info.available;
        }
        
        // Collect CPU information
        if let Ok(cpu_usage) = Self::get_cpu_usage() {
            stats.cpu_usage = cpu_usage;
        }
        
        // Collect load average
        if let Ok(load_avg) = Self::get_load_average() {
            stats.load_average = load_avg;
        }
        
        // Collect disk information
        if let Ok(disk_info) = Self::get_disk_info() {
            stats.disk_usage = disk_info.used;
            stats.disk_available = disk_info.available;
        }
        
        // Collect process information
        if let Ok(process_info) = Self::get_process_info() {
            stats.open_file_descriptors = process_info.open_fds;
            stats.threads_count = process_info.threads;
        }
        
        stats
    }
    
    /// Get memory information
    fn get_memory_info() -> Result<MemoryInfo, Box<dyn std::error::Error>> {
        #[cfg(target_os = "linux")]
        {
            let contents = std::fs::read_to_string("/proc/meminfo")?;
            let mut total = 0;
            let mut available = 0;
            
            for line in contents.lines() {
                if line.starts_with("MemTotal:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        total = kb_str.parse::<u64>()? * 1024;
                    }
                } else if line.starts_with("MemAvailable:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        available = kb_str.parse::<u64>()? * 1024;
                    }
                }
            }
            
            let used = total - available;
            return Ok(MemoryInfo { total, used, available });
        }
        
        #[cfg(target_os = "macos")]
        {
            // On macOS, we can use sysctl or estimate based on activity monitor
            // For now, provide reasonable defaults
            let total = 8 * 1024 * 1024 * 1024; // 8GB default
            let available = total / 2; // Estimate 50% available
            let used = total - available;
            return Ok(MemoryInfo { total, used, available });
        }
        
        // Default fallback
        Ok(MemoryInfo {
            total: 4 * 1024 * 1024 * 1024, // 4GB default
            used: 2 * 1024 * 1024 * 1024,  // 2GB used
            available: 2 * 1024 * 1024 * 1024, // 2GB available
        })
    }
    
    /// Get CPU usage percentage
    fn get_cpu_usage() -> Result<f64, Box<dyn std::error::Error>> {
        #[cfg(target_os = "linux")]
        {
            // Read /proc/stat for CPU usage
            let contents = std::fs::read_to_string("/proc/stat")?;
            if let Some(cpu_line) = contents.lines().next() {
                let values: Vec<&str> = cpu_line.split_whitespace().collect();
                if values.len() >= 8 {
                    let user: u64 = values[1].parse()?;
                    let nice: u64 = values[2].parse()?;
                    let system: u64 = values[3].parse()?;
                    let idle: u64 = values[4].parse()?;
                    let iowait: u64 = values[5].parse()?;
                    let irq: u64 = values[6].parse()?;
                    let softirq: u64 = values[7].parse()?;
                    
                    let total = user + nice + system + idle + iowait + irq + softirq;
                    let usage = if total > 0 {
                        ((total - idle) as f64 / total as f64) * 100.0
                    } else {
                        0.0
                    };
                    
                    return Ok(usage);
                }
            }
        }
        
        // Default fallback
        Ok(25.0) // 25% CPU usage
    }
    
    /// Get load average
    fn get_load_average() -> Result<(f64, f64, f64), Box<dyn std::error::Error>> {
        #[cfg(target_os = "linux")]
        {
            let contents = std::fs::read_to_string("/proc/loadavg")?;
            let parts: Vec<&str> = contents.split_whitespace().collect();
            if parts.len() >= 3 {
                let load_1m = parts[0].parse()?;
                let load_5m = parts[1].parse()?;
                let load_15m = parts[2].parse()?;
                return Ok((load_1m, load_5m, load_15m));
            }
        }
        
        // Default fallback
        Ok((1.0, 1.0, 1.0))
    }
    
    /// Get disk information
    fn get_disk_info() -> Result<DiskInfo, Box<dyn std::error::Error>> {
        // For now, return reasonable defaults
        // In a full implementation, this would query filesystem statistics
        Ok(DiskInfo {
            used: 50 * 1024 * 1024 * 1024,     // 50GB used
            available: 100 * 1024 * 1024 * 1024, // 100GB available
        })
    }
    
    /// Get process information
    fn get_process_info() -> Result<ProcessInfo, Box<dyn std::error::Error>> {
        #[cfg(target_os = "linux")]
        {
            // Get current process info
            let pid = std::process::id();
            let status_path = format!("/proc/{}/status", pid);
            
            if let Ok(contents) = std::fs::read_to_string(status_path) {
                let mut threads = 0;
                
                for line in contents.lines() {
                    if line.starts_with("Threads:") {
                        if let Some(count_str) = line.split_whitespace().nth(1) {
                            threads = count_str.parse().unwrap_or(0);
                        }
                    }
                }
                
                // For open file descriptors, we would need to check /proc/PID/fd/
                // For now, use a reasonable estimate
                let open_fds = 100; // Estimate
                
                return Ok(ProcessInfo {
                    open_fds,
                    threads,
                });
            }
        }
        
        // Default fallback
        Ok(ProcessInfo {
            open_fds: 50,
            threads: 10,
        })
    }
    
    pub async fn collect_system_stats(&self) -> SystemStats {
        let stats = self.current_stats.read().await;
        stats.clone()
    }
    
    /// Get current memory usage as a percentage
    pub async fn get_memory_usage_percent(&self) -> f64 {
        let stats = self.current_stats.read().await;
        if stats.memory_total > 0 {
            (stats.memory_used as f64 / stats.memory_total as f64) * 100.0
        } else {
            0.0
        }
    }
    
    /// Get current CPU usage
    pub async fn get_cpu_usage_percent(&self) -> f64 {
        let stats = self.current_stats.read().await;
        stats.cpu_usage
    }
}

/// Memory information structure
#[derive(Debug, Clone)]
struct MemoryInfo {
    total: u64,
    used: u64,
    available: u64,
}

/// Disk information structure
#[derive(Debug, Clone)]
struct DiskInfo {
    used: u64,
    available: u64,
}

/// Process information structure
#[derive(Debug, Clone)]
struct ProcessInfo {
    open_fds: u64,
    threads: u64,
}

impl BottleneckDetector {
    pub fn new(config: BottleneckDetectorConfig) -> Self {
        Self {
            config,
            detected_bottlenecks: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    pub async fn analyze_metrics(&self, _metrics: &PerformanceMetrics) {
        // Analyze bottlenecks based on metrics
        // This is a placeholder implementation
    }
}

impl AlertingSystem {
    pub fn new(config: AlertingConfig) -> Self {
        Self {
            config,
            active_alerts: Arc::new(RwLock::new(Vec::new())),
            alert_history: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    pub async fn check_alerts(&self, _metrics: &PerformanceMetrics) {
        // Check for alert conditions
        // This is a placeholder implementation
    }
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new(config: PerformanceMonitorConfig) -> Self {
        let metrics_collector = Arc::new(RwLock::new(MetricsCollector::new()));
        let system_monitor = Arc::new(SystemResourceMonitor::new());
        let bottleneck_detector = Arc::new(BottleneckDetector::new(BottleneckDetectorConfig::default()));
        let alerting_system = Arc::new(AlertingSystem::new(AlertingConfig::default()));

        let monitor = Self {
            metrics_collector,
            system_monitor,
            bottleneck_detector,
            alerting_system,
            config,
        };

        // Start background monitoring tasks
        monitor.start_monitoring();

        monitor
    }

    /// Start background monitoring tasks
    fn start_monitoring(&self) {
        let metrics_collector = self.metrics_collector.clone();
        let system_monitor = self.system_monitor.clone();
        let bottleneck_detector = self.bottleneck_detector.clone();
        let alerting_system = self.alerting_system.clone();
        let config = self.config.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(config.collection_interval));

            loop {
                interval.tick().await;

                // Collect system metrics
                let system_stats = system_monitor.collect_system_stats().await;
                
                // Update metrics collector
                let mut collector = metrics_collector.write().await;
                collector.update_system_metrics(&system_stats);

                // Analyze bottlenecks
                if config.enable_bottleneck_analysis {
                    let metrics = collector.get_current_metrics();
                    bottleneck_detector.analyze_metrics(&metrics).await;
                }

                // Check for alerts
                let metrics = collector.get_current_metrics();
                alerting_system.check_alerts(&metrics).await;

                // Cleanup old data
                collector.cleanup_old_data(config.max_metrics_history);
            }
        });
    }

    /// Get current performance metrics
    pub async fn get_current_metrics(&self) -> PerformanceMetrics {
        let collector = self.metrics_collector.read().await;
        collector.get_current_metrics()
    }

    /// Get performance metrics history
    pub async fn get_metrics_history(&self) -> Vec<PerformanceMetrics> {
        let collector = self.metrics_collector.read().await;
        collector.get_metrics_history()
    }

    /// Get detected bottlenecks
    pub async fn get_detected_bottlenecks(&self) -> Vec<DetectedBottleneck> {
        let detector = self.bottleneck_detector.detected_bottlenecks.read().await;
        detector.clone()
    }

    /// Get active alerts
    pub async fn get_active_alerts(&self) -> Vec<PerformanceAlert> {
        let alerting = self.alerting_system.active_alerts.read().await;
        alerting.clone()
    }

    /// Record analysis operation timing
    pub async fn record_analysis_timing(&self, operation: &str, duration: Duration) {
        let mut collector = self.metrics_collector.write().await;
        collector.record_operation_timing(operation, duration);
    }

    /// Record error
    pub async fn record_error(&self, error_type: &str) {
        let mut collector = self.metrics_collector.write().await;
        collector.record_error(error_type);
    }

    /// Update active analysis count
    pub async fn update_active_analyses(&self, count: u64) {
        let mut collector = self.metrics_collector.write().await;
        collector.update_active_analyses(count);
    }
}

impl MetricsService {
    pub fn new() -> Self {
        Self
    }

    pub fn calculate_repository_metrics(&self, analyses: &[FileAnalysis]) -> RepositoryMetrics {
        let total_files = analyses.len() as u32;
        let mut total_lines = 0u32;
        let mut total_complexity = 0u32;
        let mut maintainability_sum = 0.0;
        let mut tech_debt_minutes = 0u32;
        
        for analysis in analyses {
            if let Some(total) = analysis.metrics.total_lines {
                total_lines += total;
            }
            total_complexity += analysis.metrics.complexity;
            maintainability_sum += analysis.metrics.maintainability_index;
            
            // Estimate technical debt based on complexity and maintainability
            let file_debt = self.estimate_technical_debt(&analysis.metrics);
            tech_debt_minutes += file_debt;
        }
        
        let average_complexity = if total_files > 0 {
            Some(total_complexity as f64 / total_files as f64)
        } else {
            None
        };
        
        let maintainability_score = if total_files > 0 {
            Some(maintainability_sum / total_files as f64)
        } else {
            None
        };
        
        RepositoryMetrics {
            total_files,
            total_lines,
            total_complexity,
            average_complexity,
            maintainability_score,
            technical_debt_minutes: Some(tech_debt_minutes),
            test_coverage_estimate: self.estimate_test_coverage(analyses),
        }
    }
    
    fn estimate_technical_debt(&self, metrics: &FileMetrics) -> u32 {
        // Simple estimation based on complexity and maintainability
        let base_debt = if metrics.complexity > 10 { 30 } else { 0 };
        let maintainability_debt = if metrics.maintainability_index < 50.0 { 60 } else { 0 };
        base_debt + maintainability_debt
    }
    
    fn estimate_test_coverage(&self, analyses: &[FileAnalysis]) -> Option<f64> {
        // Simple heuristic: check for test files
        let test_files = analyses.iter()
            .filter(|a| a.path.contains("test") || a.path.contains("spec"))
            .count();
            
        if test_files > 0 {
            let ratio = test_files as f64 / analyses.len() as f64;
            Some((ratio * 100.0).min(90.0)) // Cap at 90% as an estimate
        } else {
            Some(0.0)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_repository_metrics_calculation() {
        let service = MetricsService::new();
        let analyses = vec![]; // Empty for now
        let metrics = service.calculate_repository_metrics(&analyses);
        
        assert_eq!(metrics.total_files, 0);
        assert_eq!(metrics.total_lines, 0);
        assert_eq!(metrics.total_complexity, 0);
    }
}