// Production-ready models with comprehensive features

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

// Enhanced Language Statistics with production metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductionLanguageStats {
    // Basic metrics
    pub file_count: usize,
    pub total_lines: usize,
    pub lines_of_code: usize,
    pub comment_lines: usize,
    pub blank_lines: usize,
    pub percentage_of_codebase: f64,
    
    // Advanced metrics
    pub average_file_size: f64,
    pub average_complexity: f64,
    pub average_function_length: f64,
    pub test_file_count: usize,
    pub test_coverage_estimate: f64,
    
    // Language-specific metrics
    pub language_version: Option<String>,
    pub framework_usage: Vec<FrameworkInfo>,
    pub dependency_count: usize,
    pub outdated_dependencies: Vec<DependencyInfo>,
    
    // Code quality metrics
    pub linting_issues: usize,
    pub code_smells: Vec<CodeSmell>,
    pub security_vulnerabilities: Vec<SecurityVulnerability>,
    
    // File information
    pub files: Vec<FileInfo>,
    pub largest_files: Vec<LargeFileInfo>,
    pub most_complex_files: Vec<ComplexFileInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    pub path: String,
    pub size_bytes: u64,
    pub lines: usize,
    pub complexity: f64,
    pub last_modified: DateTime<Utc>,
    pub contributors: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LargeFileInfo {
    pub path: String,
    pub size_bytes: u64,
    pub lines: usize,
    pub reason: String,
    pub refactoring_suggestions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplexFileInfo {
    pub path: String,
    pub complexity: f64,
    pub functions: Vec<ComplexFunction>,
    pub refactoring_priority: RefactoringPriority,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplexFunction {
    pub name: String,
    pub complexity: f64,
    pub lines: usize,
    pub parameters: usize,
    pub nesting_depth: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RefactoringPriority {
    Critical,
    High,
    Medium,
    Low,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FrameworkInfo {
    pub name: String,
    pub version: String,
    pub usage_count: usize,
    pub patterns_detected: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyInfo {
    pub name: String,
    pub current_version: String,
    pub latest_version: String,
    pub vulnerability_count: usize,
    pub update_priority: UpdatePriority,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpdatePriority {
    SecurityCritical,
    SecurityHigh,
    Feature,
    Minor,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeSmell {
    pub smell_type: CodeSmellType,
    pub severity: Severity,
    pub file_path: String,
    pub line_number: usize,
    pub description: String,
    pub suggested_fix: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CodeSmellType {
    LongMethod,
    LargeClass,
    DuplicateCode,
    DeadCode,
    GodClass,
    FeatureEnvy,
    DataClump,
    PrimitiveObsession,
    SwitchStatements,
    ParallelInheritanceHierarchies,
    LazyClass,
    SpeculativeGenerality,
    TemporaryField,
    MessageChains,
    MiddleMan,
    InappropriateIntimacy,
    AlternativeClassesWithDifferentInterfaces,
    IncompleteLibraryClass,
    DataClass,
    RefusedBequest,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityVulnerability {
    pub vulnerability_type: VulnerabilityType,
    pub severity: Severity,
    pub cwe_id: Option<String>,
    pub owasp_category: Option<String>,
    pub file_path: String,
    pub line_number: usize,
    pub description: String,
    pub remediation: String,
    pub false_positive_probability: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum VulnerabilityType {
    SqlInjection,
    CrossSiteScripting,
    CommandInjection,
    PathTraversal,
    InsecureDeserialization,
    HardcodedCredentials,
    WeakCryptography,
    InsecureRandomness,
    UnvalidatedRedirect,
    XmlExternalEntity,
    BrokenAuthentication,
    SensitiveDataExposure,
    BrokenAccessControl,
    SecurityMisconfiguration,
    UsingComponentsWithKnownVulnerabilities,
    InsufficientLogging,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Severity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

// Enhanced Pattern Detection with production features
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductionPatternDetection {
    pub pattern_id: String,
    pub pattern_name: String,
    pub pattern_category: PatternCategory,
    pub confidence_score: f64,
    pub instances: Vec<PatternInstance>,
    pub metadata: PatternMetadata,
    pub recommendations: Vec<PatternRecommendation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PatternCategory {
    Creational,
    Structural,
    Behavioral,
    Architectural,
    Concurrency,
    Security,
    Performance,
    AntiPattern,
    CodeSmell,
    Custom,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternInstance {
    pub file_path: String,
    pub start_line: usize,
    pub end_line: usize,
    pub code_snippet: String,
    pub involved_classes: Vec<String>,
    pub involved_methods: Vec<String>,
    pub complexity_impact: f64,
    pub quality_impact: QualityImpact,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityImpact {
    pub maintainability: f64,
    pub testability: f64,
    pub readability: f64,
    pub performance: f64,
    pub security: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternMetadata {
    pub first_detected: DateTime<Utc>,
    pub last_updated: DateTime<Utc>,
    pub detection_method: DetectionMethod,
    pub related_patterns: Vec<String>,
    pub documentation_links: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DetectionMethod {
    AstAnalysis,
    MachineLearning,
    RuleBasedHeuristics,
    StatisticalAnalysis,
    Hybrid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternRecommendation {
    pub recommendation_type: RecommendationType,
    pub description: String,
    pub impact: String,
    pub effort_estimate: EffortEstimate,
    pub code_example: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationType {
    Refactor,
    Keep,
    Monitor,
    Investigate,
    Remove,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EffortEstimate {
    pub hours: f64,
    pub complexity: ComplexityLevel,
    pub risk_level: RiskLevel,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComplexityLevel {
    Trivial,
    Simple,
    Moderate,
    Complex,
    VeryComplex,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

// Enhanced Code Embeddings with production features
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductionCodeEmbedding {
    pub file_path: String,
    pub chunk_id: String,
    pub chunk_index: usize,
    pub total_chunks: usize,
    pub embedding_vector: Vec<f32>,
    pub model_info: EmbeddingModelInfo,
    pub context: EmbeddingContext,
    pub metadata: EmbeddingMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingModelInfo {
    pub model_name: String,
    pub model_version: String,
    pub dimension: usize,
    pub context_window: usize,
    pub tokenizer: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingContext {
    pub code_snippet: String,
    pub surrounding_context: String,
    pub semantic_type: SemanticType,
    pub importance_score: f64,
    pub related_symbols: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SemanticType {
    FunctionDefinition,
    ClassDefinition,
    InterfaceDefinition,
    ImportStatement,
    ConfigurationBlock,
    TestCase,
    Documentation,
    ErrorHandling,
    BusinessLogic,
    DataProcessing,
    ApiEndpoint,
    Other,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingMetadata {
    pub generated_at: DateTime<Utc>,
    pub processing_time_ms: u64,
    pub token_count: usize,
    pub language: String,
    pub framework_context: Option<String>,
}

// Enhanced Repository Metrics with production features
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductionRepositoryMetrics {
    // Basic metrics
    pub total_files: usize,
    pub total_lines: usize,
    pub lines_of_code: usize,
    pub comment_lines: usize,
    pub blank_lines: usize,
    
    // Complexity metrics
    pub cyclomatic_complexity: ComplexityMetrics,
    pub cognitive_complexity: ComplexityMetrics,
    pub halstead_metrics: HalsteadMetrics,
    pub maintainability_index: f64,
    
    // Architecture metrics
    pub module_count: usize,
    pub package_count: usize,
    pub average_module_size: f64,
    pub coupling_metrics: CouplingMetrics,
    pub cohesion_metrics: CohesionMetrics,
    
    // Quality metrics
    pub code_duplication: DuplicationMetrics,
    pub test_metrics: TestMetrics,
    pub documentation_metrics: DocumentationMetrics,
    pub technical_debt: TechnicalDebtMetrics,
    
    // Dependency metrics
    pub dependency_metrics: DependencyMetrics,
    pub circular_dependencies: Vec<CircularDependency>,
    pub dependency_graph: DependencyGraph,
    
    // Security metrics
    pub security_metrics: SecurityMetrics,
    pub vulnerability_summary: VulnerabilitySummary,
    
    // Performance indicators
    pub performance_indicators: PerformanceIndicators,
    pub hot_spots: Vec<CodeHotSpot>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplexityMetrics {
    pub average: f64,
    pub median: f64,
    pub p90: f64,
    pub p95: f64,
    pub max: f64,
    pub distribution: HashMap<String, usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HalsteadMetrics {
    pub vocabulary: usize,
    pub length: usize,
    pub calculated_length: f64,
    pub volume: f64,
    pub difficulty: f64,
    pub effort: f64,
    pub time: f64,
    pub bugs: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CouplingMetrics {
    pub afferent_coupling: usize,
    pub efferent_coupling: usize,
    pub instability: f64,
    pub abstractness: f64,
    pub distance_from_main_sequence: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CohesionMetrics {
    pub lack_of_cohesion: f64,
    pub tight_class_cohesion: f64,
    pub loose_class_cohesion: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DuplicationMetrics {
    pub percentage: f64,
    pub total_duplicated_lines: usize,
    pub total_duplicated_blocks: usize,
    pub largest_duplicate_block: usize,
    pub duplicate_hotspots: Vec<DuplicateHotspot>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DuplicateHotspot {
    pub files: Vec<String>,
    pub line_ranges: Vec<(usize, usize)>,
    pub size: usize,
    pub impact: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestMetrics {
    pub test_count: usize,
    pub test_coverage_estimate: f64,
    pub test_to_code_ratio: f64,
    pub assertion_density: f64,
    pub test_execution_time_estimate: f64,
    pub flaky_test_indicators: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentationMetrics {
    pub documentation_coverage: f64,
    pub api_documentation_coverage: f64,
    pub inline_comment_density: f64,
    pub readme_quality_score: f64,
    pub missing_documentation: Vec<MissingDocumentation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MissingDocumentation {
    pub item_type: DocumentationType,
    pub item_name: String,
    pub file_path: String,
    pub priority: DocumentationPriority,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DocumentationType {
    PublicApi,
    ComplexFunction,
    Configuration,
    Module,
    Class,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DocumentationPriority {
    Critical,
    High,
    Medium,
    Low,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TechnicalDebtMetrics {
    pub total_debt_hours: f64,
    pub debt_ratio: f64,
    pub debt_categories: HashMap<String, f64>,
    pub high_debt_files: Vec<TechnicalDebtItem>,
    pub remediation_cost_estimate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TechnicalDebtItem {
    pub file_path: String,
    pub debt_hours: f64,
    pub debt_types: Vec<String>,
    pub remediation_priority: RemediationPriority,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RemediationPriority {
    Immediate,
    Short,
    Medium,
    Long,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyMetrics {
    pub total_dependencies: usize,
    pub direct_dependencies: usize,
    pub transitive_dependencies: usize,
    pub outdated_dependencies: usize,
    pub vulnerable_dependencies: usize,
    pub license_issues: Vec<LicenseIssue>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseIssue {
    pub dependency_name: String,
    pub license_type: String,
    pub issue_type: LicenseIssueType,
    pub severity: Severity,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LicenseIssueType {
    Incompatible,
    Unknown,
    Restrictive,
    Deprecated,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircularDependency {
    pub cycle: Vec<String>,
    pub impact: String,
    pub suggested_break_point: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyGraph {
    pub nodes: Vec<DependencyNode>,
    pub edges: Vec<DependencyEdge>,
    pub clusters: Vec<DependencyCluster>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyNode {
    pub id: String,
    pub name: String,
    pub node_type: DependencyNodeType,
    pub metrics: NodeMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DependencyNodeType {
    Module,
    Package,
    Class,
    Interface,
    Function,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeMetrics {
    pub in_degree: usize,
    pub out_degree: usize,
    pub betweenness_centrality: f64,
    pub page_rank: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyEdge {
    pub from: String,
    pub to: String,
    pub edge_type: DependencyEdgeType,
    pub weight: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DependencyEdgeType {
    Import,
    Inheritance,
    Composition,
    Usage,
    TestDependency,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyCluster {
    pub cluster_id: String,
    pub nodes: Vec<String>,
    pub cohesion_score: f64,
    pub suggested_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityMetrics {
    pub security_score: f64,
    pub vulnerability_count: usize,
    pub high_risk_patterns: usize,
    pub security_hotspots: Vec<SecurityHotspot>,
    pub compliance_status: ComplianceStatus,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityHotspot {
    pub file_path: String,
    pub hotspot_type: SecurityHotspotType,
    pub risk_level: RiskLevel,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityHotspotType {
    Authentication,
    Authorization,
    InputValidation,
    Cryptography,
    SessionManagement,
    ErrorHandling,
    Logging,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceStatus {
    pub owasp_compliance: f64,
    pub pci_compliance: Option<f64>,
    pub hipaa_compliance: Option<f64>,
    pub gdpr_compliance: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilitySummary {
    pub critical: usize,
    pub high: usize,
    pub medium: usize,
    pub low: usize,
    pub info: usize,
    pub false_positives: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceIndicators {
    pub estimated_memory_usage: MemoryUsageEstimate,
    pub algorithmic_complexity: AlgorithmicComplexity,
    pub database_query_analysis: DatabaseQueryAnalysis,
    pub api_performance_indicators: ApiPerformanceIndicators,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryUsageEstimate {
    pub peak_memory_mb: f64,
    pub memory_leaks_suspected: usize,
    pub large_object_allocations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlgorithmicComplexity {
    pub time_complexity_issues: Vec<ComplexityIssue>,
    pub space_complexity_issues: Vec<ComplexityIssue>,
    pub optimization_opportunities: Vec<OptimizationOpportunity>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplexityIssue {
    pub location: String,
    pub current_complexity: String,
    pub impact: String,
    pub suggested_improvement: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationOpportunity {
    pub optimization_type: OptimizationType,
    pub location: String,
    pub potential_improvement: String,
    pub effort_estimate: EffortEstimate,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OptimizationType {
    AlgorithmOptimization,
    CachingOpportunity,
    ParallelizationOpportunity,
    DatabaseQueryOptimization,
    NetworkCallReduction,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseQueryAnalysis {
    pub n_plus_one_queries: Vec<String>,
    pub missing_indexes: Vec<String>,
    pub complex_queries: Vec<String>,
    pub optimization_suggestions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiPerformanceIndicators {
    pub slow_endpoints: Vec<SlowEndpoint>,
    pub high_latency_operations: Vec<String>,
    pub rate_limiting_recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlowEndpoint {
    pub endpoint: String,
    pub estimated_latency_ms: f64,
    pub bottleneck: String,
    pub optimization_suggestion: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeHotSpot {
    pub file_path: String,
    pub hotspot_type: HotSpotType,
    pub score: f64,
    pub contributors: Vec<String>,
    pub change_frequency: f64,
    pub bug_frequency: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HotSpotType {
    HighComplexity,
    FrequentChanges,
    BugProne,
    PerformanceBottleneck,
    SecurityRisk,
}

// Error handling structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductionError {
    pub error_id: String,
    pub error_type: ProductionErrorType,
    pub message: String,
    pub context: HashMap<String, String>,
    pub stack_trace: Option<Vec<String>>,
    pub recovery_suggestions: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProductionErrorType {
    ParseError,
    AnalysisError,
    ResourceError,
    TimeoutError,
    ConfigurationError,
    ValidationError,
    ExternalServiceError,
    UnknownError,
}