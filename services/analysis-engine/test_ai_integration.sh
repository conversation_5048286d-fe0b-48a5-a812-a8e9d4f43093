#!/bin/bash
# Test AI Integration and Diagnose IAM Issues for Analysis Engine

set -e

echo "=== Analysis Engine AI Integration Test ==="
echo "Testing Vertex AI access and IAM permissions"
echo ""

# Check if we have credentials
if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
    echo "ERROR: GOOGLE_APPLICATION_CREDENTIALS not set"
    echo "Please set: export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json"
    exit 1
fi

if [ ! -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
    echo "ERROR: Service account key file not found at: $GOOGLE_APPLICATION_CREDENTIALS"
    exit 1
fi

echo "✓ Service account key found at: $GOOGLE_APPLICATION_CREDENTIALS"

# Extract service account email
SERVICE_ACCOUNT_EMAIL=$(cat $GOOGLE_APPLICATION_CREDENTIALS | grep -oP '"client_email":\s*"\K[^"]+')
echo "✓ Service account: $SERVICE_ACCOUNT_EMAIL"

PROJECT_ID=${GCP_PROJECT_ID:-"vibe-match-463114"}
REGION=${GCP_REGION:-"us-central1"}

echo ""
echo "=== Checking Current IAM Permissions ==="
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo ""

# Check current IAM roles
echo "Current IAM roles for service account:"
gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --format="table(bindings.role)" 2>/dev/null || echo "Failed to get IAM policy"

echo ""
echo "=== Required IAM Roles for Vertex AI ==="
echo "The following roles are required for Vertex AI integration:"
echo "- roles/aiplatform.user (for model predictions)"
echo "- roles/serviceusage.serviceUsageConsumer (for API access)"
echo ""

echo "=== Testing Vertex AI API Access ==="
# Try to list models to test basic API access
echo "Testing list models API:"
gcloud ai models list --region=$REGION --project=$PROJECT_ID 2>&1 | head -5 || true

echo ""
echo "=== Testing Gemini 2.5 Model Access ==="
# Create a test request file
cat > /tmp/gemini_test_request.json << EOF
{
  "contents": [{
    "role": "user",
    "parts": [{
      "text": "Hello, this is a test. Please respond with 'OK'."
    }]
  }],
  "generationConfig": {
    "temperature": 0.1,
    "topP": 0.8,
    "topK": 40,
    "maxOutputTokens": 100
  }
}
EOF

# Test Gemini 2.5 Flash
echo "Testing Gemini 2.5 Flash model..."
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth application-default print-access-token)" \
    -H "Content-Type: application/json" \
    -d @/tmp/gemini_test_request.json \
    "https://$REGION-aiplatform.googleapis.com/v1/projects/$PROJECT_ID/locations/$REGION/publishers/google/models/gemini-2.5-flash:predict" \
    -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Failed to call Gemini 2.5 Flash"

echo ""
echo "Testing Gemini 2.5 Pro model..."
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth application-default print-access-token)" \
    -H "Content-Type: application/json" \
    -d @/tmp/gemini_test_request.json \
    "https://$REGION-aiplatform.googleapis.com/v1/projects/$PROJECT_ID/locations/$REGION/publishers/google/models/gemini-2.5-pro:predict" \
    -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Failed to call Gemini 2.5 Pro"

echo ""
echo "=== Testing Text Embeddings Model ==="
# Create embeddings test request
cat > /tmp/embeddings_test_request.json << EOF
{
  "instances": [{
    "content": "Test code for embedding generation",
    "task_type": "CODE_RETRIEVAL_QUERY"
  }],
  "parameters": {
    "outputDimensionality": 768
  }
}
EOF

echo "Testing text-embedding-005 model..."
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth application-default print-access-token)" \
    -H "Content-Type: application/json" \
    -d @/tmp/embeddings_test_request.json \
    "https://$REGION-aiplatform.googleapis.com/v1/projects/$PROJECT_ID/locations/$REGION/publishers/google/models/text-embedding-005:predict" \
    -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Failed to call text-embedding-005"

# Clean up
rm -f /tmp/gemini_test_request.json /tmp/embeddings_test_request.json

echo ""
echo "=== IAM Permission Fix Script ==="
echo "To grant the required permissions, run the following commands:"
echo ""
echo "# Grant Vertex AI User role"
echo "gcloud projects add-iam-policy-binding $PROJECT_ID \\"
echo "    --member=\"serviceAccount:$SERVICE_ACCOUNT_EMAIL\" \\"
echo "    --role=\"roles/aiplatform.user\""
echo ""
echo "# Grant Service Usage Consumer role"
echo "gcloud projects add-iam-policy-binding $PROJECT_ID \\"
echo "    --member=\"serviceAccount:$SERVICE_ACCOUNT_EMAIL\" \\"
echo "    --role=\"roles/serviceusage.serviceUsageConsumer\""
echo ""
echo "# (Optional) For development, you might also need:"
echo "# gcloud projects add-iam-policy-binding $PROJECT_ID \\"
echo "#     --member=\"serviceAccount:$SERVICE_ACCOUNT_EMAIL\" \\"
echo "#     --role=\"roles/ml.developer\""
echo ""
echo "=== Test Complete ==="