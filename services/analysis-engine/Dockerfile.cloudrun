# Build stage
FROM rust:latest as builder

# Install dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /usr/src/app

# Copy manifests
COPY Cargo.toml Cargo.lock ./

# Copy source
COPY src ./src
COPY benches ./benches

# Build release binary
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -u 1001 appuser

# Copy binary from builder
COPY --from=builder /usr/src/app/target/release/analysis-engine /usr/local/bin/analysis-engine

# Set ownership
RUN chown appuser:appuser /usr/local/bin/analysis-engine

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8001

# Set environment variables for Cloud Run
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1
# Cloud Run will use its metadata server for authentication automatically
# Do not set GOOGLE_APPLICATION_CREDENTIALS as it interferes with metadata server auth

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run the binary
CMD ["analysis-engine"]