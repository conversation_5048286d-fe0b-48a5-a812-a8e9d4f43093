"""
FastAPI Application Entry Point

Main FastAPI application configuration and setup.
"""

import logging
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, Response, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.sessions import SessionMiddleware
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY, HTTP_500_INTERNAL_SERVER_ERROR
import structlog
import asyncio
from datetime import datetime

from ..config.settings import get_settings
from ..database.connection import init_database, close_database
from ..ml.manager import init_ml_manager, close_ml_manager
from .routes import patterns, health, ml, repositories, analytics
from .websocket import websocket_endpoint
from .middleware import (
    setup_structured_logging,
    setup_monitoring,
    setup_error_handling,
    setup_rate_limiting,
    setup_tracing,
    StructuredLoggingMiddleware,
    PrometheusMiddleware,
    ErrorHandlingMiddleware,
    RateLimitMiddleware,
    RequestContextMiddleware,
    PerformanceMonitoringMiddleware,
)
from .utils.cache import init_cache, close_cache

# Configure structured logging
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management with proper startup and shutdown."""
    settings = get_settings()
    app.state.settings = settings
    app.state.startup_time = datetime.utcnow()
    
    logger.info("Starting Pattern Mining Service", version="1.0.0", environment=settings.environment)
    
    try:
        # Initialize database connection
        logger.info("Initializing database connection")
        await init_database(settings)
        
        # Initialize cache (Redis)
        logger.info("Initializing cache connection")
        await init_cache(settings)
        
        # Initialize ML models and manager
        logger.info("Initializing ML models")
        await init_ml_manager(settings)
        
        # Setup monitoring and tracing
        if settings.enable_tracing:
            logger.info("Setting up distributed tracing")
            setup_tracing(settings)
        
        if settings.enable_metrics:
            logger.info("Setting up metrics collection")
            setup_monitoring(app, settings)
        
        logger.info("Service startup completed successfully")
        
        yield
        
    except Exception as e:
        logger.error("Failed to start service", error=str(e), exc_info=True)
        raise
    finally:
        # Cleanup resources
        logger.info("Shutting down Pattern Mining Service")
        
        try:
            # Close ML manager
            await close_ml_manager()
            logger.info("ML manager closed")
            
            # Close cache connection
            await close_cache()
            logger.info("Cache connection closed")
            
            # Close database connection
            await close_database()
            logger.info("Database connection closed")
            
        except Exception as e:
            logger.error("Error during shutdown", error=str(e), exc_info=True)
        
        logger.info("Service shutdown completed")


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle validation errors with structured response."""
    logger.warning(
        "Validation error",
        request_id=getattr(request.state, 'request_id', 'unknown'),
        errors=exc.errors(),
        body=exc.body,
    )
    
    return JSONResponse(
        status_code=HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Validation Error",
            "message": "Invalid request data",
            "details": exc.errors(),
            "request_id": getattr(request.state, 'request_id', 'unknown'),
            "timestamp": datetime.utcnow().isoformat(),
        },
    )


async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """Handle HTTP exceptions with structured response."""
    logger.warning(
        "HTTP exception",
        request_id=getattr(request.state, 'request_id', 'unknown'),
        status_code=exc.status_code,
        detail=exc.detail,
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP Error",
            "message": exc.detail,
            "status_code": exc.status_code,
            "request_id": getattr(request.state, 'request_id', 'unknown'),
            "timestamp": datetime.utcnow().isoformat(),
        },
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions with structured response."""
    logger.error(
        "Unhandled exception",
        request_id=getattr(request.state, 'request_id', 'unknown'),
        error=str(exc),
        exc_info=True,
    )
    
    return JSONResponse(
        status_code=HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "request_id": getattr(request.state, 'request_id', 'unknown'),
            "timestamp": datetime.utcnow().isoformat(),
        },
    )


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    # Setup structured logging
    setup_structured_logging(settings)
    
    app = FastAPI(
        title="Pattern Mining Service",
        description="Advanced pattern detection and mining for code analysis",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs" if settings.environment == "development" else None,
        redoc_url="/redoc" if settings.environment == "development" else None,
        openapi_url="/openapi.json" if settings.environment != "production" else None,
        generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}" if route.tags else route.name,
    )
    
    # Add exception handlers
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    # Add security middleware
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"],  # Configure with actual hosts in production
        )
    
    # Add session middleware
    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.secret_key,
        max_age=3600,  # 1 hour
        same_site="lax",
        https_only=settings.environment == "production",
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Request-ID", "X-Process-Time", "X-Rate-Limit-Remaining"],
    )
    
    # Add compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Add custom middleware (order matters - reverse order of execution)
    app.add_middleware(PerformanceMonitoringMiddleware)
    app.add_middleware(PrometheusMiddleware)
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RateLimitMiddleware, settings=settings)
    app.add_middleware(RequestContextMiddleware)
    app.add_middleware(StructuredLoggingMiddleware)
    
    # Setup error handling
    setup_error_handling(app, settings)
    
    # Setup rate limiting
    setup_rate_limiting(app, settings)
    
    # Include routers
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(patterns.router, prefix="/api/v1/patterns", tags=["patterns"])
    app.include_router(repositories.router, prefix="/api/v1/repositories", tags=["repositories"])
    app.include_router(analytics.router, prefix="/api/v1/analytics", tags=["analytics"])
    app.include_router(ml.router, prefix="/api/v1/ml", tags=["ml"])
    
    # Add WebSocket endpoint
    app.websocket("/ws")(websocket_endpoint)
    
    # Add startup event for logging
    @app.on_event("startup")
    async def startup_event():
        logger.info("FastAPI application started", version="1.0.0")
    
    # Add shutdown event for logging
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("FastAPI application shutting down")
    
    return app


# Create the FastAPI application instance
app = create_app()