"""
Cache Utilities

Redis client setup and caching utilities.
"""

import json
import pickle
from typing import Any, Optional, Dict, Union
from functools import lru_cache
import redis.asyncio as redis
import structlog
from ...config.settings import get_settings

logger = structlog.get_logger()

# Global Redis client
_redis_client: Optional[redis.Redis] = None


@lru_cache()
def get_redis_client(redis_config: Optional[Dict[str, Any]] = None) -> redis.Redis:
    """Get Redis client instance."""
    global _redis_client
    
    if _redis_client is None:
        if redis_config is None:
            settings = get_settings()
            redis_config = settings.redis_config
        
        _redis_client = redis.from_url(
            redis_config["url"],
            max_connections=redis_config.get("max_connections", 10),
            decode_responses=True,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
        )
    
    return _redis_client


class CacheManager:
    """Redis-based cache manager."""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.redis_client = redis_client or get_redis_client()
        self.default_ttl = 3600  # 1 hour
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            value = await self.redis_client.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error("Cache get failed", key=key, error=str(e))
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """Set value in cache."""
        try:
            serialized_value = json.dumps(value, default=str)
            ttl = ttl or self.default_ttl
            
            await self.redis_client.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.error("Cache set failed", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            await self.redis_client.delete(key)
            return True
        except Exception as e:
            logger.error("Cache delete failed", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            return await self.redis_client.exists(key)
        except Exception as e:
            logger.error("Cache exists check failed", key=key, error=str(e))
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter in cache."""
        try:
            return await self.redis_client.incrby(key, amount)
        except Exception as e:
            logger.error("Cache increment failed", key=key, error=str(e))
            return None
    
    async def get_many(self, keys: list[str]) -> Dict[str, Any]:
        """Get multiple values from cache."""
        try:
            values = await self.redis_client.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                if value:
                    result[key] = json.loads(value)
            return result
        except Exception as e:
            logger.error("Cache get_many failed", keys=keys, error=str(e))
            return {}
    
    async def set_many(
        self,
        mapping: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """Set multiple values in cache."""
        try:
            ttl = ttl or self.default_ttl
            
            # Use pipeline for atomic operation
            async with self.redis_client.pipeline(transaction=True) as pipe:
                for key, value in mapping.items():
                    serialized_value = json.dumps(value, default=str)
                    pipe.setex(key, ttl, serialized_value)
                
                await pipe.execute()
            return True
        except Exception as e:
            logger.error("Cache set_many failed", mapping=mapping, error=str(e))
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear keys matching pattern."""
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                return await self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.error("Cache clear_pattern failed", pattern=pattern, error=str(e))
            return 0


class PatternCache:
    """Specialized cache for pattern detection results."""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager or CacheManager()
        self.pattern_ttl = 7200  # 2 hours
    
    def _get_pattern_key(self, code_hash: str, language: str, options: Dict[str, Any]) -> str:
        """Generate cache key for pattern detection."""
        options_str = json.dumps(options, sort_keys=True)
        return f"pattern:{code_hash}:{language}:{hash(options_str)}"
    
    async def get_patterns(
        self,
        code_hash: str,
        language: str,
        options: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Get cached pattern detection results."""
        key = self._get_pattern_key(code_hash, language, options)
        return await self.cache_manager.get(key)
    
    async def set_patterns(
        self,
        code_hash: str,
        language: str,
        options: Dict[str, Any],
        patterns: Dict[str, Any]
    ) -> bool:
        """Cache pattern detection results."""
        key = self._get_pattern_key(code_hash, language, options)
        return await self.cache_manager.set(key, patterns, self.pattern_ttl)
    
    async def clear_patterns(self, code_hash: str) -> int:
        """Clear cached patterns for specific code."""
        pattern = f"pattern:{code_hash}:*"
        return await self.cache_manager.clear_pattern(pattern)


class ModelCache:
    """Specialized cache for ML model results."""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager or CacheManager()
        self.model_ttl = 3600  # 1 hour
    
    def _get_model_key(self, model_id: str, input_hash: str) -> str:
        """Generate cache key for model inference."""
        return f"model:{model_id}:{input_hash}"
    
    async def get_inference(
        self,
        model_id: str,
        input_hash: str
    ) -> Optional[Dict[str, Any]]:
        """Get cached model inference results."""
        key = self._get_model_key(model_id, input_hash)
        return await self.cache_manager.get(key)
    
    async def set_inference(
        self,
        model_id: str,
        input_hash: str,
        result: Dict[str, Any]
    ) -> bool:
        """Cache model inference results."""
        key = self._get_model_key(model_id, input_hash)
        return await self.cache_manager.set(key, result, self.model_ttl)
    
    async def clear_model_cache(self, model_id: str) -> int:
        """Clear cached results for specific model."""
        pattern = f"model:{model_id}:*"
        return await self.cache_manager.clear_pattern(pattern)


class VectorCache:
    """Specialized cache for vector embeddings."""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager or CacheManager()
        self.vector_ttl = 86400  # 24 hours
    
    def _get_vector_key(self, text_hash: str, model_name: str) -> str:
        """Generate cache key for vector embeddings."""
        return f"vector:{model_name}:{text_hash}"
    
    async def get_embedding(
        self,
        text_hash: str,
        model_name: str
    ) -> Optional[list[float]]:
        """Get cached vector embedding."""
        key = self._get_vector_key(text_hash, model_name)
        result = await self.cache_manager.get(key)
        return result.get("embedding") if result else None
    
    async def set_embedding(
        self,
        text_hash: str,
        model_name: str,
        embedding: list[float]
    ) -> bool:
        """Cache vector embedding."""
        key = self._get_vector_key(text_hash, model_name)
        return await self.cache_manager.set(
            key,
            {"embedding": embedding},
            self.vector_ttl
        )
    
    async def clear_embeddings(self, model_name: str) -> int:
        """Clear cached embeddings for specific model."""
        pattern = f"vector:{model_name}:*"
        return await self.cache_manager.clear_pattern(pattern)


# Cache initialization and cleanup functions
async def init_cache(settings) -> None:
    """Initialize Redis cache connection."""
    global _redis_client
    try:
        _redis_client = redis.from_url(
            settings.redis_url,
            max_connections=settings.redis_max_connections,
            decode_responses=True,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
        )
        
        # Test connection
        await _redis_client.ping()
        logger.info("Redis cache initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize Redis cache", error=str(e))
        raise


async def close_cache() -> None:
    """Close Redis cache connection."""
    global _redis_client
    if _redis_client:
        try:
            await _redis_client.close()
            logger.info("Redis cache closed successfully")
        except Exception as e:
            logger.error("Error closing Redis cache", error=str(e))
    _redis_client = None


# Global cache instances
@lru_cache()
def get_cache_manager() -> CacheManager:
    """Get global cache manager instance."""
    return CacheManager()


@lru_cache()
def get_pattern_cache() -> PatternCache:
    """Get global pattern cache instance."""
    return PatternCache()


@lru_cache()
def get_model_cache() -> ModelCache:
    """Get global model cache instance."""
    return ModelCache()


@lru_cache()
def get_vector_cache() -> VectorCache:
    """Get global vector cache instance."""
    return VectorCache()