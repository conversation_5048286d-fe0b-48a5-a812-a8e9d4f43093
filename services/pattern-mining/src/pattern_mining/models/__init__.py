"""
Pydantic Models Module

Comprehensive data models and validation schemas for the pattern mining service.
Includes API models, ML models, database models, and common utilities.
"""

# Base models
from .base import (
    BaseModel,
    TimestampedModel,
    IdentifiableModel,
    ErrorResponse,
    SuccessResponse,
    PaginatedResponse,
    JobStatus
)

# API models
from .api import (
    PatternDetectionRequest,
    PatternDetectionResponse,
    BatchDetectionRequest,
    BatchDetectionResponse,
    StreamingDetectionRequest,
    StreamingDetectionResponse,
    RepositoryAnalysisRequest,
    RepositoryAnalysisResponse,
    DetectionConfig,
    PatternSummary,
    AnalysisScope,
    ProcessingMode
)

# Pattern models
from .patterns import (
    PatternType,
    SeverityLevel,
    DetectionType,
    ConfidenceLevel,
    PatternCategory,
    PatternLocation,
    ConfidenceMetrics,
    PatternExplanation,
    PatternRecommendation,
    CodeExample,
    DetectedPattern,
    PatternResult,  # Alias for backward compatibility
    PatternAnalysisJob,
    PatternStatistics,
    PatternFeedback,
    PatternTemplate
)

# ML models
from .ml import (
    ModelType,
    ModelStatus,
    TrainingStatus,
    FeatureType,
    ModelInfo,
    EnhancedModelInfo,
    EvaluationMetrics,
    ModelTrainingRequest,
    ModelTrainingResponse,
    TrainingJob,
    ModelPredictionRequest,
    ModelPredictionResponse,
    ModelDeploymentRequest,
    ModelDeploymentResponse,
    FeatureExtractionRequest,
    FeatureExtractionResponse,
    CodeFeatures,
    StructuralFeatures,
    LexicalFeatures,
    SemanticFeatures,
    StatisticalFeatures,
    EnsembleModelRequest,
    EnsembleModelResponse,
    BatchInferenceRequest,
    BatchInferenceResponse,
    ModelPerformanceMetrics,
    ModelComparisonRequest,
    ModelComparisonResponse,
    ModelRegistry
)

# Database models
from .database import (
    BigQueryFieldType,
    BigQueryFieldMode,
    BigQuerySchema,
    BigQueryField,
    BigQueryDataset,
    BigQueryTable,
    BigQueryQueryRequest,
    BigQueryQueryResponse,
    RepositoryRecord,
    PatternDetectionRecord,
    FeatureExtractionRecord,
    ModelTrainingRecord,
    ModelInferenceRecord,
    PatternFeedbackRecord,
    PatternAnalyticsRecord,
    PATTERN_MINING_SCHEMAS
)

# Common models
from .common import (
    ErrorCode,
    ErrorSeverity,
    ErrorResponse as CommonErrorResponse,
    SuccessResponse as CommonSuccessResponse,
    ValidationError,
    PaginationParams,
    PaginatedResponse as CommonPaginatedResponse,
    CursorPaginationParams,
    CursorPaginatedResponse,
    SearchParams,
    SearchResponse,
    HealthStatus,
    HealthCheck,
    ComponentHealth,
    APIResponse,
    RateLimitInfo,
    CacheInfo
)

# Health models
from .health import HealthResponse

# Re-export with consistent naming
__all__ = [
    # Base models
    "BaseModel",
    "TimestampedModel",
    "IdentifiableModel",
    "ErrorResponse",
    "SuccessResponse",
    "PaginatedResponse",
    "JobStatus",
    
    # API models
    "PatternDetectionRequest",
    "PatternDetectionResponse",
    "BatchDetectionRequest",
    "BatchDetectionResponse",
    "StreamingDetectionRequest",
    "StreamingDetectionResponse",
    "RepositoryAnalysisRequest",
    "RepositoryAnalysisResponse",
    "DetectionConfig",
    "PatternSummary",
    "AnalysisScope",
    "ProcessingMode",
    
    # Pattern models
    "PatternType",
    "SeverityLevel",
    "DetectionType",
    "ConfidenceLevel",
    "PatternCategory",
    "PatternLocation",
    "ConfidenceMetrics",
    "PatternExplanation",
    "PatternRecommendation",
    "CodeExample",
    "DetectedPattern",
    "PatternResult",
    "PatternAnalysisJob",
    "PatternStatistics",
    "PatternFeedback",
    "PatternTemplate",
    
    # ML models
    "ModelType",
    "ModelStatus",
    "TrainingStatus",
    "FeatureType",
    "ModelInfo",
    "EnhancedModelInfo",
    "EvaluationMetrics",
    "ModelTrainingRequest",
    "ModelTrainingResponse",
    "TrainingJob",
    "ModelPredictionRequest",
    "ModelPredictionResponse",
    "ModelDeploymentRequest",
    "ModelDeploymentResponse",
    "FeatureExtractionRequest",
    "FeatureExtractionResponse",
    "CodeFeatures",
    "StructuralFeatures",
    "LexicalFeatures",
    "SemanticFeatures",
    "StatisticalFeatures",
    "EnsembleModelRequest",
    "EnsembleModelResponse",
    "BatchInferenceRequest",
    "BatchInferenceResponse",
    "ModelPerformanceMetrics",
    "ModelComparisonRequest",
    "ModelComparisonResponse",
    "ModelRegistry",
    
    # Database models
    "BigQueryFieldType",
    "BigQueryFieldMode",
    "BigQuerySchema",
    "BigQueryField",
    "BigQueryDataset",
    "BigQueryTable",
    "BigQueryQueryRequest",
    "BigQueryQueryResponse",
    "RepositoryRecord",
    "PatternDetectionRecord",
    "FeatureExtractionRecord",
    "ModelTrainingRecord",
    "ModelInferenceRecord",
    "PatternFeedbackRecord",
    "PatternAnalyticsRecord",
    "PATTERN_MINING_SCHEMAS",
    
    # Common models
    "ErrorCode",
    "ErrorSeverity",
    "CommonErrorResponse",
    "CommonSuccessResponse",
    "ValidationError",
    "PaginationParams",
    "CommonPaginatedResponse",
    "CursorPaginationParams",
    "CursorPaginatedResponse",
    "SearchParams",
    "SearchResponse",
    "HealthStatus",
    "HealthCheck",
    "ComponentHealth",
    "APIResponse",
    "RateLimitInfo",
    "CacheInfo",
    
    # Health models
    "HealthResponse",
]