"""
Health Check Models

Pydantic models for health check and monitoring endpoints.
"""

from pydantic import Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

from .base import BaseModel


class HealthStatus(str, Enum):
    """Health status enumeration."""
    
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: HealthStatus = Field(..., description="Service health status")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    uptime: Optional[float] = Field(None, description="Service uptime in seconds")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional health details")


class ComponentHealth(BaseModel):
    """Individual component health status."""
    
    name: str = Field(..., description="Component name")
    status: HealthStatus = Field(..., description="Component health status")
    message: Optional[str] = Field(None, description="Health status message")
    last_checked: datetime = Field(default_factory=datetime.utcnow)
    response_time: Optional[float] = Field(None, description="Response time in seconds")


class DetailedHealthResponse(BaseModel):
    """Detailed health check response with component status."""
    
    status: HealthStatus = Field(..., description="Overall service health status")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    uptime: Optional[float] = Field(None, description="Service uptime in seconds")
    components: list[ComponentHealth] = Field(default_factory=list, description="Component health status")
    
    @property
    def is_healthy(self) -> bool:
        """Check if the service is healthy."""
        return self.status == HealthStatus.HEALTHY
    
    @property
    def unhealthy_components(self) -> list[ComponentHealth]:
        """Get list of unhealthy components."""
        return [
            component for component in self.components
            if component.status in [HealthStatus.UNHEALTHY, HealthStatus.DEGRADED]
        ]