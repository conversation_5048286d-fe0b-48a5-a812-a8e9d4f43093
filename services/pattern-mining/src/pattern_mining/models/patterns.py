"""
Pattern Detection Models

Comprehensive Pydantic models for pattern detection and analysis operations,
including ML-specific pattern types, confidence scoring, and advanced detection results.
"""

from pydantic import Field, validator, root_validator
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime
from enum import Enum
import numpy as np

from .base import BaseModel, TimestampedModel, JobStatus


class PatternType(str, Enum):
    """Comprehensive pattern type enumeration."""
    
    # Design patterns
    DESIGN_PATTERN = "design_pattern"
    ARCHITECTURAL_PATTERN = "architectural_pattern"
    CREATIONAL_PATTERN = "creational_pattern"
    STRUCTURAL_PATTERN = "structural_pattern"
    BEHAVIORAL_PATTERN = "behavioral_pattern"
    
    # Anti-patterns and code smells
    ANTI_PATTERN = "anti_pattern"
    CODE_SMELL = "code_smell"
    ARCHITECTURAL_SMELL = "architectural_smell"
    
    # Security vulnerabilities
    SECURITY_VULNERABILITY = "security_vulnerability"
    SECURITY_ISSUE = "security_issue"
    PRIVACY_ISSUE = "privacy_issue"
    
    # Performance issues
    PERFORMANCE_ISSUE = "performance_issue"
    SCALABILITY_ISSUE = "scalability_issue"
    MEMORY_LEAK = "memory_leak"
    
    # ML-specific patterns
    ML_PATTERN = "ml_pattern"
    ML_ANTIPATTERN = "ml_antipattern"
    DATA_LEAK = "data_leak"
    MODEL_BIAS = "model_bias"
    
    # Discovered patterns
    DISCOVERED_PATTERN = "discovered_pattern"
    ANOMALY = "anomaly"
    UNUSUAL_CONSTRUCT = "unusual_construct"
    
    # Quality and maintainability
    MAINTAINABILITY_ISSUE = "maintainability_issue"
    READABILITY_ISSUE = "readability_issue"
    DOCUMENTATION_ISSUE = "documentation_issue"


class SeverityLevel(str, Enum):
    """Severity level enumeration."""
    
    INFO = "info"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DetectionType(str, Enum):
    """Detection type enumeration."""
    
    STATIC_ANALYSIS = "static_analysis"
    ML_INFERENCE = "ml_inference"
    HEURISTIC = "heuristic"
    HYBRID = "hybrid"
    CLUSTERING = "clustering"
    DEEP_LEARNING = "deep_learning"
    ENSEMBLE = "ensemble"
    RULE_BASED = "rule_based"
    BIGQUERY_ML = "bigquery_ml"


class ConfidenceLevel(str, Enum):
    """Confidence level enumeration."""
    
    VERY_LOW = "very_low"      # 0.0 - 0.3
    LOW = "low"                # 0.3 - 0.5
    MEDIUM = "medium"          # 0.5 - 0.7
    HIGH = "high"              # 0.7 - 0.9
    VERY_HIGH = "very_high"    # 0.9 - 1.0


class PatternCategory(str, Enum):
    """Pattern category enumeration."""
    
    DESIGN = "design"
    SECURITY = "security"
    PERFORMANCE = "performance"
    MAINTAINABILITY = "maintainability"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    ARCHITECTURE = "architecture"
    ML_SPECIFIC = "ml_specific"


class PatternDetectionRequest(BaseModel):
    """Request model for pattern detection."""
    
    code: str = Field(..., description="Source code to analyze")
    language: str = Field(..., description="Programming language")
    detection_types: List[DetectionType] = Field(
        default=[DetectionType.ML_INFERENCE],
        description="Types of detection to perform"
    )
    pattern_types: Optional[List[PatternType]] = Field(
        None,
        description="Specific pattern types to detect"
    )
    confidence_threshold: float = Field(
        0.7,
        ge=0.0,
        le=1.0,
        description="Minimum confidence threshold for pattern detection"
    )
    enable_deep_analysis: bool = Field(
        False,
        description="Enable deep analysis for complex patterns"
    )
    context: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional context for pattern detection"
    )
    
    @validator('language')
    def validate_language(cls, v):
        """Validate programming language."""
        supported_languages = [
            'python', 'javascript', 'typescript', 'java', 'cpp', 'c', 'go',
            'rust', 'ruby', 'php', 'swift', 'kotlin', 'scala', 'clojure'
        ]
        if v.lower() not in supported_languages:
            raise ValueError(f"Language '{v}' not supported")
        return v.lower()


class PatternLocation(BaseModel):
    """Location information for detected patterns."""
    
    file_path: Optional[str] = Field(None, description="File path")
    line_start: int = Field(..., ge=1, description="Starting line number")
    line_end: int = Field(..., ge=1, description="Ending line number")
    column_start: Optional[int] = Field(None, ge=1, description="Starting column")
    column_end: Optional[int] = Field(None, ge=1, description="Ending column")
    
    # Additional location context
    function_name: Optional[str] = Field(None, description="Function/method name")
    class_name: Optional[str] = Field(None, description="Class name")
    module_name: Optional[str] = Field(None, description="Module name")
    
    @validator('line_end')
    def validate_line_end(cls, v, values):
        """Validate that line_end >= line_start."""
        if 'line_start' in values and v < values['line_start']:
            raise ValueError("line_end must be >= line_start")
        return v
    
    @property
    def line_count(self) -> int:
        """Get number of lines spanned by this location."""
        return self.line_end - self.line_start + 1


class ConfidenceMetrics(BaseModel):
    """Comprehensive confidence metrics for pattern detection."""
    
    # Overall confidence
    overall_confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Overall confidence score"
    )
    
    # Component confidences
    model_confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="ML model confidence"
    )
    feature_confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Feature-based confidence"
    )
    pattern_completeness: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Pattern completeness score"
    )
    cross_validation_confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Cross-validation confidence"
    )
    historical_accuracy: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Historical accuracy for this pattern type"
    )
    
    # Confidence level
    confidence_level: ConfidenceLevel = Field(
        ..., 
        description="Confidence level category"
    )
    
    # Uncertainty indicators
    uncertainty_factors: List[str] = Field(
        default_factory=list,
        description="Factors contributing to uncertainty"
    )
    
    # Calibration metrics
    calibration_score: Optional[float] = Field(
        None, 
        ge=0.0, 
        le=1.0, 
        description="Confidence calibration score"
    )
    
    @validator('confidence_level')
    def validate_confidence_level(cls, v, values):
        """Validate confidence level matches overall confidence."""
        if 'overall_confidence' in values:
            confidence = values['overall_confidence']
            expected_level = cls._get_confidence_level(confidence)
            if v != expected_level:
                raise ValueError(f"Confidence level {v} doesn't match score {confidence}")
        return v
    
    @staticmethod
    def _get_confidence_level(confidence: float) -> ConfidenceLevel:
        """Get confidence level from score."""
        if confidence < 0.3:
            return ConfidenceLevel.VERY_LOW
        elif confidence < 0.5:
            return ConfidenceLevel.LOW
        elif confidence < 0.7:
            return ConfidenceLevel.MEDIUM
        elif confidence < 0.9:
            return ConfidenceLevel.HIGH
        else:
            return ConfidenceLevel.VERY_HIGH
    
    @root_validator
    def validate_component_confidences(cls, values):
        """Validate component confidences are consistent."""
        overall = values.get('overall_confidence', 0.0)
        components = [
            values.get('model_confidence', 0.0),
            values.get('feature_confidence', 0.0),
            values.get('pattern_completeness', 0.0),
            values.get('cross_validation_confidence', 0.0),
            values.get('historical_accuracy', 0.0)
        ]
        
        # Check that overall confidence is reasonable given components
        avg_components = sum(components) / len(components)
        if abs(overall - avg_components) > 0.3:  # Allow some variance
            values['uncertainty_factors'] = values.get('uncertainty_factors', [])
            values['uncertainty_factors'].append("Component confidence mismatch")
        
        return values


class PatternExplanation(BaseModel):
    """Detailed explanation of why a pattern was detected."""
    
    # Basic explanation
    summary: str = Field(..., description="Brief explanation summary")
    detailed_explanation: str = Field(..., description="Detailed explanation")
    
    # Evidence
    evidence: List[str] = Field(
        default_factory=list,
        description="Evidence supporting the detection"
    )
    counter_evidence: List[str] = Field(
        default_factory=list,
        description="Evidence against the detection"
    )
    
    # Feature analysis
    key_features: List[str] = Field(
        default_factory=list,
        description="Key features that led to detection"
    )
    feature_importance: Dict[str, float] = Field(
        default_factory=dict,
        description="Feature importance scores"
    )
    
    # Pattern context
    pattern_context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Context about the pattern"
    )
    
    # Similar patterns
    similar_patterns: List[str] = Field(
        default_factory=list,
        description="Similar patterns that were considered"
    )
    
    # Reasoning chain
    reasoning_steps: List[str] = Field(
        default_factory=list,
        description="Step-by-step reasoning"
    )


class PatternRecommendation(BaseModel):
    """Recommendation for addressing a detected pattern."""
    
    # Basic recommendation
    title: str = Field(..., description="Recommendation title")
    description: str = Field(..., description="Detailed recommendation")
    
    # Priority and effort
    priority: str = Field(
        ..., 
        description="Priority level (low, medium, high, critical)"
    )
    effort_estimate: str = Field(
        ..., 
        description="Effort estimate (quick, medium, large)"
    )
    impact: str = Field(
        ..., 
        description="Expected impact (low, medium, high)"
    )
    
    # Implementation guidance
    implementation_steps: List[str] = Field(
        default_factory=list,
        description="Step-by-step implementation guide"
    )
    
    # Code examples
    code_examples: List['CodeExample'] = Field(
        default_factory=list,
        description="Before/after code examples"
    )
    
    # Resources
    references: List[str] = Field(
        default_factory=list,
        description="Reference links and documentation"
    )
    tools: List[str] = Field(
        default_factory=list,
        description="Recommended tools or libraries"
    )
    
    # Metrics
    estimated_time_hours: Optional[float] = Field(
        None, 
        description="Estimated implementation time"
    )
    confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Recommendation confidence"
    )


class CodeExample(BaseModel):
    """Code example showing before/after states."""
    
    title: str = Field(..., description="Example title")
    before_code: str = Field(..., description="Code before improvement")
    after_code: str = Field(..., description="Code after improvement")
    explanation: str = Field(..., description="Explanation of the change")
    language: str = Field(..., description="Programming language")
    
    @validator('before_code', 'after_code')
    def validate_code_content(cls, v):
        """Validate code content is not empty."""
        if not v.strip():
            raise ValueError("Code content cannot be empty")
        return v


class DetectedPattern(BaseModel):
    """Comprehensive model for detected patterns."""
    
    # Basic identification
    pattern_id: str = Field(..., description="Unique pattern identifier")
    pattern_name: str = Field(..., description="Human-readable pattern name")
    pattern_type: PatternType = Field(..., description="Type of pattern")
    pattern_category: PatternCategory = Field(..., description="Pattern category")
    
    # Description and context
    description: str = Field(..., description="Pattern description")
    summary: str = Field(..., description="Brief pattern summary")
    
    # Severity and confidence
    severity: SeverityLevel = Field(..., description="Severity level")
    confidence_metrics: ConfidenceMetrics = Field(..., description="Comprehensive confidence metrics")
    
    # Location information
    locations: List[PatternLocation] = Field(
        ..., 
        description="Pattern locations (can span multiple locations)"
    )
    
    # Detection details
    detection_method: DetectionType = Field(..., description="Detection method used")
    model_version: Optional[str] = Field(None, description="Model version used")
    
    # Code information
    code_snippet: Optional[str] = Field(None, description="Relevant code snippet")
    affected_components: List[str] = Field(
        default_factory=list,
        description="Affected components/modules"
    )
    
    # Explanation and recommendations
    explanation: PatternExplanation = Field(..., description="Detailed explanation")
    recommendations: List[PatternRecommendation] = Field(
        default_factory=list,
        description="Improvement recommendations"
    )
    
    # Impact and metrics
    impact_assessment: Dict[str, Any] = Field(
        default_factory=dict,
        description="Impact assessment metrics"
    )
    
    # Related patterns
    related_patterns: List[str] = Field(
        default_factory=list,
        description="Related pattern IDs"
    )
    similar_patterns: List[str] = Field(
        default_factory=list,
        description="Similar patterns found"
    )
    
    # Metadata
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    tags: List[str] = Field(
        default_factory=list,
        description="Pattern tags"
    )
    
    # Timestamps
    detected_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    
    # Validation and feedback
    validation_status: Optional[str] = Field(None, description="Validation status")
    feedback_score: Optional[float] = Field(None, description="User feedback score")
    
    @property
    def confidence_score(self) -> float:
        """Get overall confidence score."""
        return self.confidence_metrics.overall_confidence
    
    @property
    def primary_location(self) -> PatternLocation:
        """Get primary location."""
        return self.locations[0] if self.locations else None
    
    @property
    def is_high_confidence(self) -> bool:
        """Check if pattern has high confidence."""
        return self.confidence_metrics.confidence_level in [
            ConfidenceLevel.HIGH, 
            ConfidenceLevel.VERY_HIGH
        ]
    
    @property
    def is_critical(self) -> bool:
        """Check if pattern is critical severity."""
        return self.severity == SeverityLevel.CRITICAL
    
    @validator('locations')
    def validate_locations(cls, v):
        """Validate that at least one location is provided."""
        if not v:
            raise ValueError("At least one location must be provided")
        return v


# Alias for backward compatibility
PatternResult = DetectedPattern


class PatternDetectionResponse(BaseModel):
    """Enhanced response model for pattern detection."""
    
    job_id: str = Field(..., description="Job identifier")
    patterns: List[DetectedPattern] = Field(default_factory=list, description="Detected patterns")
    summary: Optional[Dict[str, Any]] = Field(None, description="Detection summary")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    
    # Enhanced response fields
    model_performance: Dict[str, Any] = Field(
        default_factory=dict,
        description="Model performance metrics"
    )
    quality_metrics: Dict[str, float] = Field(
        default_factory=dict,
        description="Quality assessment metrics"
    )
    
    @property
    def pattern_count(self) -> int:
        """Get total number of detected patterns."""
        return len(self.patterns)
    
    @property
    def critical_patterns(self) -> List[DetectedPattern]:
        """Get critical severity patterns."""
        return [p for p in self.patterns if p.severity == SeverityLevel.CRITICAL]
    
    @property
    def high_severity_patterns(self) -> List[DetectedPattern]:
        """Get high severity patterns."""
        return [p for p in self.patterns if p.severity == SeverityLevel.HIGH]
    
    @property
    def high_confidence_patterns(self) -> List[DetectedPattern]:
        """Get high confidence patterns."""
        return [p for p in self.patterns if p.is_high_confidence]
    
    @property
    def patterns_by_category(self) -> Dict[PatternCategory, List[DetectedPattern]]:
        """Get patterns grouped by category."""
        by_category = {}
        for pattern in self.patterns:
            category = pattern.pattern_category
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(pattern)
        return by_category


class PatternAnalysisJob(JobStatus):
    """Enhanced pattern analysis job model."""
    
    request: Optional[PatternDetectionRequest] = Field(None, description="Original request")
    results: List[DetectedPattern] = Field(default_factory=list, description="Analysis results")
    metrics: Optional[Dict[str, Any]] = Field(None, description="Analysis metrics")
    
    # Enhanced job fields
    performance_metrics: Dict[str, Any] = Field(
        default_factory=dict,
        description="Performance metrics"
    )
    resource_usage: Dict[str, Any] = Field(
        default_factory=dict,
        description="Resource usage metrics"
    )
    
    @property
    def is_completed(self) -> bool:
        """Check if the job is completed."""
        return self.status == "completed"
    
    @property
    def is_failed(self) -> bool:
        """Check if the job failed."""
        return self.status == "failed"
    
    @property
    def critical_patterns_count(self) -> int:
        """Get count of critical patterns."""
        return sum(1 for p in self.results if p.is_critical)


class PatternStatistics(BaseModel):
    """Enhanced pattern statistics model."""
    
    total_patterns: int = Field(..., description="Total number of patterns")
    by_type: Dict[PatternType, int] = Field(default_factory=dict, description="Patterns by type")
    by_severity: Dict[SeverityLevel, int] = Field(default_factory=dict, description="Patterns by severity")
    by_detection_method: Dict[DetectionType, int] = Field(default_factory=dict, description="Patterns by detection method")
    by_category: Dict[PatternCategory, int] = Field(default_factory=dict, description="Patterns by category")
    by_confidence_level: Dict[ConfidenceLevel, int] = Field(default_factory=dict, description="Patterns by confidence level")
    
    # Enhanced statistics
    average_confidence: float = Field(..., ge=0.0, le=1.0, description="Average confidence score")
    median_confidence: float = Field(..., ge=0.0, le=1.0, description="Median confidence score")
    confidence_std: float = Field(..., ge=0.0, description="Confidence standard deviation")
    
    processing_time: float = Field(..., description="Total processing time")
    patterns_per_second: float = Field(..., description="Processing rate")
    
    # Quality metrics
    high_confidence_ratio: float = Field(..., ge=0.0, le=1.0, description="High confidence patterns ratio")
    critical_patterns_ratio: float = Field(..., ge=0.0, le=1.0, description="Critical patterns ratio")
    
    @classmethod
    def from_patterns(cls, patterns: List[DetectedPattern], processing_time: float) -> 'PatternStatistics':
        """Create statistics from pattern results."""
        if not patterns:
            return cls(
                total_patterns=0,
                average_confidence=0.0,
                median_confidence=0.0,
                confidence_std=0.0,
                processing_time=processing_time,
                patterns_per_second=0.0,
                high_confidence_ratio=0.0,
                critical_patterns_ratio=0.0
            )
        
        # Count by various dimensions
        by_type = {}
        by_severity = {}
        by_detection_method = {}
        by_category = {}
        by_confidence_level = {}
        
        confidences = []
        critical_count = 0
        high_confidence_count = 0
        
        for pattern in patterns:
            # Basic counts
            by_type[pattern.pattern_type] = by_type.get(pattern.pattern_type, 0) + 1
            by_severity[pattern.severity] = by_severity.get(pattern.severity, 0) + 1
            by_detection_method[pattern.detection_method] = by_detection_method.get(pattern.detection_method, 0) + 1
            by_category[pattern.pattern_category] = by_category.get(pattern.pattern_category, 0) + 1
            by_confidence_level[pattern.confidence_metrics.confidence_level] = by_confidence_level.get(pattern.confidence_metrics.confidence_level, 0) + 1
            
            # Confidence tracking
            confidences.append(pattern.confidence_score)
            
            # Special counts
            if pattern.is_critical:
                critical_count += 1
            if pattern.is_high_confidence:
                high_confidence_count += 1
        
        # Calculate statistics
        avg_confidence = sum(confidences) / len(confidences)
        median_confidence = sorted(confidences)[len(confidences) // 2]
        confidence_std = (sum((c - avg_confidence) ** 2 for c in confidences) / len(confidences)) ** 0.5
        
        patterns_per_second = len(patterns) / processing_time if processing_time > 0 else 0
        high_confidence_ratio = high_confidence_count / len(patterns)
        critical_patterns_ratio = critical_count / len(patterns)
        
        return cls(
            total_patterns=len(patterns),
            by_type=by_type,
            by_severity=by_severity,
            by_detection_method=by_detection_method,
            by_category=by_category,
            by_confidence_level=by_confidence_level,
            average_confidence=avg_confidence,
            median_confidence=median_confidence,
            confidence_std=confidence_std,
            processing_time=processing_time,
            patterns_per_second=patterns_per_second,
            high_confidence_ratio=high_confidence_ratio,
            critical_patterns_ratio=critical_patterns_ratio
        )


class PatternFeedback(BaseModel):
    """Model for pattern feedback and validation."""
    
    pattern_id: str = Field(..., description="Pattern identifier")
    user_id: str = Field(..., description="User providing feedback")
    feedback_type: str = Field(..., description="Type of feedback (validation, correction, etc.)")
    
    # Feedback content
    is_correct: bool = Field(..., description="Whether the pattern detection is correct")
    confidence_rating: Optional[float] = Field(None, ge=0.0, le=1.0, description="User confidence rating")
    severity_rating: Optional[SeverityLevel] = Field(None, description="User severity assessment")
    
    # Comments and corrections
    comments: Optional[str] = Field(None, description="User comments")
    suggested_corrections: Optional[Dict[str, Any]] = Field(None, description="Suggested corrections")
    
    # Metadata
    feedback_at: datetime = Field(default_factory=datetime.utcnow)
    context: Dict[str, Any] = Field(default_factory=dict, description="Feedback context")


class PatternTemplate(BaseModel):
    """Template for pattern definitions."""
    
    template_id: str = Field(..., description="Template identifier")
    pattern_type: PatternType = Field(..., description="Pattern type")
    pattern_category: PatternCategory = Field(..., description="Pattern category")
    
    # Template definition
    name: str = Field(..., description="Pattern name")
    description: str = Field(..., description="Pattern description")
    
    # Detection criteria
    detection_criteria: Dict[str, Any] = Field(..., description="Detection criteria")
    feature_requirements: List[str] = Field(default_factory=list, description="Required features")
    
    # Thresholds
    confidence_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Confidence threshold")
    severity_mapping: Dict[str, SeverityLevel] = Field(default_factory=dict, description="Severity mapping")
    
    # Documentation
    examples: List[CodeExample] = Field(default_factory=list, description="Pattern examples")
    references: List[str] = Field(default_factory=list, description="Reference documentation")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    version: str = Field(default="1.0", description="Template version")
    languages: List[str] = Field(default_factory=list, description="Supported languages")


# Model rebuilding for forward references
PatternRecommendation.model_rebuild()
DetectedPattern.model_rebuild()
PatternDetectionResponse.model_rebuild()
PatternAnalysisJob.model_rebuild()
PatternStatistics.model_rebuild()