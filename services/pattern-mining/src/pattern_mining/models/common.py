"""
Common Models

Shared Pydantic models for error handling, pagination, validation, and common utilities.
These models are used across the entire pattern-mining service.
"""

from pydantic import Field, validator, root_validator
from typing import List, Dict, Any, Optional, Union, Generic, TypeVar, Callable
from datetime import datetime
from enum import Enum
from uuid import UUID, uuid4

from .base import BaseModel, TimestampedModel

T = TypeVar('T')


class ErrorCode(str, Enum):
    """Standard error codes for the pattern mining service."""
    
    # Validation errors
    INVALID_INPUT = "INVALID_INPUT"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_FORMAT = "INVALID_FORMAT"
    
    # Authentication and authorization
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    INVALID_TOKEN = "INVALID_TOKEN"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    
    # Resource errors
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS"
    RESOURCE_CONFLICT = "RESOURCE_CONFLICT"
    RESOURCE_LOCKED = "RESOURCE_LOCKED"
    
    # Processing errors
    PROCESSING_ERROR = "PROCESSING_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"
    
    # ML/AI specific errors
    MODEL_NOT_FOUND = "MODEL_NOT_FOUND"
    MODEL_LOADING_ERROR = "MODEL_LOADING_ERROR"
    FEATURE_EXTRACTION_ERROR = "FEATURE_EXTRACTION_ERROR"
    INFERENCE_ERROR = "INFERENCE_ERROR"
    TRAINING_ERROR = "TRAINING_ERROR"
    
    # Database errors
    DATABASE_ERROR = "DATABASE_ERROR"
    CONNECTION_ERROR = "CONNECTION_ERROR"
    QUERY_ERROR = "QUERY_ERROR"
    TRANSACTION_ERROR = "TRANSACTION_ERROR"
    
    # External service errors
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    BIGQUERY_ERROR = "BIGQUERY_ERROR"
    STORAGE_ERROR = "STORAGE_ERROR"
    
    # System errors
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorResponse(BaseModel):
    """Enhanced error response model."""
    
    # Basic error information
    error: str = Field(..., description="Error message")
    error_code: ErrorCode = Field(..., description="Standardized error code")
    error_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique error identifier")
    
    # Error details
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    field_errors: Optional[Dict[str, List[str]]] = Field(None, description="Field-specific errors")
    
    # Context information
    request_id: Optional[str] = Field(None, description="Request ID for tracing")
    user_id: Optional[str] = Field(None, description="User ID if applicable")
    
    # Error metadata
    severity: ErrorSeverity = Field(default=ErrorSeverity.MEDIUM, description="Error severity")
    category: str = Field(..., description="Error category")
    
    # Debugging information
    stack_trace: Optional[str] = Field(None, description="Stack trace for debugging")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    
    # Suggestions
    suggestions: List[str] = Field(default_factory=list, description="Error resolution suggestions")
    documentation_url: Optional[str] = Field(None, description="Relevant documentation URL")
    
    # Timing
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Retry information
    retryable: bool = Field(default=False, description="Whether the request can be retried")
    retry_after_seconds: Optional[int] = Field(None, description="Retry delay in seconds")
    
    @classmethod
    def create_validation_error(
        cls,
        message: str,
        field_errors: Optional[Dict[str, List[str]]] = None,
        request_id: Optional[str] = None
    ) -> 'ErrorResponse':
        """Create a validation error response."""
        return cls(
            error=message,
            error_code=ErrorCode.VALIDATION_ERROR,
            category="validation",
            field_errors=field_errors,
            request_id=request_id,
            severity=ErrorSeverity.LOW,
            retryable=False,
            suggestions=["Check input parameters and format", "Validate required fields"]
        )
    
    @classmethod
    def create_not_found_error(
        cls,
        resource_type: str,
        resource_id: str,
        request_id: Optional[str] = None
    ) -> 'ErrorResponse':
        """Create a not found error response."""
        return cls(
            error=f"{resource_type} with ID '{resource_id}' not found",
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            category="resource",
            details={"resource_type": resource_type, "resource_id": resource_id},
            request_id=request_id,
            severity=ErrorSeverity.LOW,
            retryable=False,
            suggestions=[f"Verify {resource_type} ID is correct", "Check if resource exists"]
        )
    
    @classmethod
    def create_processing_error(
        cls,
        message: str,
        error_code: ErrorCode = ErrorCode.PROCESSING_ERROR,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None,
        retryable: bool = True
    ) -> 'ErrorResponse':
        """Create a processing error response."""
        return cls(
            error=message,
            error_code=error_code,
            category="processing",
            details=details,
            request_id=request_id,
            severity=ErrorSeverity.MEDIUM,
            retryable=retryable,
            retry_after_seconds=30 if retryable else None,
            suggestions=["Retry the request", "Check system status"]
        )


class SuccessResponse(BaseModel):
    """Enhanced success response model."""
    
    # Success information
    message: str = Field(..., description="Success message")
    success: bool = Field(default=True, description="Success indicator")
    
    # Response data
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    
    # Metadata
    request_id: Optional[str] = Field(None, description="Request ID for tracing")
    processing_time_ms: Optional[int] = Field(None, description="Processing time")
    
    # Timing
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Additional context
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    
    @classmethod
    def create(
        cls,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None,
        processing_time_ms: Optional[int] = None
    ) -> 'SuccessResponse':
        """Create a success response."""
        return cls(
            message=message,
            data=data,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )


class ValidationError(BaseModel):
    """Individual validation error."""
    
    field: str = Field(..., description="Field name")
    message: str = Field(..., description="Error message")
    code: str = Field(..., description="Error code")
    value: Optional[Any] = Field(None, description="Invalid value")
    
    @classmethod
    def create(cls, field: str, message: str, code: str, value: Any = None) -> 'ValidationError':
        """Create a validation error."""
        return cls(field=field, message=message, code=code, value=value)


class PaginationParams(BaseModel):
    """Pagination parameters."""
    
    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    page_size: int = Field(default=20, ge=1, le=1000, description="Items per page")
    
    # Sorting
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field(default="desc", description="Sort order (asc/desc)")
    
    # Filtering
    filters: Optional[Dict[str, Any]] = Field(None, description="Filter parameters")
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size
    
    @property
    def limit(self) -> int:
        """Get limit for database queries."""
        return self.page_size
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        """Validate sort order."""
        if v.lower() not in ['asc', 'desc']:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v.lower()


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response model."""
    
    # Data
    items: List[T] = Field(..., description="List of items")
    
    # Pagination metadata
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    total: int = Field(..., description="Total number of items")
    total_pages: int = Field(..., description="Total number of pages")
    
    # Navigation
    has_next: bool = Field(..., description="Has next page")
    has_previous: bool = Field(..., description="Has previous page")
    next_page: Optional[int] = Field(None, description="Next page number")
    previous_page: Optional[int] = Field(None, description="Previous page number")
    
    # Additional metadata
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field(default="desc", description="Sort order")
    filters: Optional[Dict[str, Any]] = Field(None, description="Applied filters")
    
    # Response metadata
    request_id: Optional[str] = Field(None, description="Request ID")
    processing_time_ms: Optional[int] = Field(None, description="Processing time")
    
    @classmethod
    def create(
        cls,
        items: List[T],
        pagination: PaginationParams,
        total: int,
        request_id: Optional[str] = None,
        processing_time_ms: Optional[int] = None
    ) -> 'PaginatedResponse[T]':
        """Create a paginated response."""
        total_pages = (total + pagination.page_size - 1) // pagination.page_size
        has_next = pagination.page < total_pages
        has_previous = pagination.page > 1
        
        return cls(
            items=items,
            page=pagination.page,
            page_size=pagination.page_size,
            total=total,
            total_pages=total_pages,
            has_next=has_next,
            has_previous=has_previous,
            next_page=pagination.page + 1 if has_next else None,
            previous_page=pagination.page - 1 if has_previous else None,
            sort_by=pagination.sort_by,
            sort_order=pagination.sort_order,
            filters=pagination.filters,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )


class CursorPaginationParams(BaseModel):
    """Cursor-based pagination parameters."""
    
    cursor: Optional[str] = Field(None, description="Cursor for pagination")
    limit: int = Field(default=20, ge=1, le=1000, description="Maximum items to return")
    
    # Sorting
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field(default="desc", description="Sort order (asc/desc)")
    
    # Filtering
    filters: Optional[Dict[str, Any]] = Field(None, description="Filter parameters")
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        """Validate sort order."""
        if v.lower() not in ['asc', 'desc']:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v.lower()


class CursorPaginatedResponse(BaseModel, Generic[T]):
    """Cursor-based paginated response model."""
    
    # Data
    items: List[T] = Field(..., description="List of items")
    
    # Cursor information
    next_cursor: Optional[str] = Field(None, description="Next cursor")
    previous_cursor: Optional[str] = Field(None, description="Previous cursor")
    
    # Navigation
    has_next: bool = Field(..., description="Has next page")
    has_previous: bool = Field(..., description="Has previous page")
    
    # Metadata
    count: int = Field(..., description="Number of items in this response")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field(default="desc", description="Sort order")
    filters: Optional[Dict[str, Any]] = Field(None, description="Applied filters")
    
    # Response metadata
    request_id: Optional[str] = Field(None, description="Request ID")
    processing_time_ms: Optional[int] = Field(None, description="Processing time")
    
    @classmethod
    def create(
        cls,
        items: List[T],
        next_cursor: Optional[str] = None,
        previous_cursor: Optional[str] = None,
        has_next: bool = False,
        has_previous: bool = False,
        pagination: Optional[CursorPaginationParams] = None,
        request_id: Optional[str] = None,
        processing_time_ms: Optional[int] = None
    ) -> 'CursorPaginatedResponse[T]':
        """Create a cursor-paginated response."""
        return cls(
            items=items,
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
            has_next=has_next,
            has_previous=has_previous,
            count=len(items),
            sort_by=pagination.sort_by if pagination else None,
            sort_order=pagination.sort_order if pagination else "desc",
            filters=pagination.filters if pagination else None,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )


class SearchParams(BaseModel):
    """Search parameters."""
    
    # Query
    query: str = Field(..., description="Search query")
    
    # Search options
    search_fields: Optional[List[str]] = Field(None, description="Fields to search in")
    exact_match: bool = Field(default=False, description="Exact match only")
    case_sensitive: bool = Field(default=False, description="Case sensitive search")
    
    # Filtering
    filters: Optional[Dict[str, Any]] = Field(None, description="Search filters")
    
    # Pagination
    pagination: PaginationParams = Field(default_factory=PaginationParams)
    
    @validator('query')
    def validate_query(cls, v):
        """Validate search query."""
        if not v.strip():
            raise ValueError("Search query cannot be empty")
        if len(v) > 1000:
            raise ValueError("Search query too long (max 1000 characters)")
        return v.strip()


class SearchResponse(BaseModel, Generic[T]):
    """Search response model."""
    
    # Search results
    results: List[T] = Field(..., description="Search results")
    
    # Search metadata
    query: str = Field(..., description="Original search query")
    total_results: int = Field(..., description="Total number of results")
    search_time_ms: int = Field(..., description="Search time in milliseconds")
    
    # Pagination
    pagination: PaginatedResponse[T] = Field(..., description="Paginated results")
    
    # Search suggestions
    suggestions: List[str] = Field(default_factory=list, description="Search suggestions")
    corrections: List[str] = Field(default_factory=list, description="Query corrections")
    
    # Facets/aggregations
    facets: Optional[Dict[str, Dict[str, int]]] = Field(None, description="Search facets")
    
    # Response metadata
    request_id: Optional[str] = Field(None, description="Request ID")
    
    @classmethod
    def create(
        cls,
        results: List[T],
        query: str,
        pagination: PaginatedResponse[T],
        search_time_ms: int,
        suggestions: List[str] = None,
        corrections: List[str] = None,
        facets: Optional[Dict[str, Dict[str, int]]] = None,
        request_id: Optional[str] = None
    ) -> 'SearchResponse[T]':
        """Create a search response."""
        return cls(
            results=results,
            query=query,
            total_results=pagination.total,
            search_time_ms=search_time_ms,
            pagination=pagination,
            suggestions=suggestions or [],
            corrections=corrections or [],
            facets=facets,
            request_id=request_id
        )


class HealthStatus(str, Enum):
    """Health status enumeration."""
    
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class HealthCheck(BaseModel):
    """Health check model."""
    
    # Basic health info
    status: HealthStatus = Field(..., description="Overall health status")
    version: str = Field(..., description="Service version")
    
    # Detailed checks
    checks: Dict[str, 'ComponentHealth'] = Field(
        default_factory=dict,
        description="Individual component health checks"
    )
    
    # Timing
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    response_time_ms: int = Field(..., description="Health check response time")
    
    # System info
    uptime_seconds: int = Field(..., description="Service uptime in seconds")
    memory_usage_mb: Optional[float] = Field(None, description="Memory usage in MB")
    cpu_usage_percent: Optional[float] = Field(None, description="CPU usage percentage")
    
    # Service-specific metrics
    active_connections: Optional[int] = Field(None, description="Active connections")
    processed_requests: Optional[int] = Field(None, description="Processed requests")
    
    @property
    def is_healthy(self) -> bool:
        """Check if service is healthy."""
        return self.status == HealthStatus.HEALTHY
    
    @property
    def failed_checks(self) -> List[str]:
        """Get list of failed health checks."""
        return [
            name for name, check in self.checks.items()
            if check.status != HealthStatus.HEALTHY
        ]


class ComponentHealth(BaseModel):
    """Individual component health check."""
    
    # Component info
    name: str = Field(..., description="Component name")
    status: HealthStatus = Field(..., description="Component health status")
    
    # Details
    message: Optional[str] = Field(None, description="Health check message")
    error: Optional[str] = Field(None, description="Error message if unhealthy")
    
    # Metrics
    response_time_ms: Optional[int] = Field(None, description="Component response time")
    last_check: datetime = Field(default_factory=datetime.utcnow)
    
    # Component-specific data
    details: Optional[Dict[str, Any]] = Field(None, description="Component-specific details")
    
    @classmethod
    def create_healthy(
        cls,
        name: str,
        message: Optional[str] = None,
        response_time_ms: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> 'ComponentHealth':
        """Create a healthy component check."""
        return cls(
            name=name,
            status=HealthStatus.HEALTHY,
            message=message or f"{name} is healthy",
            response_time_ms=response_time_ms,
            details=details
        )
    
    @classmethod
    def create_unhealthy(
        cls,
        name: str,
        error: str,
        response_time_ms: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> 'ComponentHealth':
        """Create an unhealthy component check."""
        return cls(
            name=name,
            status=HealthStatus.UNHEALTHY,
            message=f"{name} is unhealthy",
            error=error,
            response_time_ms=response_time_ms,
            details=details
        )


class APIResponse(BaseModel, Generic[T]):
    """Generic API response wrapper."""
    
    # Response status
    success: bool = Field(..., description="Response success indicator")
    
    # Response data
    data: Optional[T] = Field(None, description="Response data")
    error: Optional[ErrorResponse] = Field(None, description="Error details")
    
    # Metadata
    request_id: Optional[str] = Field(None, description="Request ID")
    processing_time_ms: Optional[int] = Field(None, description="Processing time")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # API metadata
    version: str = Field(default="1.0", description="API version")
    
    @classmethod
    def success_response(
        cls,
        data: T,
        request_id: Optional[str] = None,
        processing_time_ms: Optional[int] = None
    ) -> 'APIResponse[T]':
        """Create a successful API response."""
        return cls(
            success=True,
            data=data,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )
    
    @classmethod
    def error_response(
        cls,
        error: ErrorResponse,
        request_id: Optional[str] = None,
        processing_time_ms: Optional[int] = None
    ) -> 'APIResponse[T]':
        """Create an error API response."""
        return cls(
            success=False,
            error=error,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )


class RateLimitInfo(BaseModel):
    """Rate limiting information."""
    
    # Rate limit configuration
    limit: int = Field(..., description="Request limit")
    remaining: int = Field(..., description="Remaining requests")
    reset_time: datetime = Field(..., description="Rate limit reset time")
    
    # Request metadata
    current_requests: int = Field(..., description="Current request count")
    window_seconds: int = Field(..., description="Rate limit window in seconds")
    
    # Status
    is_limited: bool = Field(..., description="Whether rate limit is exceeded")
    
    @property
    def reset_in_seconds(self) -> int:
        """Get seconds until rate limit resets."""
        return max(0, int((self.reset_time - datetime.utcnow()).total_seconds()))
    
    @property
    def usage_percentage(self) -> float:
        """Get usage percentage."""
        return (self.current_requests / self.limit) * 100 if self.limit > 0 else 0


class CacheInfo(BaseModel):
    """Cache information."""
    
    # Cache status
    hit: bool = Field(..., description="Cache hit")
    key: str = Field(..., description="Cache key")
    
    # Cache metadata
    ttl_seconds: Optional[int] = Field(None, description="Time to live in seconds")
    created_at: Optional[datetime] = Field(None, description="Cache entry creation time")
    
    # Cache statistics
    hit_count: Optional[int] = Field(None, description="Cache hit count")
    miss_count: Optional[int] = Field(None, description="Cache miss count")
    
    @property
    def hit_ratio(self) -> Optional[float]:
        """Calculate cache hit ratio."""
        if self.hit_count is not None and self.miss_count is not None:
            total = self.hit_count + self.miss_count
            return self.hit_count / total if total > 0 else None
        return None


# Model rebuilding for forward references
ComponentHealth.model_rebuild()
HealthCheck.model_rebuild()
PaginatedResponse.model_rebuild()
CursorPaginatedResponse.model_rebuild()
SearchResponse.model_rebuild()
APIResponse.model_rebuild()