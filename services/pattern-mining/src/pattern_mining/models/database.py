"""
Database Models

Pydantic models for database operations, BigQuery schemas, and data persistence.
Optimized for BigQuery storage and analytics with proper field types and constraints.
"""

from pydantic import Field, validator, root_validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, date
from enum import Enum
from decimal import Decimal

from .base import BaseModel, TimestampedModel
from .patterns import PatternType, SeverityLevel, DetectionType, PatternCategory, ConfidenceLevel


class BigQueryFieldType(str, Enum):
    """BigQuery field types."""
    
    STRING = "STRING"
    INTEGER = "INTEGER"
    FLOAT = "FLOAT"
    BOOLEAN = "BOOLEAN"
    TIMESTAMP = "TIMESTAMP"
    DATE = "DATE"
    DATETIME = "DATETIME"
    NUMERIC = "NUMERIC"
    JSON = "JSON"
    RECORD = "RECORD"
    REPEATED = "REPEATED"


class BigQueryFieldMode(str, Enum):
    """BigQuery field modes."""
    
    NULLABLE = "NULLABLE"
    REQUIRED = "REQUIRED"
    REPEATED = "REPEATED"


class BigQuerySchema(BaseModel):
    """BigQuery table schema definition."""
    
    table_name: str = Field(..., description="Table name")
    dataset_name: str = Field(..., description="Dataset name")
    project_id: str = Field(..., description="Project ID")
    
    # Schema fields
    fields: List['BigQueryField'] = Field(..., description="Table fields")
    
    # Table properties
    description: Optional[str] = Field(None, description="Table description")
    clustering_fields: List[str] = Field(default_factory=list, description="Clustering fields")
    partitioning_field: Optional[str] = Field(None, description="Partitioning field")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    version: str = Field(default="1.0", description="Schema version")
    
    @property
    def fully_qualified_name(self) -> str:
        """Get fully qualified table name."""
        return f"{self.project_id}.{self.dataset_name}.{self.table_name}"
    
    @validator('table_name', 'dataset_name')
    def validate_names(cls, v):
        """Validate table and dataset names."""
        if not v.replace('_', '').isalnum():
            raise ValueError("Names must be alphanumeric with underscores")
        return v


class BigQueryField(BaseModel):
    """BigQuery field definition."""
    
    name: str = Field(..., description="Field name")
    type: BigQueryFieldType = Field(..., description="Field type")
    mode: BigQueryFieldMode = Field(default=BigQueryFieldMode.NULLABLE, description="Field mode")
    description: Optional[str] = Field(None, description="Field description")
    
    # For RECORD types
    fields: Optional[List['BigQueryField']] = Field(None, description="Nested fields")
    
    @validator('name')
    def validate_field_name(cls, v):
        """Validate field name."""
        if not v.replace('_', '').isalnum():
            raise ValueError("Field name must be alphanumeric with underscores")
        return v


class RepositoryRecord(BaseModel):
    """Repository record for BigQuery storage."""
    
    # Primary identifiers
    repository_id: str = Field(..., description="Repository identifier")
    repository_url: str = Field(..., description="Repository URL")
    repository_name: str = Field(..., description="Repository name")
    
    # Repository metadata
    owner: str = Field(..., description="Repository owner")
    language: str = Field(..., description="Primary language")
    languages: List[str] = Field(default_factory=list, description="All languages")
    
    # Repository statistics
    stars: int = Field(default=0, description="Star count")
    forks: int = Field(default=0, description="Fork count")
    size_kb: int = Field(default=0, description="Repository size in KB")
    
    # Analysis metadata
    last_analyzed: datetime = Field(default_factory=datetime.utcnow)
    analysis_count: int = Field(default=0, description="Number of analyses")
    
    # Partitioning field for BigQuery
    analysis_date: date = Field(default_factory=date.today, description="Analysis date for partitioning")
    
    @validator('repository_url')
    def validate_url(cls, v):
        """Validate repository URL."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError("Repository URL must start with http:// or https://")
        return v


class PatternDetectionRecord(BaseModel):
    """Pattern detection record for BigQuery storage."""
    
    # Detection metadata
    detection_id: str = Field(..., description="Detection identifier")
    repository_id: str = Field(..., description="Repository identifier")
    file_path: str = Field(..., description="File path")
    
    # Pattern information
    pattern_id: str = Field(..., description="Pattern identifier")
    pattern_name: str = Field(..., description="Pattern name")
    pattern_type: PatternType = Field(..., description="Pattern type")
    pattern_category: PatternCategory = Field(..., description="Pattern category")
    
    # Detection results
    severity: SeverityLevel = Field(..., description="Severity level")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    confidence_level: ConfidenceLevel = Field(..., description="Confidence level")
    
    # Location information
    line_start: int = Field(..., description="Starting line")
    line_end: int = Field(..., description="Ending line")
    column_start: Optional[int] = Field(None, description="Starting column")
    column_end: Optional[int] = Field(None, description="Ending column")
    
    # Context
    function_name: Optional[str] = Field(None, description="Function name")
    class_name: Optional[str] = Field(None, description="Class name")
    module_name: Optional[str] = Field(None, description="Module name")
    
    # Detection metadata
    detection_method: DetectionType = Field(..., description="Detection method")
    model_version: Optional[str] = Field(None, description="Model version")
    language: str = Field(..., description="Programming language")
    
    # Code metrics
    lines_of_code: int = Field(default=0, description="Lines of code")
    cyclomatic_complexity: float = Field(default=0.0, description="Cyclomatic complexity")
    
    # Processing metadata
    processing_time_ms: int = Field(default=0, description="Processing time")
    detected_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Partitioning field for BigQuery
    detection_date: date = Field(default_factory=date.today, description="Detection date for partitioning")
    
    # Validation and feedback
    validation_status: Optional[str] = Field(None, description="Validation status")
    feedback_score: Optional[float] = Field(None, description="Feedback score")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        """Validate confidence score."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence must be between 0.0 and 1.0")
        return v


class FeatureExtractionRecord(BaseModel):
    """Feature extraction record for BigQuery storage."""
    
    # Extraction metadata
    extraction_id: str = Field(..., description="Extraction identifier")
    repository_id: str = Field(..., description="Repository identifier")
    file_path: str = Field(..., description="File path")
    language: str = Field(..., description="Programming language")
    
    # Feature vectors (stored as JSON in BigQuery)
    structural_features: List[float] = Field(..., description="Structural features")
    lexical_features: List[float] = Field(..., description="Lexical features")
    semantic_features: List[float] = Field(..., description="Semantic features")
    statistical_features: List[float] = Field(..., description="Statistical features")
    
    # Feature metadata
    feature_count: int = Field(..., description="Total feature count")
    feature_version: str = Field(..., description="Feature extractor version")
    
    # Quality metrics
    feature_quality_score: float = Field(..., ge=0.0, le=1.0, description="Feature quality")
    extraction_success: bool = Field(..., description="Extraction success")
    
    # Code metrics
    lines_of_code: int = Field(..., description="Lines of code")
    ast_depth: int = Field(..., description="AST depth")
    node_count: int = Field(..., description="AST node count")
    
    # Processing metadata
    extraction_time_ms: int = Field(..., description="Extraction time")
    extracted_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Partitioning field for BigQuery
    extraction_date: date = Field(default_factory=date.today, description="Extraction date for partitioning")
    
    @validator('structural_features', 'lexical_features', 'semantic_features', 'statistical_features')
    def validate_features(cls, v):
        """Validate feature vectors."""
        if not isinstance(v, list):
            raise ValueError("Features must be a list")
        if not all(isinstance(x, (int, float)) for x in v):
            raise ValueError("All features must be numeric")
        return v


class ModelTrainingRecord(BaseModel):
    """Model training record for BigQuery storage."""
    
    # Training metadata
    training_id: str = Field(..., description="Training identifier")
    model_id: str = Field(..., description="Model identifier")
    model_name: str = Field(..., description="Model name")
    model_type: str = Field(..., description="Model type")
    
    # Training configuration
    training_data_size: int = Field(..., description="Training data size")
    validation_data_size: int = Field(..., description="Validation data size")
    test_data_size: int = Field(..., description="Test data size")
    
    # Hyperparameters (stored as JSON)
    hyperparameters: Dict[str, Any] = Field(default_factory=dict, description="Hyperparameters")
    
    # Training results
    training_accuracy: float = Field(..., ge=0.0, le=1.0, description="Training accuracy")
    validation_accuracy: float = Field(..., ge=0.0, le=1.0, description="Validation accuracy")
    test_accuracy: float = Field(..., ge=0.0, le=1.0, description="Test accuracy")
    
    # Additional metrics
    precision: float = Field(..., ge=0.0, le=1.0, description="Precision")
    recall: float = Field(..., ge=0.0, le=1.0, description="Recall")
    f1_score: float = Field(..., ge=0.0, le=1.0, description="F1 score")
    
    # Training metadata
    training_duration_seconds: int = Field(..., description="Training duration")
    epochs_completed: int = Field(..., description="Epochs completed")
    
    # Model artifacts
    model_size_mb: float = Field(..., description="Model size in MB")
    model_path: str = Field(..., description="Model storage path")
    
    # Timestamps
    training_started: datetime = Field(default_factory=datetime.utcnow)
    training_completed: datetime = Field(default_factory=datetime.utcnow)
    
    # Partitioning field for BigQuery
    training_date: date = Field(default_factory=date.today, description="Training date for partitioning")
    
    @validator('training_accuracy', 'validation_accuracy', 'test_accuracy', 'precision', 'recall', 'f1_score')
    def validate_metrics(cls, v):
        """Validate metric values."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Metrics must be between 0.0 and 1.0")
        return v


class ModelInferenceRecord(BaseModel):
    """Model inference record for BigQuery storage."""
    
    # Inference metadata
    inference_id: str = Field(..., description="Inference identifier")
    model_id: str = Field(..., description="Model identifier")
    model_version: str = Field(..., description="Model version")
    
    # Input metadata
    repository_id: str = Field(..., description="Repository identifier")
    file_path: str = Field(..., description="File path")
    language: str = Field(..., description="Programming language")
    
    # Inference results
    predictions: List[str] = Field(..., description="Predictions")
    confidences: List[float] = Field(..., description="Confidence scores")
    
    # Performance metrics
    inference_time_ms: int = Field(..., description="Inference time")
    feature_count: int = Field(..., description="Feature count")
    
    # Quality metrics
    average_confidence: float = Field(..., ge=0.0, le=1.0, description="Average confidence")
    prediction_quality: float = Field(..., ge=0.0, le=1.0, description="Prediction quality")
    
    # Resource usage
    memory_usage_mb: float = Field(..., description="Memory usage")
    cpu_usage_percent: float = Field(..., description="CPU usage")
    
    # Timestamps
    inference_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Partitioning field for BigQuery
    inference_date: date = Field(default_factory=date.today, description="Inference date for partitioning")
    
    @validator('confidences')
    def validate_confidences(cls, v):
        """Validate confidence scores."""
        if not all(0.0 <= c <= 1.0 for c in v):
            raise ValueError("All confidences must be between 0.0 and 1.0")
        return v


class PatternFeedbackRecord(BaseModel):
    """Pattern feedback record for BigQuery storage."""
    
    # Feedback metadata
    feedback_id: str = Field(..., description="Feedback identifier")
    pattern_id: str = Field(..., description="Pattern identifier")
    detection_id: str = Field(..., description="Detection identifier")
    
    # User information
    user_id: str = Field(..., description="User identifier")
    user_role: str = Field(..., description="User role")
    
    # Feedback content
    is_correct: bool = Field(..., description="Is detection correct")
    confidence_rating: Optional[float] = Field(None, ge=0.0, le=1.0, description="User confidence rating")
    severity_rating: Optional[SeverityLevel] = Field(None, description="User severity rating")
    
    # Feedback details
    feedback_type: str = Field(..., description="Feedback type")
    comments: Optional[str] = Field(None, description="User comments")
    
    # Context
    repository_id: str = Field(..., description="Repository identifier")
    file_path: str = Field(..., description="File path")
    language: str = Field(..., description="Programming language")
    
    # Timestamps
    feedback_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Partitioning field for BigQuery
    feedback_date: date = Field(default_factory=date.today, description="Feedback date for partitioning")


class PatternAnalyticsRecord(BaseModel):
    """Pattern analytics record for BigQuery storage."""
    
    # Analytics metadata
    analytics_id: str = Field(..., description="Analytics identifier")
    repository_id: str = Field(..., description="Repository identifier")
    
    # Time period
    period_start: datetime = Field(..., description="Period start")
    period_end: datetime = Field(..., description="Period end")
    
    # Pattern statistics
    total_patterns: int = Field(..., description="Total patterns detected")
    unique_patterns: int = Field(..., description="Unique pattern types")
    
    # Severity distribution
    critical_patterns: int = Field(default=0, description="Critical patterns")
    high_patterns: int = Field(default=0, description="High severity patterns")
    medium_patterns: int = Field(default=0, description="Medium severity patterns")
    low_patterns: int = Field(default=0, description="Low severity patterns")
    
    # Pattern type distribution
    design_patterns: int = Field(default=0, description="Design patterns")
    anti_patterns: int = Field(default=0, description="Anti-patterns")
    code_smells: int = Field(default=0, description="Code smells")
    security_issues: int = Field(default=0, description="Security issues")
    performance_issues: int = Field(default=0, description="Performance issues")
    
    # Quality metrics
    average_confidence: float = Field(..., ge=0.0, le=1.0, description="Average confidence")
    quality_score: float = Field(..., ge=0.0, le=100.0, description="Quality score")
    
    # Repository metrics
    files_analyzed: int = Field(..., description="Files analyzed")
    lines_of_code: int = Field(..., description="Lines of code")
    languages: List[str] = Field(default_factory=list, description="Languages")
    
    # Timestamps
    computed_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Partitioning field for BigQuery
    analytics_date: date = Field(default_factory=date.today, description="Analytics date for partitioning")


class BigQueryDataset(BaseModel):
    """BigQuery dataset configuration."""
    
    # Dataset information
    dataset_id: str = Field(..., description="Dataset identifier")
    project_id: str = Field(..., description="Project identifier")
    location: str = Field(default="US", description="Dataset location")
    
    # Dataset properties
    description: Optional[str] = Field(None, description="Dataset description")
    default_table_expiration_ms: Optional[int] = Field(None, description="Default table expiration")
    
    # Access control
    access_roles: List[str] = Field(default_factory=list, description="Access roles")
    
    # Labels
    labels: Dict[str, str] = Field(default_factory=dict, description="Dataset labels")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_modified: datetime = Field(default_factory=datetime.utcnow)


class BigQueryTable(BaseModel):
    """BigQuery table configuration."""
    
    # Table information
    table_id: str = Field(..., description="Table identifier")
    dataset_id: str = Field(..., description="Dataset identifier")
    project_id: str = Field(..., description="Project identifier")
    
    # Table properties
    schema: BigQuerySchema = Field(..., description="Table schema")
    description: Optional[str] = Field(None, description="Table description")
    
    # Partitioning and clustering
    time_partitioning: Optional[Dict[str, Any]] = Field(None, description="Time partitioning config")
    clustering: Optional[List[str]] = Field(None, description="Clustering fields")
    
    # Table settings
    require_partition_filter: bool = Field(default=True, description="Require partition filter")
    expiration_ms: Optional[int] = Field(None, description="Table expiration time")
    
    # Labels
    labels: Dict[str, str] = Field(default_factory=dict, description="Table labels")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_modified: datetime = Field(default_factory=datetime.utcnow)
    
    @property
    def fully_qualified_name(self) -> str:
        """Get fully qualified table name."""
        return f"{self.project_id}.{self.dataset_id}.{self.table_id}"


class BigQueryQueryRequest(BaseModel):
    """BigQuery query request."""
    
    # Query information
    query_id: str = Field(..., description="Query identifier")
    sql: str = Field(..., description="SQL query")
    
    # Query configuration
    use_legacy_sql: bool = Field(default=False, description="Use legacy SQL")
    dry_run: bool = Field(default=False, description="Dry run only")
    
    # Performance settings
    maximum_bytes_billed: Optional[int] = Field(None, description="Maximum bytes billed")
    use_query_cache: bool = Field(default=True, description="Use query cache")
    
    # Job configuration
    job_id: Optional[str] = Field(None, description="Job identifier")
    priority: str = Field(default="INTERACTIVE", description="Job priority")
    
    # Labels
    labels: Dict[str, str] = Field(default_factory=dict, description="Query labels")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    @validator('sql')
    def validate_sql(cls, v):
        """Validate SQL query."""
        if not v.strip():
            raise ValueError("SQL query cannot be empty")
        return v


class BigQueryQueryResponse(BaseModel):
    """BigQuery query response."""
    
    # Query metadata
    query_id: str = Field(..., description="Query identifier")
    job_id: str = Field(..., description="Job identifier")
    
    # Results
    rows: List[Dict[str, Any]] = Field(default_factory=list, description="Query results")
    total_rows: int = Field(default=0, description="Total rows")
    
    # Performance metrics
    bytes_processed: int = Field(default=0, description="Bytes processed")
    bytes_billed: int = Field(default=0, description="Bytes billed")
    
    # Timing
    creation_time: datetime = Field(default_factory=datetime.utcnow)
    start_time: Optional[datetime] = Field(None, description="Query start time")
    end_time: Optional[datetime] = Field(None, description="Query end time")
    
    # Status
    state: str = Field(default="PENDING", description="Query state")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    # Statistics
    slot_ms: Optional[int] = Field(None, description="Slot milliseconds")
    
    @property
    def duration_ms(self) -> Optional[int]:
        """Calculate query duration in milliseconds."""
        if self.start_time and self.end_time:
            return int((self.end_time - self.start_time).total_seconds() * 1000)
        return None
    
    @property
    def is_successful(self) -> bool:
        """Check if query was successful."""
        return self.state == "DONE" and self.error_message is None


# Schema definitions for BigQuery tables
PATTERN_MINING_SCHEMAS = {
    "repositories": BigQuerySchema(
        table_name="repositories",
        dataset_name="pattern_mining",
        project_id="vibe-match-463114",
        fields=[
            BigQueryField(name="repository_id", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="repository_url", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="repository_name", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="owner", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="language", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="languages", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REPEATED),
            BigQueryField(name="stars", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="forks", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="size_kb", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="last_analyzed", type=BigQueryFieldType.TIMESTAMP, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="analysis_count", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="analysis_date", type=BigQueryFieldType.DATE, mode=BigQueryFieldMode.REQUIRED),
        ],
        clustering_fields=["language", "owner"],
        partitioning_field="analysis_date"
    ),
    
    "pattern_detections": BigQuerySchema(
        table_name="pattern_detections",
        dataset_name="pattern_mining",
        project_id="vibe-match-463114",
        fields=[
            BigQueryField(name="detection_id", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="repository_id", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="file_path", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="pattern_id", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="pattern_name", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="pattern_type", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="pattern_category", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="severity", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="confidence", type=BigQueryFieldType.FLOAT, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="confidence_level", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="line_start", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="line_end", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="column_start", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="column_end", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="function_name", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="class_name", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="module_name", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="detection_method", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="model_version", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="language", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="lines_of_code", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="cyclomatic_complexity", type=BigQueryFieldType.FLOAT, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="processing_time_ms", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="detected_at", type=BigQueryFieldType.TIMESTAMP, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="detection_date", type=BigQueryFieldType.DATE, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="validation_status", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.NULLABLE),
            BigQueryField(name="feedback_score", type=BigQueryFieldType.FLOAT, mode=BigQueryFieldMode.NULLABLE),
        ],
        clustering_fields=["pattern_type", "severity", "language"],
        partitioning_field="detection_date"
    ),
    
    "feature_extractions": BigQuerySchema(
        table_name="feature_extractions",
        dataset_name="pattern_mining",
        project_id="vibe-match-463114",
        fields=[
            BigQueryField(name="extraction_id", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="repository_id", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="file_path", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="language", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="structural_features", type=BigQueryFieldType.JSON, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="lexical_features", type=BigQueryFieldType.JSON, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="semantic_features", type=BigQueryFieldType.JSON, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="statistical_features", type=BigQueryFieldType.JSON, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="feature_count", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="feature_version", type=BigQueryFieldType.STRING, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="feature_quality_score", type=BigQueryFieldType.FLOAT, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="extraction_success", type=BigQueryFieldType.BOOLEAN, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="lines_of_code", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="ast_depth", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="node_count", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="extraction_time_ms", type=BigQueryFieldType.INTEGER, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="extracted_at", type=BigQueryFieldType.TIMESTAMP, mode=BigQueryFieldMode.REQUIRED),
            BigQueryField(name="extraction_date", type=BigQueryFieldType.DATE, mode=BigQueryFieldMode.REQUIRED),
        ],
        clustering_fields=["language", "feature_version"],
        partitioning_field="extraction_date"
    )
}


# Model rebuilding for forward references
BigQueryField.model_rebuild()
BigQuerySchema.model_rebuild()