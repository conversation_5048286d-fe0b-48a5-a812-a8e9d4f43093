"""
Machine Learning Models

Comprehensive Pydantic models for ML operations, feature extraction, 
model inference, and training data management.
"""

from pydantic import Field, validator, root_validator
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime
from enum import Enum
import numpy as np

from .base import BaseModel, TimestampedModel, JobStatus


class ModelType(str, Enum):
    """ML model type enumeration."""
    
    CLASSIFICATION = "classification"
    REGRESSION = "regression"
    CLUSTERING = "clustering"
    TRANSFORMER = "transformer"
    NEURAL_NETWORK = "neural_network"
    ENSEMBLE = "ensemble"
    AUTOENCODER = "autoencoder"
    CNN = "cnn"
    LSTM = "lstm"
    CNN_LSTM = "cnn_lstm"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    DBSCAN = "dbscan"
    KMEANS = "kmeans"
    BIGQUERY_ML = "bigquery_ml"


class FeatureType(str, Enum):
    """Feature type enumeration."""
    
    STRUCTURAL = "structural"
    LEXICAL = "lexical"
    SEMANTIC = "semantic"
    STATISTICAL = "statistical"
    SYNTACTIC = "syntactic"
    GRAPH = "graph"
    EMBEDDING = "embedding"
    CONTEXTUAL = "contextual"


class ModelStatus(str, Enum):
    """Model status enumeration."""
    
    TRAINING = "training"
    TRAINED = "trained"
    DEPLOYED = "deployed"
    DEPRECATED = "deprecated"
    FAILED = "failed"


class TrainingStatus(str, Enum):
    """Training status enumeration."""
    
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class FeatureExtractionRequest(BaseModel):
    """Request model for feature extraction."""
    
    # Input data
    ast_data: Dict[str, Any] = Field(..., description="AST data")
    code_content: str = Field(..., description="Source code content")
    language: str = Field(..., description="Programming language")
    
    # Feature selection
    feature_types: List[FeatureType] = Field(
        default_factory=lambda: [
            FeatureType.STRUCTURAL,
            FeatureType.LEXICAL,
            FeatureType.SEMANTIC,
            FeatureType.STATISTICAL
        ],
        description="Types of features to extract"
    )
    
    # Extraction configuration
    embedding_dimension: int = Field(default=768, ge=1, le=2048, description="Embedding dimension")
    max_sequence_length: int = Field(default=512, ge=1, le=2048, description="Maximum sequence length")
    include_context: bool = Field(default=True, description="Include contextual features")
    
    # Processing options
    normalize_features: bool = Field(default=True, description="Normalize features")
    remove_outliers: bool = Field(default=False, description="Remove outliers")
    
    @validator('ast_data')
    def validate_ast_data(cls, v):
        """Validate AST data structure."""
        if not isinstance(v, dict):
            raise ValueError("AST data must be a dictionary")
        return v


class CodeFeatures(BaseModel):
    """Comprehensive code feature representation."""
    
    # Feature vectors
    structural_features: List[float] = Field(..., description="Structural features from AST")
    lexical_features: List[float] = Field(..., description="Lexical and naming features")
    semantic_features: List[float] = Field(..., description="Semantic and flow features")
    statistical_features: List[float] = Field(..., description="Statistical code metrics")
    
    # Advanced features
    embeddings: Optional[List[float]] = Field(None, description="Code embeddings")
    graph_features: Optional[List[float]] = Field(None, description="Graph-based features")
    contextual_features: Optional[List[float]] = Field(None, description="Contextual features")
    
    # Feature metadata
    feature_names: List[str] = Field(..., description="Feature names")
    feature_types: Dict[str, FeatureType] = Field(..., description="Feature type mapping")
    feature_importance: Optional[Dict[str, float]] = Field(None, description="Feature importance scores")
    
    # Quality metrics
    feature_quality: Dict[str, float] = Field(
        default_factory=dict,
        description="Feature quality metrics"
    )
    
    # Processing info
    extraction_time_ms: int = Field(..., description="Feature extraction time")
    feature_version: str = Field(default="1.0", description="Feature extraction version")
    
    @property
    def feature_count(self) -> int:
        """Get total number of features."""
        return len(self.structural_features) + len(self.lexical_features) + \
               len(self.semantic_features) + len(self.statistical_features)
    
    @property
    def combined_features(self) -> List[float]:
        """Get all features combined."""
        features = []
        features.extend(self.structural_features)
        features.extend(self.lexical_features)
        features.extend(self.semantic_features)
        features.extend(self.statistical_features)
        
        if self.embeddings:
            features.extend(self.embeddings)
        if self.graph_features:
            features.extend(self.graph_features)
        if self.contextual_features:
            features.extend(self.contextual_features)
        
        return features
    
    @validator('structural_features', 'lexical_features', 'semantic_features', 'statistical_features')
    def validate_feature_vectors(cls, v):
        """Validate feature vectors."""
        if not isinstance(v, list):
            raise ValueError("Feature vectors must be lists")
        if not all(isinstance(x, (int, float)) for x in v):
            raise ValueError("Feature vectors must contain only numeric values")
        return v


class FeatureExtractionResponse(BaseModel):
    """Response model for feature extraction."""
    
    # Extracted features
    features: CodeFeatures = Field(..., description="Extracted features")
    
    # Extraction metrics
    extraction_success: bool = Field(..., description="Whether extraction succeeded")
    extraction_time_ms: int = Field(..., description="Extraction time in milliseconds")
    
    # Quality assessment
    feature_quality_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Overall feature quality score"
    )
    
    # Metadata
    extractor_version: str = Field(..., description="Feature extractor version")
    model_requirements: Dict[str, Any] = Field(
        default_factory=dict,
        description="Model requirements for these features"
    )
    
    # Error handling
    warnings: List[str] = Field(default_factory=list, description="Extraction warnings")
    errors: List[str] = Field(default_factory=list, description="Extraction errors")


class StructuralFeatures(BaseModel):
    """Structural features from AST analysis."""
    
    # AST metrics
    ast_depth: int = Field(..., ge=0, description="AST depth")
    ast_breadth: int = Field(..., ge=0, description="AST breadth")
    node_count: int = Field(..., ge=0, description="Total node count")
    
    # Node type distribution
    node_type_counts: Dict[str, int] = Field(
        default_factory=dict,
        description="Node type counts"
    )
    
    # Structural patterns
    class_count: int = Field(default=0, ge=0, description="Number of classes")
    function_count: int = Field(default=0, ge=0, description="Number of functions")
    loop_count: int = Field(default=0, ge=0, description="Number of loops")
    conditional_count: int = Field(default=0, ge=0, description="Number of conditionals")
    
    # Complexity metrics
    cyclomatic_complexity: float = Field(default=0.0, ge=0.0, description="Cyclomatic complexity")
    max_nesting_depth: int = Field(default=0, ge=0, description="Maximum nesting depth")
    
    # Object-oriented metrics
    inheritance_depth: int = Field(default=0, ge=0, description="Inheritance depth")
    coupling_score: float = Field(default=0.0, ge=0.0, description="Coupling score")
    cohesion_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Cohesion score")


class LexicalFeatures(BaseModel):
    """Lexical and naming features."""
    
    # Identifier patterns
    camel_case_count: int = Field(default=0, ge=0, description="CamelCase identifiers")
    snake_case_count: int = Field(default=0, ge=0, description="snake_case identifiers")
    kebab_case_count: int = Field(default=0, ge=0, description="kebab-case identifiers")
    
    # Naming quality
    average_identifier_length: float = Field(default=0.0, ge=0.0, description="Average identifier length")
    vocabulary_richness: float = Field(default=0.0, ge=0.0, le=1.0, description="Vocabulary richness")
    
    # Comment analysis
    comment_density: float = Field(default=0.0, ge=0.0, le=1.0, description="Comment density")
    comment_quality_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Comment quality")
    
    # Code style
    average_line_length: float = Field(default=0.0, ge=0.0, description="Average line length")
    max_line_length: int = Field(default=0, ge=0, description="Maximum line length")
    
    # Token analysis
    token_count: int = Field(default=0, ge=0, description="Total token count")
    unique_token_count: int = Field(default=0, ge=0, description="Unique token count")
    token_diversity: float = Field(default=0.0, ge=0.0, le=1.0, description="Token diversity")


class SemanticFeatures(BaseModel):
    """Semantic and data flow features."""
    
    # Data flow
    variable_definitions: int = Field(default=0, ge=0, description="Variable definitions")
    variable_usages: int = Field(default=0, ge=0, description="Variable usages")
    data_flow_ratio: float = Field(default=0.0, ge=0.0, description="Data flow ratio")
    
    # Control flow
    sequential_blocks: int = Field(default=0, ge=0, description="Sequential blocks")
    branching_points: int = Field(default=0, ge=0, description="Branching points")
    loop_nesting: int = Field(default=0, ge=0, description="Loop nesting level")
    
    # Method calls
    method_call_count: int = Field(default=0, ge=0, description="Method call count")
    unique_methods_called: int = Field(default=0, ge=0, description="Unique methods called")
    call_chain_length: float = Field(default=0.0, ge=0.0, description="Average call chain length")
    
    # Dependencies
    external_dependencies: int = Field(default=0, ge=0, description="External dependencies")
    internal_dependencies: int = Field(default=0, ge=0, description="Internal dependencies")
    circular_dependency_risk: float = Field(default=0.0, ge=0.0, le=1.0, description="Circular dependency risk")
    
    # Exception handling
    exception_handling_count: int = Field(default=0, ge=0, description="Exception handling blocks")
    exception_types: List[str] = Field(default_factory=list, description="Exception types")


class StatisticalFeatures(BaseModel):
    """Statistical code metrics."""
    
    # Basic metrics
    lines_of_code: int = Field(..., ge=0, description="Lines of code")
    lines_of_comments: int = Field(default=0, ge=0, description="Lines of comments")
    blank_lines: int = Field(default=0, ge=0, description="Blank lines")
    
    # Halstead metrics
    vocabulary_size: int = Field(default=0, ge=0, description="Halstead vocabulary")
    program_length: int = Field(default=0, ge=0, description="Halstead length")
    program_volume: float = Field(default=0.0, ge=0.0, description="Halstead volume")
    program_difficulty: float = Field(default=0.0, ge=0.0, description="Halstead difficulty")
    program_effort: float = Field(default=0.0, ge=0.0, description="Halstead effort")
    
    # Complexity metrics
    cognitive_complexity: float = Field(default=0.0, ge=0.0, description="Cognitive complexity")
    maintainability_index: float = Field(default=0.0, ge=0.0, description="Maintainability index")
    
    # Size metrics
    file_size_bytes: int = Field(default=0, ge=0, description="File size in bytes")
    character_count: int = Field(default=0, ge=0, description="Character count")
    
    # Readability
    readability_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Readability score")
    complexity_density: float = Field(default=0.0, ge=0.0, description="Complexity density")


class ModelInfo(TimestampedModel):
    """Model information model."""
    
    model_id: str = Field(..., description="Model identifier")
    name: str = Field(..., description="Model name")
    description: str = Field(..., description="Model description")
    model_type: ModelType = Field(..., description="Type of model")
    version: str = Field(..., description="Model version")
    status: ModelStatus = Field(..., description="Model status")
    framework: str = Field(..., description="ML framework used")
    input_schema: Dict[str, Any] = Field(..., description="Input data schema")
    output_schema: Dict[str, Any] = Field(..., description="Output data schema")
    metrics: Optional[Dict[str, float]] = Field(None, description="Model performance metrics")
    hyperparameters: Dict[str, Any] = Field(default_factory=dict, description="Model hyperparameters")
    training_data_info: Optional[Dict[str, Any]] = Field(None, description="Training data information")
    model_size: Optional[int] = Field(None, description="Model size in bytes")
    inference_time: Optional[float] = Field(None, description="Average inference time in seconds")
    
    @property
    def is_deployable(self) -> bool:
        """Check if model can be deployed."""
        return self.status == ModelStatus.TRAINED
    
    @property
    def is_active(self) -> bool:
        """Check if model is active."""
        return self.status == ModelStatus.DEPLOYED


class EvaluationMetrics(BaseModel):
    """Model evaluation metrics."""
    
    accuracy: Optional[float] = Field(None, ge=0.0, le=1.0, description="Accuracy score")
    precision: Optional[float] = Field(None, ge=0.0, le=1.0, description="Precision score")
    recall: Optional[float] = Field(None, ge=0.0, le=1.0, description="Recall score")
    f1_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="F1 score")
    roc_auc: Optional[float] = Field(None, ge=0.0, le=1.0, description="ROC AUC score")
    confusion_matrix: Optional[List[List[int]]] = Field(None, description="Confusion matrix")
    classification_report: Optional[Dict[str, Any]] = Field(None, description="Classification report")
    custom_metrics: Dict[str, float] = Field(default_factory=dict, description="Custom metrics")
    
    @validator('f1_score')
    def validate_f1_score(cls, v, values):
        """Validate F1 score calculation."""
        if v is not None and 'precision' in values and 'recall' in values:
            precision = values['precision']
            recall = values['recall']
            if precision is not None and recall is not None:
                expected_f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
                if abs(v - expected_f1) > 0.01:  # Allow small tolerance
                    raise ValueError("F1 score does not match precision and recall")
        return v


class ModelTrainingRequest(BaseModel):
    """Request model for training ML models."""
    
    model_id: str = Field(..., description="Model identifier")
    training_data: Dict[str, Any] = Field(..., description="Training data configuration")
    validation_data: Optional[Dict[str, Any]] = Field(None, description="Validation data configuration")
    hyperparameters: Dict[str, Any] = Field(default_factory=dict, description="Model hyperparameters")
    training_config: Dict[str, Any] = Field(default_factory=dict, description="Training configuration")
    early_stopping: bool = Field(True, description="Enable early stopping")
    max_epochs: int = Field(100, ge=1, description="Maximum number of training epochs")
    batch_size: int = Field(32, ge=1, description="Training batch size")
    learning_rate: float = Field(0.001, gt=0.0, description="Learning rate")
    callbacks: List[str] = Field(default_factory=list, description="Training callbacks")
    
    @validator('hyperparameters')
    def validate_hyperparameters(cls, v):
        """Validate hyperparameters."""
        if not isinstance(v, dict):
            raise ValueError("Hyperparameters must be a dictionary")
        return v


class ModelTrainingResponse(BaseModel):
    """Response model for training requests."""
    
    job_id: str = Field(..., description="Training job identifier")
    model_id: str = Field(..., description="Model identifier")
    status: TrainingStatus = Field(..., description="Training status")
    estimated_duration: Optional[str] = Field(None, description="Estimated training duration")
    message: Optional[str] = Field(None, description="Status message")
    created_at: datetime = Field(default_factory=datetime.utcnow)


class TrainingJob(JobStatus):
    """Training job model."""
    
    model_id: str = Field(..., description="Model identifier")
    training_request: Optional[ModelTrainingRequest] = Field(None, description="Training request")
    metrics: Optional[EvaluationMetrics] = Field(None, description="Training metrics")
    logs: List[str] = Field(default_factory=list, description="Training logs")
    checkpoints: List[str] = Field(default_factory=list, description="Model checkpoints")
    
    @property
    def is_completed(self) -> bool:
        """Check if training is completed."""
        return self.status == TrainingStatus.COMPLETED.value
    
    @property
    def is_failed(self) -> bool:
        """Check if training failed."""
        return self.status == TrainingStatus.FAILED.value


class ModelPredictionRequest(BaseModel):
    """Request model for model predictions."""
    
    model_id: str = Field(..., description="Model identifier")
    input_data: Dict[str, Any] = Field(..., description="Input data for prediction")
    batch_size: Optional[int] = Field(None, ge=1, description="Batch size for prediction")
    return_probabilities: bool = Field(False, description="Return prediction probabilities")
    
    @validator('input_data')
    def validate_input_data(cls, v):
        """Validate input data."""
        if not isinstance(v, dict):
            raise ValueError("Input data must be a dictionary")
        return v


class ModelPredictionResponse(BaseModel):
    """Response model for model predictions."""
    
    model_id: str = Field(..., description="Model identifier")
    predictions: Union[List[Any], Dict[str, Any]] = Field(..., description="Model predictions")
    probabilities: Optional[List[float]] = Field(None, description="Prediction probabilities")
    confidence_scores: Optional[List[float]] = Field(None, description="Confidence scores")
    processing_time: float = Field(..., description="Processing time in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @property
    def prediction_count(self) -> int:
        """Get number of predictions."""
        if isinstance(self.predictions, list):
            return len(self.predictions)
        return 1


class EnsembleModelRequest(BaseModel):
    """Request model for ensemble model inference."""
    
    # Input features
    features: CodeFeatures = Field(..., description="Code features")
    
    # Model selection
    model_types: List[ModelType] = Field(
        default_factory=lambda: [ModelType.RANDOM_FOREST, ModelType.GRADIENT_BOOSTING, ModelType.DBSCAN],
        description="Model types to use in ensemble"
    )
    
    # Ensemble configuration
    voting_strategy: str = Field(
        default="soft",
        description="Voting strategy (hard, soft, weighted)"
    )
    confidence_weighting: bool = Field(
        default=True,
        description="Weight predictions by confidence"
    )
    
    # Performance options
    parallel_inference: bool = Field(
        default=True,
        description="Run models in parallel"
    )
    timeout_seconds: int = Field(
        default=30,
        ge=1,
        le=300,
        description="Inference timeout"
    )


class EnsembleModelResponse(BaseModel):
    """Response model for ensemble model inference."""
    
    # Ensemble results
    ensemble_predictions: List[Any] = Field(..., description="Ensemble predictions")
    ensemble_confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Ensemble confidence score"
    )
    
    # Individual model results
    individual_predictions: Dict[str, Any] = Field(
        default_factory=dict,
        description="Individual model predictions"
    )
    individual_confidences: Dict[str, float] = Field(
        default_factory=dict,
        description="Individual model confidences"
    )
    
    # Ensemble metrics
    agreement_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Agreement between models"
    )
    uncertainty_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Uncertainty in predictions"
    )
    
    # Model performance
    model_weights: Dict[str, float] = Field(
        default_factory=dict,
        description="Model weights in ensemble"
    )
    inference_time_ms: int = Field(..., description="Total inference time")
    
    # Quality indicators
    prediction_quality: Dict[str, float] = Field(
        default_factory=dict,
        description="Prediction quality metrics"
    )


class BatchInferenceRequest(BaseModel):
    """Request model for batch inference."""
    
    # Input data
    features_batch: List[CodeFeatures] = Field(..., description="Batch of code features")
    
    # Model configuration
    model_id: str = Field(..., description="Model identifier")
    batch_size: int = Field(default=32, ge=1, le=1000, description="Batch size")
    
    # Processing options
    parallel_processing: bool = Field(default=True, description="Enable parallel processing")
    return_detailed_results: bool = Field(default=False, description="Return detailed results")
    
    # Quality controls
    confidence_threshold: float = Field(
        default=0.5,
        ge=0.0,
        le=1.0,
        description="Confidence threshold for filtering"
    )
    
    @validator('features_batch')
    def validate_features_batch(cls, v):
        """Validate features batch."""
        if not v:
            raise ValueError("Features batch cannot be empty")
        if len(v) > 10000:
            raise ValueError("Batch size too large (max 10000)")
        return v


class BatchInferenceResponse(BaseModel):
    """Response model for batch inference."""
    
    # Batch results
    predictions: List[Any] = Field(..., description="Batch predictions")
    confidences: List[float] = Field(..., description="Prediction confidences")
    
    # Processing metrics
    total_processed: int = Field(..., description="Total items processed")
    successful_predictions: int = Field(..., description="Successful predictions")
    failed_predictions: int = Field(..., description="Failed predictions")
    
    # Performance metrics
    total_time_ms: int = Field(..., description="Total processing time")
    average_time_per_item_ms: float = Field(..., description="Average time per item")
    throughput_items_per_second: float = Field(..., description="Processing throughput")
    
    # Quality metrics
    average_confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Average confidence score"
    )
    high_confidence_count: int = Field(..., description="High confidence predictions")
    
    # Error handling
    errors: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Processing errors"
    )
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_processed == 0:
            return 0.0
        return self.successful_predictions / self.total_processed


class ModelPerformanceMetrics(BaseModel):
    """Comprehensive model performance metrics."""
    
    # Basic metrics
    accuracy: float = Field(..., ge=0.0, le=1.0, description="Accuracy score")
    precision: float = Field(..., ge=0.0, le=1.0, description="Precision score")
    recall: float = Field(..., ge=0.0, le=1.0, description="Recall score")
    f1_score: float = Field(..., ge=0.0, le=1.0, description="F1 score")
    
    # Advanced metrics
    roc_auc: Optional[float] = Field(None, ge=0.0, le=1.0, description="ROC AUC score")
    pr_auc: Optional[float] = Field(None, ge=0.0, le=1.0, description="PR AUC score")
    
    # Confidence metrics
    confidence_accuracy: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Confidence calibration accuracy"
    )
    confidence_reliability: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Confidence reliability score"
    )
    
    # Efficiency metrics
    inference_time_ms: float = Field(..., ge=0.0, description="Average inference time")
    memory_usage_mb: float = Field(..., ge=0.0, description="Memory usage")
    model_size_mb: float = Field(..., ge=0.0, description="Model size")
    
    # Robustness metrics
    stability_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Prediction stability score"
    )
    noise_resistance: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Noise resistance score"
    )
    
    # Class-specific metrics
    per_class_metrics: Dict[str, Dict[str, float]] = Field(
        default_factory=dict,
        description="Per-class performance metrics"
    )
    
    # Confusion matrix
    confusion_matrix: Optional[List[List[int]]] = Field(None, description="Confusion matrix")
    
    # Quality indicators
    overall_quality_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Overall model quality score"
    )
    
    @classmethod
    def calculate_quality_score(cls, metrics: Dict[str, float]) -> float:
        """Calculate overall quality score from metrics."""
        weights = {
            'accuracy': 0.25,
            'precision': 0.20,
            'recall': 0.20,
            'f1_score': 0.20,
            'confidence_accuracy': 0.15
        }
        
        weighted_sum = sum(
            metrics.get(metric, 0.0) * weight
            for metric, weight in weights.items()
        )
        
        return min(weighted_sum, 1.0)


class ModelComparisonRequest(BaseModel):
    """Request model for comparing multiple models."""
    
    # Models to compare
    model_ids: List[str] = Field(..., description="Model identifiers to compare")
    
    # Test data
    test_features: List[CodeFeatures] = Field(..., description="Test features")
    ground_truth: List[Any] = Field(..., description="Ground truth labels")
    
    # Comparison metrics
    metrics_to_compare: List[str] = Field(
        default_factory=lambda: ["accuracy", "precision", "recall", "f1_score"],
        description="Metrics to compare"
    )
    
    # Statistical testing
    statistical_significance: bool = Field(
        default=True,
        description="Perform statistical significance testing"
    )
    confidence_level: float = Field(
        default=0.95,
        ge=0.8,
        le=0.99,
        description="Confidence level for statistical tests"
    )
    
    @validator('model_ids')
    def validate_model_ids(cls, v):
        """Validate model IDs."""
        if len(v) < 2:
            raise ValueError("At least 2 models required for comparison")
        if len(set(v)) != len(v):
            raise ValueError("Duplicate model IDs not allowed")
        return v


class ModelComparisonResponse(BaseModel):
    """Response model for model comparison."""
    
    # Individual model results
    model_performances: Dict[str, ModelPerformanceMetrics] = Field(
        ...,
        description="Performance metrics for each model"
    )
    
    # Comparison results
    ranking: List[Tuple[str, float]] = Field(
        ...,
        description="Models ranked by performance"
    )
    
    # Statistical analysis
    statistical_tests: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Statistical significance test results"
    )
    
    # Recommendations
    best_model: str = Field(..., description="Best performing model")
    recommendations: List[str] = Field(
        default_factory=list,
        description="Recommendations based on comparison"
    )
    
    # Summary
    comparison_summary: Dict[str, Any] = Field(
        default_factory=dict,
        description="Summary of comparison results"
    )
    
    # Metadata
    comparison_timestamp: datetime = Field(default_factory=datetime.utcnow)
    test_data_size: int = Field(..., description="Size of test dataset")


class ModelRegistry(BaseModel):
    """Model registry for managing model versions."""
    
    # Registry info
    registry_id: str = Field(..., description="Registry identifier")
    registry_name: str = Field(..., description="Registry name")
    
    # Models
    models: Dict[str, ModelInfo] = Field(
        default_factory=dict,
        description="Registered models"
    )
    
    # Versioning
    model_versions: Dict[str, List[str]] = Field(
        default_factory=dict,
        description="Model versions"
    )
    
    # Deployment tracking
    deployed_models: Dict[str, str] = Field(
        default_factory=dict,
        description="Currently deployed models"
    )
    
    # Performance tracking
    performance_history: Dict[str, List[Dict[str, Any]]] = Field(
        default_factory=dict,
        description="Performance history"
    )
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    
    def add_model(self, model_info: ModelInfo) -> None:
        """Add model to registry."""
        self.models[model_info.model_id] = model_info
        if model_info.model_id not in self.model_versions:
            self.model_versions[model_info.model_id] = []
        self.model_versions[model_info.model_id].append(model_info.version)
        self.last_updated = datetime.utcnow()
    
    def get_latest_version(self, model_id: str) -> Optional[str]:
        """Get latest version of a model."""
        versions = self.model_versions.get(model_id, [])
        return versions[-1] if versions else None
    
    def is_model_deployed(self, model_id: str) -> bool:
        """Check if model is deployed."""
        return model_id in self.deployed_models


# Update existing ModelInfo to include enhanced features
class EnhancedModelInfo(ModelInfo):
    """Enhanced model information with additional ML-specific fields."""
    
    # Feature requirements
    required_features: List[FeatureType] = Field(
        default_factory=list,
        description="Required feature types"
    )
    feature_dimension: int = Field(..., description="Expected feature dimension")
    
    # Model capabilities
    supported_languages: List[str] = Field(
        default_factory=list,
        description="Supported programming languages"
    )
    pattern_types: List[str] = Field(
        default_factory=list,
        description="Pattern types this model can detect"
    )
    
    # Performance characteristics
    performance_metrics: Optional[ModelPerformanceMetrics] = Field(
        None,
        description="Model performance metrics"
    )
    
    # Resource requirements
    min_memory_mb: int = Field(default=512, description="Minimum memory requirement")
    max_memory_mb: int = Field(default=4096, description="Maximum memory requirement")
    cpu_cores: int = Field(default=1, description="CPU cores required")
    gpu_required: bool = Field(default=False, description="GPU requirement")
    
    # Deployment info
    deployment_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="Deployment configuration"
    )
    health_check_url: Optional[str] = Field(None, description="Health check URL")
    
    # Monitoring
    monitoring_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="Monitoring configuration"
    )
    
    @property
    def memory_footprint_mb(self) -> int:
        """Estimate memory footprint."""
        base_memory = self.min_memory_mb
        model_memory = self.model_size // (1024 * 1024) if self.model_size else 0
        return base_memory + model_memory


# Model rebuilding for forward references
EnsembleModelRequest.model_rebuild()
EnsembleModelResponse.model_rebuild()
BatchInferenceRequest.model_rebuild()
BatchInferenceResponse.model_rebuild()
ModelPerformanceMetrics.model_rebuild()
ModelComparisonRequest.model_rebuild()
ModelComparisonResponse.model_rebuild()
ModelRegistry.model_rebuild()
EnhancedModelInfo.model_rebuild()


class ModelDeploymentRequest(BaseModel):
    """Request model for model deployment."""
    
    model_id: str = Field(..., description="Model identifier")
    deployment_config: Dict[str, Any] = Field(default_factory=dict, description="Deployment configuration")
    scaling_config: Optional[Dict[str, Any]] = Field(None, description="Scaling configuration")
    resource_requirements: Optional[Dict[str, Any]] = Field(None, description="Resource requirements")
    
    @validator('deployment_config')
    def validate_deployment_config(cls, v):
        """Validate deployment configuration."""
        if not isinstance(v, dict):
            raise ValueError("Deployment config must be a dictionary")
        return v


class ModelDeploymentResponse(BaseModel):
    """Response model for model deployment."""
    
    deployment_id: str = Field(..., description="Deployment identifier")
    model_id: str = Field(..., description="Model identifier")
    endpoint_url: str = Field(..., description="Model endpoint URL")
    status: str = Field(..., description="Deployment status")
    created_at: datetime = Field(default_factory=datetime.utcnow)