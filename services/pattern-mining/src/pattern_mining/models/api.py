"""
API Request/Response Models

Comprehensive Pydantic models for pattern detection API endpoints including
repository analysis, batch processing, and streaming response models.
"""

from pydantic import Field, validator, root_validator
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime
from enum import Enum
from uuid import UUID, uuid4

from .base import BaseModel, TimestampedModel, PaginatedResponse
from .patterns import PatternType, SeverityLevel, DetectionType


class AnalysisScope(str, Enum):
    """Analysis scope enumeration."""
    
    FILE = "file"
    DIRECTORY = "directory"
    REPOSITORY = "repository"
    PACKAGE = "package"
    MODULE = "module"


class ProcessingMode(str, Enum):
    """Processing mode enumeration."""
    
    SYNCHRONOUS = "synchronous"
    ASYNCHRONOUS = "asynchronous"
    STREAMING = "streaming"
    BATCH = "batch"


class PatternDetectionRequest(BaseModel):
    """Request model for pattern detection endpoints."""
    
    # Core request data
    repository_id: str = Field(..., description="Repository identifier")
    ast_data: Dict[str, Any] = Field(..., description="AST data from analysis engine")
    code_content: str = Field(..., description="Source code content")
    file_path: str = Field(..., description="File path within repository")
    language: str = Field(..., description="Programming language")
    
    # Detection configuration
    detection_config: 'DetectionConfig' = Field(..., description="Detection configuration")
    
    # Context and metadata
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Request metadata")
    
    # Processing options
    processing_mode: ProcessingMode = Field(
        default=ProcessingMode.SYNCHRONOUS,
        description="Processing mode"
    )
    priority: int = Field(default=5, ge=1, le=10, description="Request priority (1-10)")
    timeout_seconds: int = Field(default=30, ge=1, le=300, description="Request timeout")
    
    @validator('language')
    def validate_language(cls, v):
        """Validate programming language."""
        supported_languages = [
            'python', 'javascript', 'typescript', 'java', 'cpp', 'c', 'go',
            'rust', 'ruby', 'php', 'swift', 'kotlin', 'scala', 'clojure',
            'csharp', 'haskell', 'r', 'matlab', 'sql', 'html', 'css'
        ]
        if v.lower() not in supported_languages:
            raise ValueError(f"Language '{v}' not supported")
        return v.lower()
    
    @validator('ast_data')
    def validate_ast_data(cls, v):
        """Validate AST data structure."""
        if not isinstance(v, dict):
            raise ValueError("AST data must be a dictionary")
        
        # Check for required AST fields
        required_fields = ['type', 'children']
        if not all(field in v for field in required_fields):
            raise ValueError(f"AST data missing required fields: {required_fields}")
        
        return v
    
    @validator('code_content')
    def validate_code_content(cls, v):
        """Validate code content."""
        if not v.strip():
            raise ValueError("Code content cannot be empty")
        
        # Check for reasonable size limits
        max_size = 10 * 1024 * 1024  # 10MB
        if len(v.encode('utf-8')) > max_size:
            raise ValueError(f"Code content exceeds maximum size of {max_size} bytes")
        
        return v


class DetectionConfig(BaseModel):
    """Configuration for pattern detection."""
    
    # Pattern selection
    pattern_types: Optional[List[PatternType]] = Field(
        None, 
        description="Specific pattern types to detect (None = all)"
    )
    exclude_patterns: List[str] = Field(
        default_factory=list,
        description="Pattern IDs to exclude"
    )
    
    # Confidence and filtering
    confidence_threshold: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Minimum confidence threshold"
    )
    max_patterns_per_file: int = Field(
        default=50,
        ge=1,
        le=1000,
        description="Maximum patterns per file"
    )
    
    # Detection methods
    enable_ml_models: bool = Field(True, description="Enable ML-based detection")
    enable_heuristic_detection: bool = Field(True, description="Enable heuristic detection")
    enable_clustering: bool = Field(True, description="Enable clustering-based detection")
    enable_deep_learning: bool = Field(False, description="Enable deep learning models")
    use_bigquery_ml: bool = Field(False, description="Use BigQuery ML for detection")
    
    # Language-specific hints
    language_hints: List[str] = Field(
        default_factory=list,
        description="Language-specific hints (frameworks, libraries)"
    )
    
    # Advanced options
    enable_cross_file_analysis: bool = Field(
        False, 
        description="Enable cross-file pattern analysis"
    )
    enable_evolutionary_patterns: bool = Field(
        False,
        description="Enable pattern evolution detection"
    )
    
    @validator('pattern_types')
    def validate_pattern_types(cls, v):
        """Validate pattern types."""
        if v is not None:
            if not isinstance(v, list):
                raise ValueError("Pattern types must be a list")
            if len(v) == 0:
                raise ValueError("Pattern types list cannot be empty")
        return v


class BatchDetectionRequest(BaseModel):
    """Request model for batch pattern detection."""
    
    # Repository information
    repository_id: str = Field(..., description="Repository identifier")
    repository_url: Optional[str] = Field(None, description="Repository URL")
    
    # File selection
    file_patterns: List[str] = Field(
        ...,
        description="Glob patterns for files to analyze"
    )
    exclude_patterns: List[str] = Field(
        default_factory=list,
        description="Glob patterns for files to exclude"
    )
    
    # Detection configuration
    detection_config: DetectionConfig = Field(..., description="Detection configuration")
    
    # Processing options
    parallel_jobs: int = Field(default=10, ge=1, le=100, description="Number of parallel jobs")
    timeout_seconds: int = Field(default=300, ge=30, le=3600, description="Batch timeout")
    chunk_size: int = Field(default=50, ge=1, le=1000, description="Files per chunk")
    
    # Analysis scope
    analysis_scope: AnalysisScope = Field(
        default=AnalysisScope.REPOSITORY,
        description="Analysis scope"
    )
    
    # Callback configuration
    callback_url: Optional[str] = Field(None, description="Callback URL for results")
    callback_headers: Dict[str, str] = Field(
        default_factory=dict,
        description="Headers for callback requests"
    )
    
    @validator('file_patterns')
    def validate_file_patterns(cls, v):
        """Validate file patterns."""
        if not v:
            raise ValueError("File patterns cannot be empty")
        
        # Check for valid glob patterns
        import glob
        for pattern in v:
            if not pattern.strip():
                raise ValueError("File pattern cannot be empty")
        
        return v
    
    @validator('parallel_jobs')
    def validate_parallel_jobs(cls, v, values):
        """Validate parallel jobs configuration."""
        if 'file_patterns' in values and len(values['file_patterns']) < v:
            return len(values['file_patterns'])
        return v


class StreamingDetectionRequest(BaseModel):
    """Request model for streaming pattern detection."""
    
    # Stream configuration
    stream_id: str = Field(..., description="Stream identifier")
    repository_id: str = Field(..., description="Repository identifier")
    
    # Detection configuration
    detection_config: DetectionConfig = Field(..., description="Detection configuration")
    
    # Streaming options
    buffer_size: int = Field(default=100, ge=1, le=1000, description="Buffer size")
    flush_interval: int = Field(default=5, ge=1, le=60, description="Flush interval in seconds")
    
    # Quality controls
    enable_incremental_analysis: bool = Field(
        True,
        description="Enable incremental analysis"
    )
    enable_result_aggregation: bool = Field(
        True,
        description="Enable result aggregation"
    )


class RepositoryAnalysisRequest(BaseModel):
    """Request model for repository analysis."""
    
    # Repository information
    repository_id: str = Field(..., description="Repository identifier")
    repository_url: str = Field(..., description="Repository URL")
    branch: str = Field(default="main", description="Branch to analyze")
    commit_sha: Optional[str] = Field(None, description="Specific commit SHA")
    
    # Analysis configuration
    analysis_type: str = Field(
        default="comprehensive",
        description="Analysis type (quick, standard, comprehensive)"
    )
    detection_config: DetectionConfig = Field(..., description="Detection configuration")
    
    # Processing options
    include_tests: bool = Field(True, description="Include test files")
    include_documentation: bool = Field(False, description="Include documentation files")
    include_build_files: bool = Field(False, description="Include build files")
    
    # Output options
    generate_report: bool = Field(True, description="Generate analysis report")
    report_format: str = Field(default="json", description="Report format")
    
    @validator('repository_url')
    def validate_repository_url(cls, v):
        """Validate repository URL."""
        import re
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(v):
            raise ValueError("Invalid repository URL format")
        
        return v


class PatternDetectionResponse(BaseModel):
    """Response model for pattern detection."""
    
    # Request information
    request_id: str = Field(..., description="Request identifier")
    repository_id: str = Field(..., description="Repository identifier")
    
    # Detection results
    patterns: List['DetectedPattern'] = Field(
        default_factory=list,
        description="Detected patterns"
    )
    
    # Analysis summary
    summary: 'PatternSummary' = Field(..., description="Pattern summary")
    
    # Performance metrics
    processing_time_ms: int = Field(..., description="Processing time in milliseconds")
    model_versions: Dict[str, str] = Field(
        default_factory=dict,
        description="Model versions used"
    )
    
    # Metadata
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Response metadata"
    )
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    @property
    def pattern_count(self) -> int:
        """Get total number of patterns."""
        return len(self.patterns)
    
    @property
    def critical_patterns(self) -> List['DetectedPattern']:
        """Get critical patterns."""
        return [p for p in self.patterns if p.severity == SeverityLevel.CRITICAL]
    
    @property
    def high_severity_patterns(self) -> List['DetectedPattern']:
        """Get high severity patterns."""
        return [p for p in self.patterns if p.severity == SeverityLevel.HIGH]


class BatchDetectionResponse(BaseModel):
    """Response model for batch pattern detection."""
    
    # Job information
    job_id: str = Field(..., description="Batch job identifier")
    repository_id: str = Field(..., description="Repository identifier")
    
    # Processing status
    status: str = Field(..., description="Batch processing status")
    progress: float = Field(default=0.0, ge=0.0, le=100.0, description="Progress percentage")
    
    # File processing results
    files_processed: int = Field(default=0, description="Number of files processed")
    files_total: int = Field(..., description="Total number of files")
    
    # Pattern results
    patterns_detected: int = Field(default=0, description="Total patterns detected")
    patterns_by_file: Dict[str, int] = Field(
        default_factory=dict,
        description="Patterns detected per file"
    )
    
    # Error handling
    errors: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Processing errors"
    )
    
    # Performance metrics
    processing_time_ms: int = Field(default=0, description="Total processing time")
    average_file_time_ms: float = Field(default=0.0, description="Average time per file")
    
    # Results access
    results_url: Optional[str] = Field(None, description="URL to access full results")
    expires_at: Optional[datetime] = Field(None, description="Results expiration time")
    
    @property
    def completion_percentage(self) -> float:
        """Get completion percentage."""
        if self.files_total == 0:
            return 0.0
        return (self.files_processed / self.files_total) * 100.0
    
    @property
    def has_errors(self) -> bool:
        """Check if there are processing errors."""
        return len(self.errors) > 0


class StreamingDetectionResponse(BaseModel):
    """Response model for streaming pattern detection."""
    
    # Stream information
    stream_id: str = Field(..., description="Stream identifier")
    sequence_number: int = Field(..., description="Response sequence number")
    
    # Batch data
    patterns: List['DetectedPattern'] = Field(
        default_factory=list,
        description="Patterns in this batch"
    )
    
    # Stream status
    is_final: bool = Field(default=False, description="Is this the final response")
    more_data: bool = Field(default=True, description="More data available")
    
    # Processing metrics
    batch_processing_time_ms: int = Field(..., description="Batch processing time")
    cumulative_patterns: int = Field(..., description="Cumulative patterns detected")
    
    # Stream control
    next_token: Optional[str] = Field(None, description="Token for next batch")
    
    @property
    def batch_size(self) -> int:
        """Get batch size."""
        return len(self.patterns)


class RepositoryAnalysisResponse(BaseModel):
    """Response model for repository analysis."""
    
    # Analysis information
    analysis_id: str = Field(..., description="Analysis identifier")
    repository_id: str = Field(..., description="Repository identifier")
    repository_url: str = Field(..., description="Repository URL")
    
    # Analysis summary
    summary: 'RepositoryAnalysisSummary' = Field(..., description="Analysis summary")
    
    # Detailed results
    file_analyses: List['FileAnalysis'] = Field(
        default_factory=list,
        description="Per-file analysis results"
    )
    
    # Quality metrics
    quality_metrics: 'QualityMetrics' = Field(..., description="Code quality metrics")
    
    # Recommendations
    recommendations: List['Recommendation'] = Field(
        default_factory=list,
        description="Improvement recommendations"
    )
    
    # Report generation
    report_url: Optional[str] = Field(None, description="Analysis report URL")
    report_format: str = Field(default="json", description="Report format")
    
    # Metadata
    analyzed_at: datetime = Field(default_factory=datetime.utcnow)
    processing_time_ms: int = Field(..., description="Total processing time")


class PatternSummary(BaseModel):
    """Summary of pattern detection results."""
    
    # Basic counts
    total_patterns: int = Field(..., description="Total patterns detected")
    unique_patterns: int = Field(..., description="Unique pattern types")
    
    # By category
    by_type: Dict[PatternType, int] = Field(
        default_factory=dict,
        description="Patterns by type"
    )
    by_severity: Dict[SeverityLevel, int] = Field(
        default_factory=dict,
        description="Patterns by severity"
    )
    by_detection_method: Dict[DetectionType, int] = Field(
        default_factory=dict,
        description="Patterns by detection method"
    )
    
    # Quality scores
    quality_score: float = Field(
        ...,
        ge=0.0,
        le=100.0,
        description="Overall quality score (0-100)"
    )
    security_score: float = Field(
        ...,
        ge=0.0,
        le=100.0,
        description="Security score (0-100)"
    )
    performance_score: float = Field(
        ...,
        ge=0.0,
        le=100.0,
        description="Performance score (0-100)"
    )
    maintainability_score: float = Field(
        ...,
        ge=0.0,
        le=100.0,
        description="Maintainability score (0-100)"
    )
    
    # Top patterns
    top_patterns: List[Tuple[str, int]] = Field(
        default_factory=list,
        description="Top patterns by occurrence"
    )
    
    # Statistics
    average_confidence: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Average confidence score"
    )
    confidence_distribution: Dict[str, int] = Field(
        default_factory=dict,
        description="Confidence score distribution"
    )
    
    @classmethod
    def from_patterns(cls, patterns: List['DetectedPattern']) -> 'PatternSummary':
        """Create summary from detected patterns."""
        if not patterns:
            return cls(
                total_patterns=0,
                unique_patterns=0,
                quality_score=100.0,
                security_score=100.0,
                performance_score=100.0,
                maintainability_score=100.0,
                average_confidence=0.0
            )
        
        # Count by type and severity
        by_type = {}
        by_severity = {}
        by_detection_method = {}
        
        for pattern in patterns:
            by_type[pattern.pattern_type] = by_type.get(pattern.pattern_type, 0) + 1
            by_severity[pattern.severity] = by_severity.get(pattern.severity, 0) + 1
            by_detection_method[pattern.detection_method] = by_detection_method.get(
                pattern.detection_method, 0
            ) + 1
        
        # Calculate quality scores
        critical_count = by_severity.get(SeverityLevel.CRITICAL, 0)
        high_count = by_severity.get(SeverityLevel.HIGH, 0)
        medium_count = by_severity.get(SeverityLevel.MEDIUM, 0)
        
        # Simple quality scoring (can be enhanced with more sophisticated logic)
        quality_score = max(0, 100 - (critical_count * 20 + high_count * 10 + medium_count * 5))
        
        # Security score based on security patterns
        security_patterns = by_type.get(PatternType.SECURITY_ISSUE, 0)
        security_score = max(0, 100 - (security_patterns * 15))
        
        # Performance score based on performance patterns
        performance_patterns = by_type.get(PatternType.PERFORMANCE_ISSUE, 0)
        performance_score = max(0, 100 - (performance_patterns * 10))
        
        # Maintainability score based on code smells and anti-patterns
        maintainability_issues = (
            by_type.get(PatternType.CODE_SMELL, 0) +
            by_type.get(PatternType.ANTI_PATTERN, 0)
        )
        maintainability_score = max(0, 100 - (maintainability_issues * 8))
        
        # Top patterns
        pattern_counts = {}
        for pattern in patterns:
            pattern_counts[pattern.pattern_name] = pattern_counts.get(pattern.pattern_name, 0) + 1
        
        top_patterns = sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Average confidence
        avg_confidence = sum(p.confidence for p in patterns) / len(patterns)
        
        # Confidence distribution
        confidence_distribution = {
            "high (>0.8)": sum(1 for p in patterns if p.confidence > 0.8),
            "medium (0.6-0.8)": sum(1 for p in patterns if 0.6 <= p.confidence <= 0.8),
            "low (<0.6)": sum(1 for p in patterns if p.confidence < 0.6)
        }
        
        return cls(
            total_patterns=len(patterns),
            unique_patterns=len(set(p.pattern_name for p in patterns)),
            by_type=by_type,
            by_severity=by_severity,
            by_detection_method=by_detection_method,
            quality_score=quality_score,
            security_score=security_score,
            performance_score=performance_score,
            maintainability_score=maintainability_score,
            top_patterns=top_patterns,
            average_confidence=avg_confidence,
            confidence_distribution=confidence_distribution
        )


class RepositoryAnalysisSummary(BaseModel):
    """Summary of repository analysis."""
    
    # File statistics
    files_analyzed: int = Field(..., description="Number of files analyzed")
    lines_of_code: int = Field(..., description="Total lines of code")
    languages: Dict[str, int] = Field(
        default_factory=dict,
        description="Languages and their line counts"
    )
    
    # Pattern statistics
    pattern_summary: PatternSummary = Field(..., description="Pattern detection summary")
    
    # Quality metrics
    code_coverage: Optional[float] = Field(None, description="Code coverage percentage")
    complexity_score: float = Field(..., description="Average complexity score")
    duplication_percentage: float = Field(..., description="Code duplication percentage")
    
    # Health indicators
    health_score: float = Field(
        ...,
        ge=0.0,
        le=100.0,
        description="Overall repository health score"
    )
    technical_debt_hours: float = Field(..., description="Estimated technical debt in hours")
    
    # Trend analysis
    trend_analysis: Optional[Dict[str, Any]] = Field(
        None,
        description="Trend analysis compared to previous runs"
    )


class FileAnalysis(BaseModel):
    """Analysis results for a single file."""
    
    # File information
    file_path: str = Field(..., description="File path")
    language: str = Field(..., description="Programming language")
    lines_of_code: int = Field(..., description="Lines of code")
    
    # Pattern results
    patterns: List['DetectedPattern'] = Field(
        default_factory=list,
        description="Patterns detected in this file"
    )
    
    # File metrics
    complexity_score: float = Field(..., description="File complexity score")
    maintainability_index: float = Field(..., description="Maintainability index")
    
    # Processing info
    processing_time_ms: int = Field(..., description="Processing time")
    analyzed_at: datetime = Field(default_factory=datetime.utcnow)


class QualityMetrics(BaseModel):
    """Code quality metrics."""
    
    # Basic metrics
    cyclomatic_complexity: float = Field(..., description="Average cyclomatic complexity")
    cognitive_complexity: float = Field(..., description="Average cognitive complexity")
    maintainability_index: float = Field(..., description="Maintainability index")
    
    # Size metrics
    lines_of_code: int = Field(..., description="Total lines of code")
    lines_of_comments: int = Field(..., description="Total lines of comments")
    comment_ratio: float = Field(..., description="Comment to code ratio")
    
    # Duplication metrics
    duplicated_lines: int = Field(..., description="Number of duplicated lines")
    duplication_ratio: float = Field(..., description="Duplication ratio")
    
    # Dependency metrics
    afferent_coupling: float = Field(..., description="Average afferent coupling")
    efferent_coupling: float = Field(..., description="Average efferent coupling")
    instability: float = Field(..., description="Package instability")
    
    # Test metrics
    test_coverage: Optional[float] = Field(None, description="Test coverage percentage")
    test_lines: Optional[int] = Field(None, description="Lines of test code")


class Recommendation(BaseModel):
    """Improvement recommendation."""
    
    # Recommendation details
    id: str = Field(..., description="Recommendation identifier")
    title: str = Field(..., description="Recommendation title")
    description: str = Field(..., description="Detailed description")
    category: str = Field(..., description="Recommendation category")
    
    # Priority and impact
    priority: str = Field(..., description="Priority level")
    impact: str = Field(..., description="Expected impact")
    effort: str = Field(..., description="Implementation effort")
    
    # Related patterns
    related_patterns: List[str] = Field(
        default_factory=list,
        description="Related pattern IDs"
    )
    
    # Implementation guidance
    implementation_steps: List[str] = Field(
        default_factory=list,
        description="Step-by-step implementation guide"
    )
    code_examples: List[str] = Field(
        default_factory=list,
        description="Code examples"
    )
    
    # Metrics
    estimated_time_hours: float = Field(..., description="Estimated implementation time")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Recommendation confidence")


# Additional models for new endpoints

class RepositoryPatternsResponse(BaseModel):
    """Response model for repository patterns endpoint."""
    
    repo_id: str = Field(..., description="Repository identifier")
    patterns: List['DetectedPattern'] = Field(
        default_factory=list,
        description="Filtered patterns"
    )
    pagination: Dict[str, Any] = Field(..., description="Pagination information")
    filters: Dict[str, Any] = Field(..., description="Applied filters")
    
    
class RepositorySummary(BaseModel):
    """Repository analysis summary."""
    
    total_files: int = Field(..., description="Total number of files")
    total_patterns: int = Field(..., description="Total patterns detected")
    critical_patterns: int = Field(..., description="Critical patterns count")
    high_confidence_patterns: int = Field(..., description="High confidence patterns count")
    languages_analyzed: List[str] = Field(..., description="Languages analyzed")
    analysis_duration: float = Field(..., description="Analysis duration in seconds")
    file_coverage: float = Field(default=0.0, description="File coverage percentage")
    pattern_density: float = Field(default=0.0, description="Pattern density")
    quality_score: float = Field(default=0.0, description="Quality score")


class RepositoryComparison(BaseModel):
    """Repository comparison results."""
    
    comparison_id: str = Field(..., description="Comparison identifier")
    repository_ids: List[str] = Field(..., description="Repository identifiers")
    comparison_type: str = Field(..., description="Comparison type")
    repositories_data: List[RepositorySummary] = Field(..., description="Repository data")
    pattern_overlap: Dict[str, Any] = Field(..., description="Pattern overlap analysis")
    unique_patterns: Dict[str, Any] = Field(..., description="Unique patterns per repository")
    quality_comparison: Dict[str, Any] = Field(..., description="Quality comparison")
    similarity_matrix: Dict[str, Any] = Field(..., description="Similarity matrix")
    recommendations: List[str] = Field(..., description="Comparison recommendations")
    metadata: Dict[str, Any] = Field(..., description="Comparison metadata")


class RepositoryComparisonRequest(BaseModel):
    """Request model for repository comparison."""
    
    repository_ids: List[str] = Field(..., description="Repository identifiers to compare")
    comparison_type: str = Field(default="similarity", description="Comparison type")
    include_patterns: bool = Field(default=True, description="Include pattern details")
    similarity_threshold: float = Field(default=0.7, description="Similarity threshold")


class AnalyticsReport(BaseModel):
    """Comprehensive analytics report."""
    
    time_range: str = Field(..., description="Time range for the report")
    start_time: datetime = Field(..., description="Report start time")
    end_time: datetime = Field(..., description="Report end time")
    granularity: str = Field(..., description="Time granularity")
    filters: Dict[str, Any] = Field(..., description="Applied filters")
    pattern_statistics: 'PatternStatistics' = Field(..., description="Pattern statistics")
    trend_analysis: 'TrendAnalysis' = Field(..., description="Trend analysis")
    performance_metrics: 'PerformanceMetrics' = Field(..., description="Performance metrics")
    quality_metrics: 'QualityMetrics' = Field(..., description="Quality metrics")
    insights: List[Dict[str, Any]] = Field(..., description="Generated insights")
    metadata: Dict[str, Any] = Field(..., description="Report metadata")


class TrendAnalysis(BaseModel):
    """Trend analysis results."""
    
    metric_name: str = Field(..., description="Metric name")
    time_series: List[Dict[str, Any]] = Field(..., description="Time series data")
    trend_direction: str = Field(..., description="Trend direction")
    growth_rate: float = Field(..., description="Growth rate")
    seasonal_patterns: List[Dict[str, Any]] = Field(..., description="Seasonal patterns")
    forecasts: List[Dict[str, Any]] = Field(..., description="Forecast data")
    confidence_intervals: List[Dict[str, Any]] = Field(..., description="Confidence intervals")
    data_points: Optional[List[Dict[str, Any]]] = Field(None, description="Raw data points")


class PerformanceMetrics(BaseModel):
    """Performance metrics."""
    
    average_response_time: float = Field(..., description="Average response time in ms")
    p95_response_time: float = Field(..., description="95th percentile response time")
    p99_response_time: float = Field(..., description="99th percentile response time")
    throughput: float = Field(..., description="Requests per second")
    error_rate: float = Field(..., description="Error rate percentage")
    cpu_usage: float = Field(..., description="CPU usage percentage")
    memory_usage: float = Field(..., description="Memory usage percentage")
    latency_metrics: Optional[List[Dict[str, Any]]] = Field(None, description="Latency metrics")


class PatternTrends(BaseModel):
    """Pattern trends analysis."""
    
    trend_type: str = Field(..., description="Type of trend")
    time_range: str = Field(..., description="Time range")
    data_points: List[Dict[str, Any]] = Field(..., description="Trend data points")
    growth_rate: float = Field(..., description="Growth rate")
    seasonal_indicators: Dict[str, Any] = Field(..., description="Seasonal indicators")
    predictions: List[Dict[str, Any]] = Field(..., description="Future predictions")


class AnalyticsQuery(BaseModel):
    """Analytics query specification."""
    
    query_type: str = Field(..., description="Query type")
    metrics: List[str] = Field(..., description="Metrics to query")
    filters: Dict[str, Any] = Field(..., description="Query filters")
    time_range: str = Field(..., description="Time range")
    group_by: List[str] = Field(default_factory=list, description="Group by fields")
    aggregations: Dict[str, str] = Field(..., description="Aggregation functions")
    limit: int = Field(default=1000, description="Result limit")
    offset: int = Field(default=0, description="Result offset")


# Import circular dependencies
from .patterns import DetectedPattern, PatternStatistics

# Update forward references
PatternDetectionResponse.model_rebuild()
PatternSummary.model_rebuild()
StreamingDetectionResponse.model_rebuild()
RepositoryAnalysisResponse.model_rebuild()
FileAnalysis.model_rebuild()
RepositoryPatternsResponse.model_rebuild()
AnalyticsReport.model_rebuild()