"""
Base Pydantic Models

Base classes and common models for the pattern mining service.
"""

from pydantic import BaseModel as PydanticBaseModel, Field, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime
from uuid import UUID, uuid4


class BaseModel(PydanticBaseModel):
    """Base model with common configuration."""
    
    model_config = ConfigDict(
        # Enable validation during assignment
        validate_assignment=True,
        # Use enum values instead of enum instances
        use_enum_values=True,
        # Allow population by field name or alias
        populate_by_name=True,
        # Serialize datetime as ISO format
        json_encoders={
            datetime: lambda v: v.isoformat(),
        }
    )


class TimestampedModel(BaseModel):
    """Model with created_at and updated_at timestamps."""
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    def update_timestamp(self) -> None:
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()


class IdentifiableModel(TimestampedModel):
    """Model with UUID identifier."""
    
    id: UUID = Field(default_factory=uuid4)


class ErrorResponse(BaseModel):
    """Standard error response model."""
    
    error: str = Field(..., description="Error message")
    error_code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = Field(None, description="Request ID for tracing")


class SuccessResponse(BaseModel):
    """Standard success response model."""
    
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginatedResponse(BaseModel):
    """Paginated response model."""
    
    items: list = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_previous: bool = Field(..., description="Whether there is a previous page")


class JobStatus(BaseModel):
    """Generic job status model."""
    
    job_id: str = Field(..., description="Job identifier")
    status: str = Field(..., description="Job status")
    progress: int = Field(0, ge=0, le=100, description="Progress percentage")
    message: Optional[str] = Field(None, description="Status message")
    result: Optional[Dict[str, Any]] = Field(None, description="Job result")
    error: Optional[str] = Field(None, description="Error message if failed")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)