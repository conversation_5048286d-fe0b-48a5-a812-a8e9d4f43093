# Pattern Mining Service Environment Variables

# Application Settings
APP_NAME=pattern-mining
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=false

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Database Settings
DATABASE_URL=postgresql://user:password@localhost:5432/pattern_mining
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

# Redis Settings
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=10

# Google Cloud Settings
GCP_PROJECT_ID=your-project-id
GCP_LOCATION=us-central1

# BigQuery Settings
BIGQUERY_DATASET=pattern_mining
BIGQUERY_TABLE_PREFIX=pattern_mining

# ML Settings
ML_MODEL_STORAGE_PATH=/tmp/models
ML_BATCH_SIZE=32
ML_MAX_SEQUENCE_LENGTH=512
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384

# API Settings
API_RATE_LIMIT=100
API_TIMEOUT=30

# Security Settings
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=["*"]

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json

# Monitoring Settings
ENABLE_METRICS=true
METRICS_PORT=9090

# Feature Flags
ENABLE_GPU=false
ENABLE_DISTRIBUTED=false
ENABLE_CACHING=true