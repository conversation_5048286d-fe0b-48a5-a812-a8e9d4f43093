# Pattern Detection Service - Production Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the Pattern Detection service in a production environment with enterprise-grade reliability, security, and performance.

## Prerequisites

### Required Tools
```bash
# Install required CLI tools
brew install google-cloud-sdk kubectl helm terraform
gcloud components install gke-gcloud-auth-plugin

# Python environment
pyenv install 3.13.0
pyenv local 3.13.0
pip install uv==0.5.0  # Ultra-fast package manager

# Verify installations
gcloud version
kubectl version --client
terraform version
python --version  # Should show 3.13.0
```

### GCP Project Setup
```bash
# Set project and enable APIs
export PROJECT_ID=episteme-prod
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable \
  container.googleapis.com \
  bigquery.googleapis.com \
  pubsub.googleapis.com \
  aiplatform.googleapis.com \
  secretmanager.googleapis.com \
  monitoring.googleapis.com \
  cloudtrace.googleapis.com
```

## Infrastructure Deployment

### 1. Terraform Infrastructure
```bash
cd infrastructure/terraform/pattern-detection

# Initialize Terraform
terraform init

# Review plan
terraform plan -var="project_id=$PROJECT_ID"

# Apply infrastructure
terraform apply -var="project_id=$PROJECT_ID" -auto-approve
```

### 2. GKE Cluster Configuration
```hcl
# terraform/gke-cluster.tf
resource "google_container_cluster" "pattern_detection" {
  name     = "pattern-detection-cluster"
  location = "us-central1"
  
  # Autopilot for managed Kubernetes
  enable_autopilot = true
  
  # Network configuration
  network    = google_compute_network.vpc.name
  subnetwork = google_compute_subnetwork.subnet.name
  
  # Security
  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = false
    master_ipv4_cidr_block  = "10.0.0.0/28"
  }
  
  # Workload Identity
  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }
  
  # GPU node pool
  node_pool {
    name = "gpu-pool"
    
    node_config {
      machine_type = "a2-highgpu-1g"
      
      guest_accelerator {
        type  = "nvidia-tesla-a100"
        count = 1
        gpu_driver_installation_config {
          gpu_driver_version = "LATEST"
        }
      }
      
      workload_metadata_config {
        mode = "GKE_METADATA"
      }
    }
    
    autoscaling {
      min_node_count = 1
      max_node_count = 5
    }
  }
  
  # CPU node pool for non-GPU workloads
  node_pool {
    name = "cpu-pool"
    
    node_config {
      machine_type = "n2-standard-8"
    }
    
    autoscaling {
      min_node_count = 2
      max_node_count = 10
    }
  }
}
```

### 3. BigQuery Setup
```bash
# Create datasets
bq mk --location=US --dataset ${PROJECT_ID}:pattern_detection_v2
bq mk --location=US --dataset ${PROJECT_ID}:pattern_detection_staging

# Apply schemas
bq query --use_legacy_sql=false < services/pattern-mining/sql/schemas.sql

# Create BI Engine reservation
bq mk --project_id=$PROJECT_ID \
  --location=US \
  --reservation \
  --slots=500 \
  --edition=ENTERPRISE_PLUS \
  pattern_detection_reservation

# Set up continuous queries
bq query --use_legacy_sql=false < services/pattern-mining/sql/continuous_queries.sql
```

## Application Deployment

### 1. Build and Push Docker Image
```bash
cd services/pattern-mining

# Build production image with GPU support
docker build \
  --platform linux/amd64 \
  --build-arg PYTHON_VERSION=3.13 \
  --build-arg ENABLE_GPU=true \
  -t gcr.io/${PROJECT_ID}/pattern-detection:v2.0.0 \
  -f Dockerfile.prod .

# Push to Container Registry
docker push gcr.io/${PROJECT_ID}/pattern-detection:v2.0.0
```

### 2. Kubernetes Deployment
```yaml
# k8s/pattern-detection/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pattern-detection-api
  namespace: pattern-detection
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: pattern-detection-api
  template:
    metadata:
      labels:
        app: pattern-detection-api
        version: v2.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: pattern-detection-sa
      
      # GPU scheduling
      nodeSelector:
        cloud.google.com/gke-accelerator: nvidia-tesla-a100
      
      tolerations:
      - key: nvidia.com/gpu
        operator: Equal
        value: present
        effect: NoSchedule
        
      containers:
      - name: api
        image: gcr.io/${PROJECT_ID}/pattern-detection:v2.0.0
        
        resources:
          requests:
            memory: "16Gi"
            cpu: "4"
            nvidia.com/gpu: 1
          limits:
            memory: "32Gi"
            cpu: "8"
            nvidia.com/gpu: 1
            
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: PROJECT_ID
          value: "${PROJECT_ID}"
        - name: ENABLE_GPU
          value: "true"
        - name: MODEL_CACHE_DIR
          value: "/models"
        - name: RAY_ADDRESS
          value: "ray://ray-head:10001"
        - name: BIGQUERY_DATASET
          value: "pattern_detection_v2"
        
        # Model loading optimization
        - name: TRANSFORMERS_CACHE
          value: "/models/transformers"
        - name: HF_DATASETS_CACHE
          value: "/models/datasets"
        - name: TORCH_HOME
          value: "/models/torch"
        
        # Performance tuning
        - name: OMP_NUM_THREADS
          value: "8"
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 9090
          name: metrics
          
        volumeMounts:
        - name: model-cache
          mountPath: /models
        - name: service-account-key
          mountPath: /secrets
          readOnly: true
          
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          
        readinessProbe:
          httpGet:
            path: /api/v1/ready
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 5
          successThreshold: 1
          failureThreshold: 3
          
        startupProbe:
          httpGet:
            path: /api/v1/startup
            port: 8000
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 30
          
      initContainers:
      # Pre-load ML models
      - name: model-loader
        image: gcr.io/${PROJECT_ID}/pattern-detection:v2.0.0
        command: ["python", "-m", "src.ml.model_loader"]
        volumeMounts:
        - name: model-cache
          mountPath: /models
          
      volumes:
      - name: model-cache
        persistentVolumeClaim:
          claimName: model-cache-pvc
      - name: service-account-key
        secret:
          secretName: pattern-detection-sa-key
```

### 3. Service and Ingress
```yaml
# k8s/pattern-detection/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: pattern-detection-api
  namespace: pattern-detection
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "pattern-detection-backend-config"}'
spec:
  type: ClusterIP
  selector:
    app: pattern-detection-api
  ports:
  - name: http
    port: 80
    targetPort: 8000
  - name: metrics
    port: 9090
    targetPort: 9090

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pattern-detection-ingress
  namespace: pattern-detection
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "pattern-detection-ip"
    networking.gke.io/managed-certificates: "pattern-detection-cert"
    kubernetes.io/ingress.allow-http: "false"
spec:
  rules:
  - host: patterns.episteme.ai
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: pattern-detection-api
            port:
              number: 80
```

### 4. Horizontal Pod Autoscaler
```yaml
# k8s/pattern-detection/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: pattern-detection-hpa
  namespace: pattern-detection
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: pattern-detection-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: pattern_detection_queue_depth
      target:
        type: AverageValue
        averageValue: "30"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30
      - type: Pods
        value: 4
        periodSeconds: 30
      selectPolicy: Max
```

## Ray Cluster Deployment

### 1. Ray Head Node
```yaml
# k8s/ray/ray-head.yaml
apiVersion: v1
kind: Service
metadata:
  name: ray-head
  namespace: pattern-detection
spec:
  type: ClusterIP
  selector:
    component: ray-head
  ports:
  - name: client
    port: 10001
    targetPort: 10001
  - name: dashboard
    port: 8265
    targetPort: 8265

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ray-head
  namespace: pattern-detection
spec:
  replicas: 1
  selector:
    matchLabels:
      component: ray-head
  template:
    metadata:
      labels:
        component: ray-head
    spec:
      containers:
      - name: ray-head
        image: rayproject/ray:2.47.0-py313-gpu
        command: ["ray", "start", "--head", "--dashboard-host=0.0.0.0"]
        resources:
          requests:
            cpu: 4
            memory: 8Gi
          limits:
            cpu: 8
            memory: 16Gi
        ports:
        - containerPort: 10001
        - containerPort: 8265
```

### 2. Ray Worker Nodes
```yaml
# k8s/ray/ray-worker.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ray-worker-gpu
  namespace: pattern-detection
spec:
  replicas: 3
  selector:
    matchLabels:
      component: ray-worker-gpu
  template:
    metadata:
      labels:
        component: ray-worker-gpu
    spec:
      nodeSelector:
        cloud.google.com/gke-accelerator: nvidia-tesla-a100
      containers:
      - name: ray-worker
        image: rayproject/ray:2.47.0-py313-gpu
        command: ["ray", "start", "--address=ray-head:10001"]
        resources:
          requests:
            cpu: 4
            memory: 16Gi
            nvidia.com/gpu: 1
          limits:
            cpu: 8
            memory: 32Gi
            nvidia.com/gpu: 1
```

## Monitoring Setup

### 1. Prometheus Configuration
```yaml
# k8s/monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      
    scrape_configs:
    - job_name: 'pattern-detection'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - pattern-detection
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
```

### 2. Grafana Dashboard
```json
{
  "dashboard": {
    "title": "Pattern Detection Service",
    "panels": [
      {
        "title": "API Latency (p95)",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(pattern_detection_api_latency_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "GPU Utilization",
        "targets": [
          {
            "expr": "avg(pattern_detection_gpu_utilization_percent)"
          }
        ]
      },
      {
        "title": "Pattern Detection Rate",
        "targets": [
          {
            "expr": "rate(pattern_detections_total[5m])"
          }
        ]
      },
      {
        "title": "Model Confidence Distribution",
        "targets": [
          {
            "expr": "histogram_quantile(0.5, rate(pattern_detection_confidence_bucket[5m]))"
          }
        ]
      }
    ]
  }
}
```

## Production Checklist

### Pre-deployment
- [ ] All tests passing (>95% coverage)
- [ ] Security scan completed (Trivy, Snyk)
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Runbook created

### Infrastructure
- [ ] GKE cluster deployed
- [ ] GPU nodes available
- [ ] BigQuery datasets created
- [ ] Pub/Sub topics configured
- [ ] Service accounts created
- [ ] SSL certificates provisioned

### Application
- [ ] Docker images built and pushed
- [ ] Kubernetes manifests applied
- [ ] Services exposed via Ingress
- [ ] Autoscaling configured
- [ ] Health checks passing

### ML Models
- [ ] Models downloaded and cached
- [ ] Model registry populated
- [ ] A/B testing configured
- [ ] Fallback models available

### Monitoring
- [ ] Prometheus scraping metrics
- [ ] Grafana dashboards created
- [ ] Alerts configured
- [ ] Log aggregation working
- [ ] Traces being collected

### Security
- [ ] Network policies applied
- [ ] Secrets encrypted
- [ ] RBAC configured
- [ ] Pod security policies enforced
- [ ] Vulnerability scanning enabled

## Rollback Procedure

```bash
# Quick rollback to previous version
kubectl rollout undo deployment/pattern-detection-api -n pattern-detection

# Rollback to specific revision
kubectl rollout undo deployment/pattern-detection-api --to-revision=2 -n pattern-detection

# Check rollout status
kubectl rollout status deployment/pattern-detection-api -n pattern-detection

# Verify health
kubectl get pods -n pattern-detection
kubectl logs -l app=pattern-detection-api -n pattern-detection --tail=100
```

## Troubleshooting

### Common Issues

1. **GPU Not Available**
```bash
# Check GPU nodes
kubectl get nodes -l cloud.google.com/gke-accelerator=nvidia-tesla-a100

# Verify GPU drivers
kubectl exec -it <pod-name> -- nvidia-smi
```

2. **Model Loading Failures**
```bash
# Check model cache
kubectl exec -it <pod-name> -- ls -la /models

# Force model download
kubectl exec -it <pod-name> -- python -m src.ml.model_loader --force
```

3. **BigQuery Connection Issues**
```bash
# Verify service account
kubectl get serviceaccount pattern-detection-sa -n pattern-detection -o yaml

# Check credentials
kubectl exec -it <pod-name> -- gcloud auth list
```

## Performance Tuning

### 1. GPU Optimization
```python
# src/config/gpu_config.py
GPU_CONFIG = {
    "mixed_precision": True,
    "memory_growth": True,
    "allow_tf32": True,
    "flash_attention": True,
    "compile_mode": "max-autotune"
}
```

### 2. Caching Strategy
```yaml
# Redis configuration
redis:
  maxmemory: 10gb
  maxmemory-policy: allkeys-lru
  save: ""  # Disable persistence for cache
```

### 3. Connection Pooling
```python
# src/config/database.py
POOL_CONFIG = {
    "min_size": 10,
    "max_size": 100,
    "max_queries": 50000,
    "max_inactive_connection_lifetime": 300
}
```

## Success Metrics

- **Availability**: 99.9% uptime
- **Latency**: p95 < 25ms
- **Throughput**: 1000+ requests/second
- **GPU Utilization**: >70%
- **Cost**: <$0.02 per repository
- **Accuracy**: >97% precision

## Next Steps

1. Enable canary deployments
2. Implement chaos engineering tests
3. Set up disaster recovery
4. Configure multi-region deployment
5. Implement cost optimization automation