# Episteme AI Development Environment (`.claude/`)

This directory contains the core configuration, prompts, and memory for the AI-driven development of the Episteme (CCL) platform. It is managed by the **Gemini Project Lead** to orchestrate the work of all specialized **Claude Code Agents**.

## Directory Structure

The directory is organized to support a structured, multi-agent development workflow:

-   **`/config/`**: Contains static configuration for the AI development system, such as context loading priorities and integration settings.
-   **`/logs/`**: Stores logs from validation runs and integration tests. This is used by the Gemini Project Lead to monitor system health.
-   **`/memory/`**: The persistent memory of the AI team. It stores session data, task history, and learned patterns to ensure context continuity.
-   **`/personas/`**: Defines the specific roles, expertise, and operational parameters for each of the specialized Claude Code Agents (e.g., `architect.yaml`, `backend-dev.yaml`).
-   **`/prompts/`**: A library of reusable command prompts and workflow definitions that the Gemini Project Lead uses to direct the Claude agents.
    -   **`/prompts/commands/`**: Contains detailed instructions for high-level commands like `/orchestrate` or `/sparc`.
-   **`/scripts/`**: Utility scripts for managing the AI development environment, such as context loading and session management.

## Operational Framework

The development process is governed by the principles outlined in the root `CLAUDE.md` file. The key components are:

1.  **Gemini Project Lead**: The master orchestrator (currently, you are interacting with this agent). It manages the project roadmap, assigns tasks, validates outputs, and ensures adherence to the architecture.
2.  **Claude Code Agents**: A team of specialized AI agents that execute development tasks within their defined personas (e.g., Frontend, Backend, DevOps).
3.  **SPARC Workflow**: A structured methodology (Specification, Pseudocode, Architecture, Refinement, Completion) used for all major feature development.

## How It Works

1.  **Planning**: The Gemini Project Lead defines the work to be done in `ROADMAP.md` and breaks it down into actionable items in `TASK.md`.
2.  **Task Assignment**: Gemini selects a task from `TASK.md` and assigns it to the appropriate Claude agent persona, providing it with the necessary context from the project files and memory.
3.  **Execution**: The Claude agent executes the task, following the SPARC workflow and adhering to the project's technical standards.
4.  **Validation**: Throughout the process, Gemini runs automated validation scripts (`make validate-*`) to ensure quality and provide rapid feedback.
5.  **Completion**: Once a feature is complete, Gemini performs a final review, prepares a pull request, and merges it upon successful validation.

This system is designed to create a highly efficient, structured, and quality-focused AI development lifecycle.
