{"mcpServers": {"github": {"command": "npx", "args": ["@modelcontextprotocol/server-github"], "env": {"GITHUB_TOKEN": "${GITHUB_TOKEN}"}}, "puppeteer": {"command": "npx", "args": ["@modelcontextprotocol/server-puppeteer"]}, "sequential-thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"]}, "google": {"command": "npx", "args": ["@modelcontextprotocol/server-google"], "env": {"GOOGLE_API_KEY": "${GOOGLE_API_KEY}"}}, "postgres": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres"], "env": {"DATABASE_URL": "${DATABASE_URL}"}}, "gcp-mcp": {"command": "npx", "args": ["@google-cloud/gcp-mcp-server"], "env": {"GOOGLE_APPLICATION_CREDENTIALS": "/Users/<USER>/Documents/GitHub/episteme/vibe-match-463114-dbda8d8a6cb9.json", "GOOGLE_CLOUD_PROJECT": "vibe-match-463114"}}, "vertex-ai-server": {"command": "node", "args": ["/Users/<USER>/Documents/GitHub/episteme/node_modules/vertex-ai-mcp-server/build/index.js"], "env": {"AI_PROVIDER": "vertex", "GOOGLE_CLOUD_PROJECT": "vibe-match-463114", "GOOGLE_CLOUD_LOCATION": "us-central1", "GOOGLE_APPLICATION_CREDENTIALS": "/Users/<USER>/Documents/GitHub/episteme/vibe-match-463114-dbda8d8a6cb9.json", "AI_TEMPERATURE": "0.0", "AI_USE_STREAMING": "true", "AI_MAX_OUTPUT_TOKENS": "65536", "AI_MAX_RETRIES": "3", "AI_RETRY_DELAY_MS": "1000"}}}}