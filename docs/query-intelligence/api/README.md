# Query Intelligence API Reference

## Overview

The Query Intelligence service provides a comprehensive API for natural language code querying. The API supports both REST and WebSocket endpoints for different use cases:

- **REST API**: Synchronous queries with complete responses
- **WebSocket API**: Streaming queries with real-time response chunks
- **Admin API**: System monitoring and management (admin only)

## Base URL

```
Production: https://query-intelligence.ccl.dev
Development: http://localhost:8002
```

## Authentication

### JWT Token Authentication

All API endpoints require authentication via JWT tokens in the Authorization header:

```http
Authorization: Bearer <jwt_token>
```

### Token Structure

```json
{
  "sub": "user_id",
  "iat": **********,
  "exp": **********,
  "roles": ["user", "admin"],
  "repository_access": ["repo-123", "repo-456"]
}
```

### Admin Access

Admin-only endpoints require the `admin` role in the JWT token. Admin endpoints provide system monitoring, cache management, and circuit breaker control.

## Health and Status Endpoints

### Health Check

```http
GET /health
```

Basic liveness probe for the service.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-10T10:30:00Z",
  "version": "1.0.0"
}
```

### Readiness Check

```http
GET /ready
```

Comprehensive readiness check including all dependencies.

**Response:**
```json
{
  "status": "ready",
  "services": {
    "redis": "healthy",
    "analysis_engine": "healthy",
    "pattern_mining": "healthy",
    "llm_service": "healthy",
    "pinecone": "healthy"
  },
  "timestamp": "2025-07-10T10:30:00Z"
}
```

### Circuit Breaker Status

```http
GET /circuit-breakers
```

Current status of all circuit breakers.

**Response:**
```json
{
  "circuit_breakers": {
    "analysis_engine": {
      "state": "CLOSED",
      "failure_count": 0,
      "last_failure": null,
      "next_attempt": null
    },
    "pattern_mining": {
      "state": "CLOSED", 
      "failure_count": 0,
      "last_failure": null,
      "next_attempt": null
    },
    "llm_service": {
      "state": "HALF_OPEN",
      "failure_count": 2,
      "last_failure": "2025-07-10T10:25:00Z",
      "next_attempt": "2025-07-10T10:30:00Z"
    }
  }
}
```

## Rate Limiting

### Rate Limit Headers

All API responses include rate limiting headers:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
X-RateLimit-Window: 60
```

### Rate Limit Configuration

- **Query Endpoint**: 50 requests per minute per user
- **Admin Endpoints**: 10 requests per minute per admin user
- **WebSocket**: 5 concurrent connections per user

### Rate Limit Exceeded Response

```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please try again later.",
  "retry_after": 45,
  "limit": 100,
  "window": 60
}
```

## Error Handling

### Standard Error Response

```json
{
  "error": "error_code",
  "message": "Human readable error message",
  "details": {
    "field": "Additional error details"
  },
  "timestamp": "2025-07-10T10:30:00Z",
  "request_id": "req-123456"
}
```

### HTTP Status Codes

- `200 OK` - Successful request
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Missing or invalid authentication
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Service error
- `503 Service Unavailable` - Service temporarily unavailable

### Error Codes

| Code | Description |
|------|-------------|
| `AUTH_FAILED` | Authentication failed |
| `INVALID_TOKEN` | JWT token is invalid or expired |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| `RATE_LIMIT_EXCEEDED` | Rate limit exceeded |
| `VALIDATION_ERROR` | Request validation failed |
| `QUERY_TOO_LONG` | Query exceeds maximum length |
| `REPOSITORY_NOT_FOUND` | Repository not accessible |
| `SERVICE_UNAVAILABLE` | External service unavailable |
| `CIRCUIT_BREAKER_OPEN` | Circuit breaker protection activated |
| `PROCESSING_TIMEOUT` | Query processing timed out |

## Common Request/Response Models

### Query Request Model

```json
{
  "query": "How does the authentication middleware work?",
  "repository_id": "repo-123",
  "session_id": "session-456",
  "user_id": "user-789",
  "filters": {
    "file_pattern": "*.py",
    "exclude_tests": true,
    "exclude_patterns": ["__pycache__", "*.pyc"],
    "include_patterns": ["src/**", "lib/**"],
    "language": "python",
    "max_file_size": 1000000
  },
  "options": {
    "stream": false,
    "max_results": 10,
    "include_snippets": true,
    "confidence_threshold": 0.7
  }
}
```

### Code Reference Model

```json
{
  "file_path": "src/middleware/auth.py",
  "start_line": 45,
  "end_line": 67,
  "snippet": "class JWTAuth:\n    def __init__(self):\n        self.secret = get_secret()\n        self.algorithm = 'HS256'",
  "relevance_score": 0.95,
  "language": "python",
  "file_size": 2048,
  "last_modified": "2025-07-10T08:30:00Z"
}
```

### Intent Analysis Model

```json
{
  "primary_intent": "EXPLAIN",
  "confidence": 0.92,
  "secondary_intents": ["FIND", "ANALYZE"],
  "complexity": "MEDIUM",
  "domain": "AUTHENTICATION",
  "keywords": ["middleware", "auth", "JWT"],
  "question_type": "HOW",
  "scope": "SPECIFIC"
}
```

### Response Metadata Model

```json
{
  "model_used": "gemini-2.5-flash",
  "chunks_retrieved": 15,
  "chunks_used": 5,
  "cache_hit": false,
  "execution_time_ms": 87.3,
  "search_time_ms": 23.1,
  "generation_time_ms": 64.2,
  "token_usage": {
    "prompt_tokens": 1200,
    "completion_tokens": 350,
    "total_tokens": 1550
  }
}
```

## Pagination

For endpoints that return lists, pagination is supported:

### Pagination Parameters

```http
GET /api/v1/queries?page=2&limit=20&sort=created_at&order=desc
```

### Pagination Response

```json
{
  "data": [...],
  "pagination": {
    "page": 2,
    "limit": 20,
    "total": 150,
    "pages": 8,
    "has_next": true,
    "has_prev": true
  }
}
```

## WebSocket Connection Management

### Connection Lifecycle

1. **Connect**: Client initiates WebSocket connection
2. **Authenticate**: Optional JWT token validation
3. **Query**: Send query requests and receive streaming responses
4. **Disconnect**: Clean connection closure

### WebSocket Message Types

| Type | Direction | Description |
|------|-----------|-------------|
| `acknowledged` | Server→Client | Query received and processing started |
| `processing_started` | Server→Client | Query processing initiated |
| `intent_analyzed` | Server→Client | Intent analysis completed |
| `status` | Server→Client | Processing status update |
| `search_complete` | Server→Client | Code search completed |
| `reference` | Server→Client | Code reference found |
| `text` | Server→Client | Response text chunk |
| `done` | Server→Client | Query processing completed |
| `error` | Server→Client | Error occurred |

### WebSocket Error Codes

| Code | Description |
|------|-------------|
| `1000` | Normal closure |
| `1008` | Policy violation (authentication failed) |
| `1011` | Internal server error |
| `1013` | Service restart |

## Security Considerations

### Input Validation

- Maximum query length: 1000 characters
- SQL injection prevention
- XSS protection
- Prompt injection detection
- PII detection and redaction

### Security Headers

```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Content-Security-Policy: default-src 'self'
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

### Audit Logging

All API requests are logged with the following information:
- User ID and IP address
- Request timestamp and duration
- Endpoint accessed and HTTP method
- Response status and size
- Authentication status
- Rate limiting status

### Data Privacy

- No query content is logged in production
- User data is encrypted in transit and at rest
- PII is automatically detected and redacted
- Data retention policies are enforced

## Performance Optimization

### Caching Strategy

- **Query Results**: Semantic similarity caching (24 hours)
- **Code Embeddings**: Long-term caching (7 days)
- **Intent Analysis**: Short-term caching (1 hour)
- **User Sessions**: Session-based caching

### Connection Pooling

- HTTP connections are pooled and reused
- WebSocket connections are load-balanced
- Circuit breakers prevent cascade failures
- Automatic retry with exponential backoff

### Monitoring

- Response time tracking (<100ms target)
- Error rate monitoring (<0.1% target)
- Cache hit rate optimization (>75% target)
- Resource utilization monitoring

## API Versioning

### Version Header

```http
Accept: application/vnd.ccl.v1+json
```

### Version in URL

```http
GET /api/v1/query
```

### Supported Versions

- **v1**: Current stable version
- **v2**: Future version (in development)

### Deprecation Policy

- 6 months notice for breaking changes
- 3 months support for deprecated versions
- Clear migration documentation provided

## SDK and Client Libraries

### Official SDKs

- **Python**: `ccl-query-intelligence-python`
- **JavaScript/TypeScript**: `@ccl/query-intelligence`
- **Go**: `github.com/ccl/query-intelligence-go`

### Example SDK Usage

```python
from ccl_query_intelligence import QueryIntelligenceClient

client = QueryIntelligenceClient(
    base_url="https://query-intelligence.ccl.dev",
    api_key="your-jwt-token"
)

result = await client.query(
    "How does authentication work?",
    repository_id="repo-123"
)
```

## Testing and Development

### API Testing

- Comprehensive test suite available
- Mock endpoints for development
- Load testing scripts provided
- Security testing included

### Development Environment

```bash
# Start development server
poetry run uvicorn query_intelligence.main:app --reload --port 8002

# Run API tests
poetry run pytest tests/integration/test_api.py

# Load test
poetry run locust -f tests/load/api_load_test.py
```

---

**Last Updated**: July 2025  
**API Version**: v1.0.0  
**Contact**: <EMAIL>