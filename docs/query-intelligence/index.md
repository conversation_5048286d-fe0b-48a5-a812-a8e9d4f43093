# Query Intelligence Service Documentation

## Overview

The Query Intelligence service is a production-ready Python microservice that enables natural language query processing for the CCL (Codebase Context Layer) platform. It allows developers to ask questions about their codebase in plain English and receive intelligent, context-aware responses.

## ✅ Production Status

**The Query Intelligence service is production-ready with 95% completion status as of July 2025.**

### Key Achievements
- ✅ Google GenAI SDK migration completed
- ✅ Gemini 2.5 models integrated (Flash, Flash-Lite, Pro)
- ✅ Security hardening with zero critical vulnerabilities
- ✅ Performance optimization achieving <100ms response times
- ✅ Comprehensive test coverage (81%, targeting 90%)
- ✅ Multi-language support (15+ languages)
- ✅ Real-time streaming via WebSocket

## Documentation Structure

### 📚 Core Documentation
- **[API Reference](api/)** - Complete REST and WebSocket API documentation
- **[Architecture](architecture/)** - System design and integration patterns
- **[Developer Guide](guides/developer-guide.md)** - Development setup and contribution guidelines
- **[Operations Runbook](operations-runbook.md)** - Production operations and monitoring

### 🔧 Implementation Guides
- **[Integration Guide](guides/integration-guide.md)** - Integration patterns with other services
- **[Deployment Guide](guides/deployment-guide.md)** - Cloud Run deployment procedures
- **[Performance Tuning](guides/performance-tuning.md)** - Optimization strategies
- **[Security Guide](guides/security-guide.md)** - Security implementation and best practices

### 🐛 Troubleshooting
- **[Common Issues](troubleshooting/)** - Debugging procedures and solutions
- **[Performance Issues](troubleshooting/performance-issues.md)** - Performance debugging
- **[Integration Issues](troubleshooting/integration-issues.md)** - Integration problem resolution

## Quick Start

### Prerequisites
- Python 3.11+
- Poetry for dependency management
- Docker and Docker Compose
- Google Cloud SDK
- Redis (for local development)

### Local Development
```bash
# Clone and setup
git clone https://github.com/episteme/ccl.git
cd ccl/services/query-intelligence

# Install dependencies
poetry install

# Start Redis
docker run -d -p 6379:6379 redis:7-alpine

# Run the service
poetry run uvicorn query_intelligence.main:app --reload --port 8002
```

### Testing
```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=query_intelligence --cov-report=html

# Run specific categories
poetry run pytest tests/unit/
poetry run pytest tests/integration/
```

## Key Features

### Core Capabilities
- **Natural Language Understanding**: 95%+ accuracy query processing
- **Semantic Search**: Vector embeddings with <50ms retrieval
- **Model Intelligence**: Automatic routing between Gemini model tiers
- **Streaming Responses**: Real-time WebSocket API
- **Multi-language Support**: 15+ programming languages
- **Query Optimization**: Real-time improvement suggestions

### Performance Metrics
- **Response Time**: <100ms (p95) ✅ Achieved
- **Availability**: 99.95% uptime target
- **Error Rate**: <0.1% (currently 0.05%)
- **Cold Start**: <2s with CPU boost
- **Throughput**: 1000+ QPS tested

### Security Features
- **Authentication**: JWT-based with service account support
- **Input Validation**: Comprehensive sanitization
- **Threat Detection**: Prompt injection, PII, SQL injection prevention
- **Rate Limiting**: Per-user Redis-based throttling
- **Audit Logging**: Complete compliance logging

## Service Integration

### Dependencies
- **analysis-engine**: Code parsing and AST analysis
- **pattern-mining**: ML-based pattern detection
- **Redis**: Caching and rate limiting
- **Pinecone**: Vector similarity search
- **Google GenAI**: LLM inference (Gemini 2.5 models)

### Circuit Breaker Protection
All external services are protected with configurable circuit breakers:
- Analysis Engine: 3 failures → 30s recovery
- Pattern Mining: 3 failures → 60s recovery
- Redis: 5 failures → 30s recovery
- LLM Service: 3 failures → 60s recovery

## Technology Stack

### Runtime Environment
- **Language**: Python 3.11+
- **Framework**: FastAPI (async web framework)
- **AI/ML**: Google GenAI SDK (unified SDK)
- **Models**: Gemini 2.5 (Flash, Flash-Lite, Pro)
- **Deployment**: Cloud Run Gen2 with CPU boost

### Data Layer
- **Vector Database**: Pinecone for semantic search
- **Cache**: Redis with multi-level caching
- **Embeddings**: Sentence Transformers (all-mpnet-base-v2)
- **Monitoring**: Prometheus metrics, structured logging

## Production Deployment

### Cloud Run Configuration
```yaml
# Optimized Cloud Run settings
min_instances: 5
max_instances: 200
memory: 16Gi
cpu: 4
concurrency: 20
cpu_boost: true
execution_environment: gen2
```

### Environment Variables
```bash
# Production configuration
ENVIRONMENT=production
USE_VERTEX_AI=true
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
SERVICE_ACCOUNT_PATH=/var/secrets/service-account.json
USE_SECRET_MANAGER=true
MIN_INSTANCES=5
MAX_INSTANCES=200
SEMANTIC_CACHE_ENABLED=true
```

## Monitoring and Observability

### Health Endpoints
- `/health` - Basic liveness check
- `/ready` - Readiness including all dependencies
- `/metrics` - Prometheus metrics endpoint
- `/circuit-breakers` - Circuit breaker status

### Key Metrics
- `query_intelligence_queries_total` - Total queries by intent/status
- `query_intelligence_query_duration_seconds` - Query processing duration
- `query_intelligence_model_latency_seconds` - Model inference time
- `query_intelligence_cache_hit_rate` - Semantic cache effectiveness
- `query_intelligence_token_usage_total` - Token consumption by model

### Alerting Rules
```yaml
# Critical alerts
- alert: HighQueryLatency
  expr: query_intelligence_query_duration_seconds{quantile="0.95"} > 0.1
  
- alert: HighErrorRate  
  expr: rate(query_intelligence_queries_total{status="error"}[5m]) > 0.01
  
- alert: ModelQuotaExhaustion
  expr: rate(query_intelligence_model_errors_total{type="quota"}[5m]) > 0
```

## Documentation Status

### ✅ Complete Documentation
- Service implementation and architecture
- API endpoints and data models
- Security and performance guides
- Production deployment procedures
- Monitoring and observability

### 🔄 In Progress
- Enhanced API documentation with OpenAPI specs
- Advanced troubleshooting guides
- Performance optimization case studies
- Integration pattern examples

### 📋 Planned
- Visual architecture diagrams
- Migration guides for major updates
- Best practices compendium
- Advanced configuration examples

## Contributing

### Development Standards
1. Use `google-genai` SDK only (no deprecated `vertexai` imports)
2. Follow coding standards in PLANNING.md
3. Maintain >90% test coverage
4. Update documentation for API changes
5. Run security scans before committing

### Code Quality
```bash
# Format and lint
poetry run black src/ tests/
poetry run ruff check src/ tests/ --fix
poetry run mypy src/

# Security scanning
poetry run bandit -r src/ -ll
poetry run safety check
```

## Support and Contact

- **Documentation Issues**: [Create an issue](https://github.com/episteme/ccl/issues)
- **Production Support**: <EMAIL>
- **Development Questions**: #query-intelligence Slack channel

---

**Last Updated**: July 2025  
**Production Status**: Ready for deployment (95% complete)  
**Next Review**: August 2025