# Query Intelligence Integration Issues Troubleshooting Guide

## Overview

This guide helps resolve integration problems between Query Intelligence and other services, including external APIs and internal microservices.

## External Service Integration Issues

### 1. Google GenAI API Errors

#### Rate Limit Exceeded

**Symptoms:**
```json
{
  "error": {
    "code": 429,
    "message": "Resource has been exhausted (e.g., check quota)",
    "status": "RESOURCE_EXHAUSTED"
  }
}
```

**Diagnostic Steps:**
```bash
# Check current quota usage
gcloud services quota list --service=generativelanguage.googleapis.com \
  --filter="metric.name:requests_per_minute"

# Monitor API errors
gcloud logs read "jsonPayload.service='google_genai' AND \
  jsonPayload.status_code=429" \
  --limit=50 --format=json | \
  jq '.[] | {time: .timestamp, model: .jsonPayload.model, error: .jsonPayload.error}'
```

**Resolution:**

1. **Implement Exponential Backoff**
```python
# Retry configuration
RETRY_CONFIG = {
    "max_attempts": 5,
    "initial_delay": 1.0,
    "max_delay": 60.0,
    "multiplier": 2.0,
    "jitter": 0.1
}
```

2. **Enable Request Batching**
```bash
# Configure batching
gcloud run services update query-intelligence \
  --set-env-vars="ENABLE_REQUEST_BATCHING=true,\
    BATCH_SIZE=10,\
    BATCH_TIMEOUT_MS=100"
```

3. **Upgrade Quota**
```bash
# Request quota increase
gcloud alpha services quota update \
  --service=generativelanguage.googleapis.com \
  --quota=requests_per_minute \
  --value=1000
```

#### Authentication Failures

**Symptoms:**
```json
{
  "error": {
    "code": 401,
    "message": "Request had invalid authentication credentials",
    "status": "UNAUTHENTICATED"
  }
}
```

**Resolution:**
```bash
# For Vertex AI backend (production)
gcloud iam service-accounts keys create key.json \
  --iam-account=query-intelligence-sa@${PROJECT_ID}.iam.gserviceaccount.com

gcloud run services update query-intelligence \
  --set-env-vars="GOOGLE_APPLICATION_CREDENTIALS=/secrets/service-account/key.json"

# For Gemini Developer API (development)
gcloud secrets create google-api-key --data-file=api-key.txt
gcloud run services update query-intelligence \
  --set-secrets="GOOGLE_API_KEY=google-api-key:latest"
```

#### Model Not Found

**Symptoms:**
```
Model 'gemini-1.5-pro' not found or access denied
```

**Resolution:**
```bash
# Update to available models (as of July 2025)
gcloud run services update query-intelligence \
  --set-env-vars="GEMINI_MODEL_NAME=gemini-2.5-flash,\
    SIMPLE_QUERY_MODEL=gemini-2.5-flash-lite,\
    COMPLEX_QUERY_MODEL=gemini-2.5-pro"
```

### 2. Redis Connection Problems

#### Connection Refused

**Symptoms:**
- Health check: `"redis": "connection_error"`
- Logs: `redis.exceptions.ConnectionError: Connection refused`

**Diagnostic Steps:**
```bash
# Test Redis connectivity
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping

# Check Redis URL format
echo $REDIS_URL  # Expected: redis://[password@]host:port[/db]

# Verify network connectivity
gcloud compute ssh instance-name -- nc -zv redis-host 6379
```

**Resolution:**

1. **Fix Connection String**
```bash
# Correct format examples
# Without auth: redis://redis.example.com:6379
# With auth: redis://default:<EMAIL>:6379
# With TLS: rediss://redis.example.com:6379

gcloud run services update query-intelligence \
  --set-env-vars="REDIS_URL=redis://********:6379"
```

2. **Configure VPC Connector (for private Redis)**
```bash
gcloud compute networks vpc-access connectors create redis-connector \
  --network=default \
  --region=us-central1 \
  --range=********/28

gcloud run services update query-intelligence \
  --vpc-connector=redis-connector
```

#### Connection Pool Exhaustion

**Symptoms:**
- `redis.exceptions.ConnectionError: Too many connections`
- Intermittent cache failures

**Resolution:**
```bash
# Monitor connection count
redis-cli CLIENT LIST | grep -c query-intelligence

# Increase pool size
gcloud run services update query-intelligence \
  --set-env-vars="REDIS_MAX_CONNECTIONS=100,\
    REDIS_CONNECTION_POOL_KWARGS='{\"max_connections\": 100, \"decode_responses\": true}'"

# Enable connection pooling cleanup
redis-cli CONFIG SET timeout 300
```

### 3. Pinecone Vector Database Failures

#### Index Not Found

**Symptoms:**
```
pinecone.core.client.exceptions.NotFoundException: Index 'ccl-code-embeddings' not found
```

**Resolution:**
```python
# Create index with correct configuration
import pinecone

pinecone.init(api_key=PINECONE_API_KEY)
pinecone.create_index(
    name="ccl-code-embeddings",
    dimension=768,  # Must match embedding model output
    metric="cosine",
    pods=1,
    replicas=1,
    pod_type="p1.x1"
)
```

#### Dimension Mismatch

**Symptoms:**
```
ValueError: Dimension of vectors (768) does not match index dimension (1536)
```

**Resolution:**
```bash
# Ensure embedding model matches index dimension
gcloud run services update query-intelligence \
  --set-env-vars="EMBEDDING_MODEL_NAME=sentence-transformers/all-mpnet-base-v2"
# This model outputs 768-dimensional vectors
```

## Internal Service Integration Issues

### 1. Analysis Engine Failures

#### Circuit Breaker OPEN

**Symptoms:**
```json
{
  "detail": "Circuit breaker 'analysis_engine' is OPEN",
  "circuit_breakers": {
    "analysis_engine": {
      "state": "open",
      "failure_count": 5,
      "last_failure_time": **********
    }
  }
}
```

**Diagnostic Steps:**
```bash
# Check Analysis Engine health
curl https://analysis-engine.ccl.dev/health

# Monitor circuit breaker events
gcloud logs read "jsonPayload.event='circuit_breaker_opened' AND \
  jsonPayload.name='analysis_engine'" \
  --limit=20

# Check for connection errors
gcloud logs read "jsonPayload.service='analysis_engine' AND \
  jsonPayload.error_type IN ('ConnectError', 'TimeoutException')" \
  --limit=50
```

**Resolution:**

1. **Adjust Circuit Breaker Settings**
```python
# More forgiving thresholds for Analysis Engine
ANALYSIS_ENGINE_CIRCUIT_BREAKER = {
    "failure_threshold": 10,      # Increased from 5
    "recovery_timeout": 30,       # Decreased from 60
    "success_threshold": 3,       # Consistent successes needed
    "expected_exception": httpx.HTTPError
}
```

2. **Implement Fallback Strategy**
```python
async def query_with_fallback(self, query: str):
    try:
        return await self.analysis_engine.analyze(query)
    except CircuitBreakerError:
        logger.warning("analysis_engine_fallback_triggered")
        # Use cached results or simplified analysis
        return await self.fallback_analyzer.analyze(query)
```

#### Timeout Errors

**Symptoms:**
- `httpx.TimeoutException: Request timeout after 20.0s`
- Partial results returned

**Resolution:**
```bash
# Increase timeout for complex queries
gcloud run services update query-intelligence \
  --set-env-vars="ANALYSIS_ENGINE_TIMEOUT=30,\
    QUERY_TIMEOUT_SECONDS=40"

# Implement request chunking for large payloads
```

### 2. Pattern Mining Service Issues

#### Service Unavailable

**Symptoms:**
- Health check: `"pattern_mining": "connection_error"`
- 503 errors from Pattern Mining

**Diagnostic Steps:**
```bash
# Check Pattern Mining deployment status
gcloud run services describe pattern-mining --region=us-central1

# Monitor Pattern Mining errors
gcloud logs read "resource.labels.service_name='pattern-mining' AND \
  severity>=ERROR" --limit=20

# Test direct connectivity
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  https://pattern-mining.ccl.dev/health
```

**Resolution:**
```bash
# Ensure Pattern Mining is deployed and scaled
gcloud run services update pattern-mining \
  --min-instances=3 \
  --max-instances=100

# Update service URL if changed
gcloud run services update query-intelligence \
  --set-env-vars="PATTERN_MINING_URL=https://pattern-mining-v2.ccl.dev"
```

## Network and Security Issues

### 1. CORS Errors

**Symptoms:**
```
Access to fetch at 'https://query-intelligence.ccl.dev/api/v1/query' from origin 
'https://app.ccl.dev' has been blocked by CORS policy
```

**Resolution:**
```bash
# Update CORS configuration
gcloud run services update query-intelligence \
  --set-env-vars="CORS_ALLOWED_ORIGINS=['https://app.ccl.dev','https://*.ccl.dev'],\
    CORS_ALLOW_CREDENTIALS=true,\
    CORS_ALLOWED_METHODS=['GET','POST','PUT','DELETE','OPTIONS'],\
    CORS_ALLOWED_HEADERS=['Content-Type','Authorization','X-Request-ID']"
```

### 2. Certificate Validation Errors

**Symptoms:**
```
ssl.SSLCertVerificationError: certificate verify failed: unable to get local issuer certificate
```

**Resolution:**
```bash
# For development (NOT for production)
gcloud run services update query-intelligence \
  --set-env-vars="SSL_VERIFY=false"

# For production - install CA certificates
RUN apt-get update && apt-get install -y ca-certificates
```

### 3. Firewall/IAM Issues

**Symptoms:**
- 403 Forbidden when calling internal services
- "Permission denied" errors

**Resolution:**
```bash
# Grant service account permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/run.invoker"

# Allow unauthenticated access (for public endpoints only)
gcloud run services add-iam-policy-binding query-intelligence \
  --member="allUsers" \
  --role="roles/run.invoker" \
  --region=us-central1
```

## WebSocket Integration Issues

### Connection Drops

**Symptoms:**
- WebSocket disconnects after ~60 seconds
- "Connection reset by peer" errors

**Resolution:**
```bash
# Increase WebSocket timeout
gcloud run services update query-intelligence \
  --timeout=3600 \
  --set-env-vars="WEBSOCKET_PING_INTERVAL=30,\
    WEBSOCKET_PING_TIMEOUT=10"
```

### Authentication on WebSocket

**Symptoms:**
- 401 errors on WebSocket upgrade
- "Missing authentication" in logs

**Resolution:**
```javascript
// Client-side WebSocket auth
const ws = new WebSocket('wss://query-intelligence.ccl.dev/api/v1/ws/query', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Or use query parameter
const ws = new WebSocket(`wss://query-intelligence.ccl.dev/api/v1/ws/query?token=${token}`);
```

## Integration Testing

### Health Check Script

```bash
#!/bin/bash
# integration-health-check.sh

echo "=== Query Intelligence Integration Health Check ==="

# Check Redis
echo -n "Redis: "
redis-cli -h $REDIS_HOST ping && echo "OK" || echo "FAILED"

# Check Google GenAI
echo -n "Google GenAI: "
curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://generativelanguage.googleapis.com/v1/models" \
  >/dev/null && echo "OK" || echo "FAILED"

# Check Analysis Engine
echo -n "Analysis Engine: "
curl -sf https://analysis-engine.ccl.dev/health >/dev/null && echo "OK" || echo "FAILED"

# Check Pattern Mining
echo -n "Pattern Mining: "
curl -sf https://pattern-mining.ccl.dev/health >/dev/null && echo "OK" || echo "FAILED"

# Check Pinecone
echo -n "Pinecone: "
curl -s -H "Api-Key: $PINECONE_API_KEY" \
  https://controller.$PINECONE_REGION.pinecone.io/databases \
  >/dev/null && echo "OK" || echo "FAILED"
```

### Integration Test Suite

```python
# test_integrations.py
import asyncio
import httpx
import redis
import pinecone
from google import generativeai as genai

async def test_all_integrations():
    results = {}
    
    # Test Redis
    try:
        r = redis.from_url(REDIS_URL)
        r.ping()
        results['redis'] = 'OK'
    except Exception as e:
        results['redis'] = f'FAILED: {str(e)}'
    
    # Test Google GenAI
    try:
        genai.configure(api_key=GOOGLE_API_KEY)
        list(genai.list_models())
        results['google_genai'] = 'OK'
    except Exception as e:
        results['google_genai'] = f'FAILED: {str(e)}'
    
    # Test internal services
    async with httpx.AsyncClient() as client:
        for service, url in [
            ('analysis_engine', ANALYSIS_ENGINE_URL),
            ('pattern_mining', PATTERN_MINING_URL)
        ]:
            try:
                resp = await client.get(f"{url}/health", timeout=5.0)
                resp.raise_for_status()
                results[service] = 'OK'
            except Exception as e:
                results[service] = f'FAILED: {str(e)}'
    
    return results
```

## Monitoring Integration Health

### Dashboards

Create monitoring dashboard with:
- Circuit breaker states
- External API error rates
- Integration latencies
- Connection pool metrics

### Alerts

```yaml
integration_alerts:
  - name: circuit_breaker_open
    condition: circuit_breaker_state == "open" for 5 minutes
    severity: critical
    channels: [pagerduty, slack]
    
  - name: redis_connection_failed
    condition: redis_health_check == "failed" for 2 minutes
    severity: critical
    
  - name: external_api_errors_high
    condition: error_rate{service="google_genai"} > 0.1 for 5 minutes
    severity: warning
    
  - name: integration_latency_high
    condition: p95_latency{operation="upstream_call"} > 5s for 5 minutes
    severity: warning
```

## Best Practices

1. **Always implement circuit breakers** for external services
2. **Use connection pooling** for database connections
3. **Implement retry logic** with exponential backoff
4. **Monitor integration health** continuously
5. **Have fallback strategies** for critical paths
6. **Test integrations** in staging before production
7. **Document API contracts** and version compatibility
8. **Use service mesh** for internal service communication (future)
9. **Implement request tracing** across services
10. **Keep integration timeouts** lower than request timeouts