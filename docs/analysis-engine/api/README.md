# 🔌 Analysis Engine API Documentation

## ✅ **Live Service Status**
**Service**: OPERATIONAL since July 2025  
**Base URL**: `http://localhost:8001`  
**Health Check**: `{"status":"healthy","service":"analysis-engine","version":"0.1.0"}`

## Table of Contents
- [🚀 Quick API Test](#quick-api-test)
- [✅ Working Endpoints](#working-endpoints)
- [⚠️ Limited Endpoints](#limited-endpoints)
- [Authentication](#authentication)
- [REST API](#rest-api)
  - [Health Endpoints](#health-endpoints)
  - [Analysis Endpoints](#analysis-endpoints)
  - [Pattern Endpoints](#pattern-endpoints)
- [WebSocket API](#websocket-api)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [Request/Response Examples](#requestresponse-examples)

## 🚀 Quick API Test

### **Test Live Service**
```bash
# Verify service is running
curl http://localhost:8001/health
# ✅ Response: {"status":"healthy","service":"analysis-engine","version":"0.1.0"}

# Test analysis endpoint  
curl -X POST http://localhost:8001/api/v1/analysis \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/example/test.git", "branch": "main"}'
# ✅ Response: {"analysis_id":"...","status":"Pending","created_at":"..."}
```

## ✅ Working Endpoints

### **Operational API Endpoints** (Verified)
- `GET /health` - **✅ WORKING** - Service health check
- `POST /api/v1/analysis` - **✅ WORKING** - Repository analysis
- `GET /api/v1/languages` - **✅ WORKING** - Supported languages (18+)

## ⚠️ Limited Endpoints

### **Endpoints with Known Issues**
- `GET /ready` - **⚠️ 503 Error** - Service dependency configuration needed
- `GET /metrics` - **⚠️ Not Configured** - Prometheus metrics setup pending
- AI-powered endpoints - **⚠️ IAM Permissions** - Requires service account role grant

### Base URLs
- **Live Service**: `http://localhost:8001/api/v1` ✅ **OPERATIONAL**
- **Production** (planned): `https://analysis-engine.ccl.dev/api/v1`
- **Staging** (planned): `https://analysis-engine-staging.ccl.dev/api/v1`

### Content Types
- Request: `application/json`
- Response: `application/json`
- WebSocket: `text` (JSON-encoded messages)

## Authentication

The API supports two authentication methods:

### 1. JWT Bearer Token (Recommended)
```http
Authorization: Bearer <jwt-token>
```

JWT tokens should be obtained from Firebase Auth and include the following claims:
```json
{
  "sub": "user-id",
  "email": "<EMAIL>",
  "email_verified": true,
  "iat": **********,
  "exp": **********
}
```

### 2. API Key
```http
X-API-Key: <api-key>
```

API keys are validated against the database and include rate limit information.

## REST API

### Health Endpoints

#### GET /health
Basic health check endpoint.

**Response**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### GET /health/ready
Readiness probe that checks all dependencies.

**Response**
```json
{
  "status": "ready",
  "checks": {
    "database": "ok",
    "storage": "ok",
    "cache": "ok",
    "vertex_ai": "ok"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### GET /health/live
Liveness probe for Kubernetes.

**Response**
```json
{
  "status": "alive",
  "uptime_seconds": 3600,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### GET /health/auth
Authentication debugging endpoint that provides information about the current authentication method and service availability.

**Response**
```json
{
  "auth_method": "service_account_file",
  "project_id": "vibe-match-463114",
  "environment": "development",
  "credentials_configured": true,
  "service_availability": {
    "spanner": true,
    "storage": true,
    "pubsub": true,
    "redis": false
  },
  "debug_info": {
    "credentials_path": "/path/to/credentials.json",
    "credentials_exists": "true",
    "service_version": "0.1.0"
  }
}
```

### Analysis Endpoints

#### POST /api/v1/analyze
Start a new repository analysis.

**Request Body**
```json
{
  "repository_url": "https://github.com/rust-lang/rust",
  "branch": "main",
  "patterns": ["authentication", "security", "database"],
  "languages": ["rust", "python"],
  "options": {
    "include_embeddings": true,
    "max_file_size": ********,
    "timeout_seconds": 300
  }
}
```

**Parameters**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `repository_url` | string | Yes | Git repository URL (HTTPS) |
| `branch` | string | No | Branch to analyze (default: main) |
| `patterns` | string[] | No | Pattern categories to detect |
| `languages` | string[] | No | Languages to analyze (empty = all) |
| `options.include_embeddings` | boolean | No | Generate embeddings (default: true) |
| `options.max_file_size` | integer | No | Max file size in bytes (default: 10MB) |
| `options.timeout_seconds` | integer | No | Analysis timeout (default: 300) |

**Response** (202 Accepted)
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "queued",
  "repository_url": "https://github.com/rust-lang/rust",
  "branch": "main",
  "created_at": "2024-01-01T00:00:00Z",
  "websocket_url": "wss://analysis-engine.ccl.dev/ws/progress/550e8400-e29b-41d4-a716-446655440000"
}
```

#### GET /api/v1/analyses/{analysis_id}
Retrieve analysis results.

**Response** (200 OK)
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "repository_url": "https://github.com/rust-lang/rust",
  "branch": "main",
  "commit_hash": "abc123def456",
  "created_at": "2024-01-01T00:00:00Z",
  "completed_at": "2024-01-01T00:05:00Z",
  "duration_seconds": 300,
  "statistics": {
    "total_files": 1523,
    "analyzed_files": 1420,
    "skipped_files": 103,
    "total_lines": 150000,
    "patterns_found": 42
  },
  "patterns": [
    {
      "pattern_id": "auth-001",
      "name": "authentication",
      "category": "security",
      "count": 15,
      "confidence": 0.95,
      "files": [
        {
          "path": "src/auth/handler.rs",
          "line": 42,
          "snippet": "fn authenticate(user: &User) -> Result<Token>",
          "language": "rust"
        }
      ]
    }
  ],
  "languages": {
    "rust": 1200,
    "python": 200,
    "javascript": 20
  },
  "embeddings_url": "gs://ccl-artifacts/analyses/550e8400/embeddings.json",
  "ast_url": "gs://ccl-artifacts/analyses/550e8400/ast.json"
}
```

#### GET /api/v1/analyses
List analyses for the authenticated user.

**Query Parameters**
| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number (default: 1) |
| `limit` | integer | Items per page (default: 20, max: 100) |
| `status` | string | Filter by status (queued, processing, completed, failed) |
| `repository_url` | string | Filter by repository URL |
| `created_after` | ISO 8601 | Filter by creation date |
| `created_before` | ISO 8601 | Filter by creation date |

**Response**
```json
{
  "analyses": [
    {
      "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
      "repository_url": "https://github.com/rust-lang/rust",
      "branch": "main",
      "status": "completed",
      "created_at": "2024-01-01T00:00:00Z",
      "patterns_count": 42
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "total_pages": 8
  }
}
```

#### DELETE /api/v1/analyses/{analysis_id}
Cancel or delete an analysis.

**Response** (204 No Content)

### Pattern Endpoints

#### GET /api/v1/patterns
List available patterns.

**Response**
```json
{
  "patterns": [
    {
      "pattern_id": "auth-001",
      "name": "authentication",
      "category": "security",
      "description": "Detects authentication implementations",
      "languages": ["rust", "python", "javascript"],
      "examples": [
        {
          "language": "rust",
          "code": "fn authenticate(user: &User) -> Result<Token>"
        }
      ]
    }
  ]
}
```

#### GET /api/v1/patterns/{pattern_id}
Get pattern details.

**Response**
```json
{
  "pattern_id": "auth-001",
  "name": "authentication",
  "category": "security",
  "description": "Detects authentication implementations",
  "ast_query": "(function_declaration name: (identifier) @fn_name (#match? @fn_name \"auth\"))",
  "languages": ["rust", "python", "javascript"],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "usage_count": 1523,
  "accuracy": 0.95
}
```

## WebSocket API

### Connection
```javascript
const ws = new WebSocket('wss://analysis-engine.ccl.dev/ws/progress/{analysis_id}');
```

### Message Format

**Progress Update**
```json
{
  "type": "progress",
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "percentage": 45.5,
  "message": "Analyzing file 683 of 1523",
  "current_file": "src/parser/mod.rs",
  "timestamp": "2024-01-01T00:02:30Z"
}
```

**Status Change**
```json
{
  "type": "status",
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "message": "Analysis completed successfully",
  "timestamp": "2024-01-01T00:05:00Z"
}
```

**Error**
```json
{
  "type": "error",
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "error": "Repository not found",
  "timestamp": "2024-01-01T00:00:30Z"
}
```

### Client Example
```javascript
const ws = new WebSocket(`wss://analysis-engine.ccl.dev/ws/progress/${analysisId}`);

ws.onopen = () => {
  console.log('Connected to progress tracker');
};

ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  
  switch (update.type) {
    case 'progress':
      updateProgressBar(update.percentage);
      updateStatusMessage(update.message);
      break;
    
    case 'status':
      handleStatusChange(update.status);
      break;
    
    case 'error':
      handleError(update.error);
      break;
  }
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

ws.onclose = () => {
  console.log('Disconnected from progress tracker');
};
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Please retry after 3600 seconds.",
    "details": {
      "limit": 1000,
      "remaining": 0,
      "reset_at": "2024-01-01T01:00:00Z"
    }
  },
  "request_id": "req_123456",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `UNAUTHORIZED` | 401 | Missing or invalid authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `ANALYSIS_FAILED` | 500 | Analysis processing error |
| `SERVICE_UNAVAILABLE` | 503 | Temporary service issue |
| `TIMEOUT` | 504 | Request timeout |

## Rate Limiting

Rate limits are enforced per user/API key:

| Tier | Requests/Hour | Concurrent Analyses | Max File Size |
|------|---------------|---------------------|---------------|
| Free | 100 | 1 | 10 MB |
| Pro | 1,000 | 5 | 50 MB |
| Team | 10,000 | 20 | 100 MB |
| Enterprise | Unlimited | Unlimited | 1 GB |

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 950
X-RateLimit-Reset: **********
```

## Request/Response Examples

### Example 1: Simple Analysis
```bash
curl -X POST https://analysis-engine.ccl.dev/api/v1/analyze \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "repository_url": "https://github.com/tokio-rs/tokio"
  }'
```

### Example 2: Filtered Analysis
```bash
curl -X POST https://analysis-engine.ccl.dev/api/v1/analyze \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "repository_url": "https://github.com/rust-lang/rust",
    "branch": "stable",
    "patterns": ["memory", "concurrency"],
    "languages": ["rust"],
    "options": {
      "max_file_size": 5242880,
      "timeout_seconds": 600
    }
  }'
```

### Example 3: Pagination
```bash
curl -G https://analysis-engine.ccl.dev/api/v1/analyses \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "page=2" \
  -d "limit=50" \
  -d "status=completed" \
  -d "created_after=2024-01-01T00:00:00Z"
```

## SDK Examples

### TypeScript/JavaScript
```typescript
import { AnalysisEngineClient } from '@ccl/analysis-engine-sdk';

const client = new AnalysisEngineClient({
  apiKey: process.env.CCL_API_KEY,
  baseUrl: 'https://analysis-engine.ccl.dev'
});

// Start analysis
const analysis = await client.analyze({
  repositoryUrl: 'https://github.com/vercel/next.js',
  patterns: ['react', 'api-routes'],
  languages: ['typescript', 'javascript']
});

// Track progress
analysis.onProgress((update) => {
  console.log(`Progress: ${update.percentage}% - ${update.message}`);
});

// Get results
const results = await analysis.waitForCompletion();
console.log(`Found ${results.patterns.length} patterns`);
```

### Python
```python
from ccl_analysis_engine import AnalysisEngineClient

client = AnalysisEngineClient(
    api_key=os.environ['CCL_API_KEY'],
    base_url='https://analysis-engine.ccl.dev'
)

# Start analysis
analysis = client.analyze(
    repository_url='https://github.com/django/django',
    patterns=['orm', 'authentication'],
    languages=['python']
)

# Track progress
for update in analysis.progress():
    print(f"Progress: {update.percentage}% - {update.message}")

# Get results
results = analysis.get_results()
print(f"Found {len(results.patterns)} patterns")
```

### Rust
```rust
use ccl_analysis_engine::{Client, AnalysisRequest};

let client = Client::new(
    std::env::var("CCL_API_KEY")?,
    "https://analysis-engine.ccl.dev"
)?;

// Start analysis
let request = AnalysisRequest::builder()
    .repository_url("https://github.com/rust-lang/cargo")
    .patterns(vec!["dependency", "build"])
    .languages(vec!["rust"])
    .build();

let analysis = client.analyze(request).await?;

// Track progress
let mut progress_stream = analysis.progress_stream();
while let Some(update) = progress_stream.next().await {
    println!("Progress: {}% - {}", update.percentage, update.message);
}

// Get results
let results = analysis.get_results().await?;
println!("Found {} patterns", results.patterns.len());
```

### Go
```go
package main

import (
    "github.com/ccl/analysis-engine-go"
)

func main() {
    client := analysisengine.NewClient(
        os.Getenv("CCL_API_KEY"),
        "https://analysis-engine.ccl.dev",
    )
    
    // Start analysis
    analysis, err := client.Analyze(context.Background(), &analysisengine.AnalysisRequest{
        RepositoryURL: "https://github.com/kubernetes/kubernetes",
        Patterns:      []string{"container", "orchestration"},
        Languages:     []string{"go"},
    })
    if err != nil {
        log.Fatal(err)
    }
    
    // Track progress
    for update := range analysis.Progress() {
        log.Printf("Progress: %.1f%% - %s", update.Percentage, update.Message)
    }
    
    // Get results
    results, err := analysis.GetResults()
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("Found %d patterns", len(results.Patterns))
}
```

## API Versioning

The API uses URL versioning. The current version is `v1`. When breaking changes are introduced, a new version will be created while maintaining the previous version for backward compatibility.

### Version Sunset Policy
- New versions are announced 6 months before release
- Previous versions are supported for 12 months after new version release
- Deprecation warnings are added to response headers

---

For more information about specific endpoints or integration help, contact the CCL API support team.